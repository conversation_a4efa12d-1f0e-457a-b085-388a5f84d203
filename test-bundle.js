/**
 * Test script to verify the JavaScript bundle has no syntax errors
 */

const http = require('http');

console.log('Testing JavaScript bundle for syntax errors...');

// Fetch the bundle
const req = http.get('http://localhost:8080/js/app.bundle.js', (res) => {
    let data = '';
    
    res.on('data', (chunk) => {
        data += chunk;
    });
    
    res.on('end', () => {
        console.log(`Bundle size: ${data.length} bytes`);
        
        try {
            // Try to parse the JavaScript (this will catch syntax errors)
            new Function(data);
            console.log('✅ Bundle has valid JavaScript syntax');
            
            // Check if our React code is present
            if (data.includes('Bridge Game')) {
                console.log('✅ React app code found in bundle');
            } else {
                console.log('❌ React app code not found in bundle');
            }
            
            // Check if React is included
            if (data.includes('react')) {
                console.log('✅ React library found in bundle');
            } else {
                console.log('❌ React library not found in bundle');
            }
            
        } catch (error) {
            console.log('❌ Bundle has JavaScript syntax errors:');
            console.error(error.message);
        }
    });
});

req.on('error', (error) => {
    console.error('❌ Failed to fetch bundle:', error.message);
});

req.setTimeout(5000, () => {
    console.error('❌ Request timeout');
    req.destroy();
});

{"name": "bridge-game", "version": "0.0.1", "description": "4-Hand Bridge Game with AI", "private": true, "keywords": ["bridge", "card-game", "ai"], "license": "MIT", "author": "", "scripts": {"test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:unit": "jest --testPathPattern=src/lib", "test:performance": "jest --testNamePattern=performance", "start": "webpack serve --open --config webpack.config.dev.js", "build": "webpack --config webpack.config.prod.js", "build:ts": "tsc"}, "devDependencies": {"@babel/core": "^7.27.4", "@babel/preset-react": "^7.27.1", "@babel/preset-typescript": "^7.27.1", "@svgr/webpack": "^8.1.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^29.5.14", "@types/node": "^24.0.1", "babel-loader": "^10.0.0", "buffer": "^6.0.3", "copy-webpack-plugin": "^11.0.0", "crypto-browserify": "^3.12.1", "css-loader": "^7.1.2", "fast-check": "^4.1.1", "html-webpack-plugin": "^5.6.0", "identity-obj-proxy": "^3.0.0", "jest": "^30.0.0", "jest-environment-jsdom": "^30.0.0", "process": "^0.11.10", "stream-browserify": "^3.0.0", "style-loader": "^4.0.0", "ts-jest": "^29.4.0", "ts-loader": "^9.5.2", "typescript": "^5.8.3", "util": "^0.12.5", "vm-browserify": "^1.1.2", "webpack": "^5.91.0", "webpack-cli": "^5.1.4", "webpack-dev-server": "^5.0.4", "webpack-merge": "^5.10.0"}, "dependencies": {"@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "react": "^19.1.0", "react-dom": "^19.1.0"}}
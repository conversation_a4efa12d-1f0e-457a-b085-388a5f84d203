<!DOCTYPE html>
<html>
<head>
    <title>React Test</title>
</head>
<body>
    <div id="root"></div>
    <script>
        console.log('HTML loaded');
        console.log('Testing basic JavaScript...');
        
        // Test if the root element exists
        const root = document.getElementById('root');
        if (root) {
            console.log('Root element found');
            root.innerHTML = '<h1>Basic HTML/JS Working</h1>';
        } else {
            console.error('Root element not found');
        }
        
        // Test if we can load the bundle
        console.log('About to load bundle...');
    </script>
    <script src="js/app.bundle.js"></script>
    <script>
        console.log('Bundle loaded (if this appears)');
    </script>
</body>
</html>

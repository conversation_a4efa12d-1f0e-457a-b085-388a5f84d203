/**
 * TypeScript type definitions for Bridge game entities
 * Covers all core bridge game concepts and data structures
 */

// ============================================================================
// CARD TYPES
// ============================================================================

export enum Suit {
  CLUBS = 'C',
  DIAMONDS = 'D',
  HEARTS = 'H',
  SPADES = 'S'
}

export enum Rank {
  TWO = '2',
  THREE = '3',
  FOUR = '4',
  FIVE = '5',
  SIX = '6',
  SEVEN = '7',
  EIGHT = '8',
  NINE = '9',
  TEN = 'T',
  JACK = 'J',
  QUEEN = 'Q',
  KING = 'K',
  ACE = 'A'
}

export interface Card {
  suit: Suit;
  rank: Rank;
}

export type Hand = Card[];
export type Deck = Card[];

// ============================================================================
// PLAYER TYPES
// ============================================================================

export enum Position {
  NORTH = 'N',
  EAST = 'E',
  SOUTH = 'S',
  WEST = 'W'
}

export enum PlayerType {
  HUMAN = 'human',
  AI = 'ai'
}

export interface Player {
  id: string;
  name: string;
  position: Position;
  type: PlayerType;
  hand: Hand;
  isConnected?: boolean;
}

// ============================================================================
// BIDDING TYPES
// ============================================================================

export enum BidLevel {
  ONE = 1,
  TWO = 2,
  THREE = 3,
  FOUR = 4,
  FIVE = 5,
  SIX = 6,
  SEVEN = 7
}

export enum BidSuit {
  CLUBS = 'C',
  DIAMONDS = 'D',
  HEARTS = 'H',
  SPADES = 'S',
  NO_TRUMP = 'NT'
}

export enum SpecialBid {
  PASS = 'PASS',
  DOUBLE = 'X',
  REDOUBLE = 'XX'
}

export type BidValue = {
  level: BidLevel;
  suit: BidSuit;
} | SpecialBid;

// Simple bid interface for WebSocket communication
export interface SimpleBid {
  level?: number;
  suit?: string | null;
  isPass?: boolean;
  isDouble?: boolean;
  isRedouble?: boolean;
}
export interface Bid {
  player: Position;
  value: BidValue;
  timestamp: Date;
  // Convenience properties for easier access
  level?: number;
  suit?: string;}

export interface Contract {
  level: BidLevel;
  suit: BidSuit;
  declarer: Position;
  doubled: 'none' | 'doubled' | 'redoubled';
}

export type Auction = Bid[];

// ============================================================================
// TRICK TYPES
// ============================================================================

export interface PlayedCard {
  card: Card;
  player: Position;
}

export interface Trick {
  id: number;
  cards: PlayedCard[];
  winner: Position;
  leadPlayer: Position;
}

// ============================================================================
// GAME STATE TYPES
// ============================================================================

export enum GamePhase {
  WAITING_FOR_PLAYERS = 'waiting_for_players',
  DEALING = 'dealing',
  BIDDING = 'bidding',
  PLAYING = 'playing',
  SCORING = 'scoring',
  FINISHED = 'finished',
  COMPLETE = 'complete'
}

export interface GameState {
  id: string;
  phase: GamePhase;
  dealer: Position;
  currentPlayer: Position;
  players: Record<Position, Player>;
  auction: Auction;
  contract: Contract | null;
  tricks: Trick[];
  currentTrick: PlayedCard[];
  dummy: Position | null;
  vulnerabilities: {
    northSouth: boolean;
    eastWest: boolean;
  };
  score: {
    northSouth: number;
    eastWest: number;
  };
  gameNumber: number;
  rubberScore: {
    northSouth: number;
    eastWest: number;
  };
}

// ============================================================================
// SCORING TYPES
// ============================================================================

export interface ScoreResult {
  contractPoints: number;
  overtricks: number;
  undertricks: number;
  bonuses: {
    game: number;
    slam: number;
    rubber: number;
    honors: number;
    insult: number; // for doubles/redoubles
  };
  penalties: number;
  totalScore: number;
}

export interface GameScore {
  northSouth: ScoreResult;
  eastWest: ScoreResult;
}

// ============================================================================
// VALIDATION TYPES
// ============================================================================

export interface ValidationResult {
  isValid: boolean;
  error?: string;
  details?: string;
}

// ============================================================================
// UTILITY TYPES
// ============================================================================

export type Partnership = 'northSouth' | 'eastWest';

export function getPartnership(position: Position): Partnership {
  return position === Position.NORTH || position === Position.SOUTH 
    ? 'northSouth' 
    : 'eastWest';
}

export function getPartner(position: Position): Position {
  switch (position) {
    case Position.NORTH: return Position.SOUTH;
    case Position.SOUTH: return Position.NORTH;
    case Position.EAST: return Position.WEST;
    case Position.WEST: return Position.EAST;
  }
}

export function getNextPosition(position: Position): Position {
  switch (position) {
    case Position.NORTH: return Position.EAST;
    case Position.EAST: return Position.SOUTH;
    case Position.SOUTH: return Position.WEST;
    case Position.WEST: return Position.NORTH;
  }
}

export function getPreviousPosition(position: Position): Position {
  switch (position) {
    case Position.NORTH: return Position.WEST;
    case Position.EAST: return Position.NORTH;
    case Position.SOUTH: return Position.EAST;
    case Position.WEST: return Position.SOUTH;
  }
}

// ============================================================================
// CONSTANTS
// ============================================================================

export const SUITS_IN_ORDER = [Suit.CLUBS, Suit.DIAMONDS, Suit.HEARTS, Suit.SPADES];
export const RANKS_IN_ORDER = [
  Rank.TWO, Rank.THREE, Rank.FOUR, Rank.FIVE, Rank.SIX, Rank.SEVEN,
  Rank.EIGHT, Rank.NINE, Rank.TEN, Rank.JACK, Rank.QUEEN, Rank.KING, Rank.ACE
];
export const POSITIONS_IN_ORDER = [Position.NORTH, Position.EAST, Position.SOUTH, Position.WEST];

export const CARDS_PER_HAND = 13;
export const TOTAL_CARDS = 52;
export const TOTAL_TRICKS = 13;

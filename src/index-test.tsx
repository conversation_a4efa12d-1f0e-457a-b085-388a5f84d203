/**
 * Test entry point to debug React mounting issues
 */

console.log('index-test.tsx loading...');

import React from 'react';
import { createRoot } from 'react-dom/client';

console.log('React imported successfully');
console.log('React version:', React.version);

const TestApp = () => {
  console.log('TestApp rendering...');
  return React.createElement('div', null, 
    React.createElement('h1', null, 'REACT TEST SUCCESS!'),
    React.createElement('p', null, 'React is mounting correctly')
  );
};

console.log('TestApp defined');

const container = document.getElementById('root');
console.log('Container:', container);

if (!container) {
  console.error('Root element not found');
  throw new Error('Root element not found');
}

console.log('About to create root...');
const root = createRoot(container);
console.log('Root created:', root);

console.log('About to render...');
root.render(React.createElement(TestApp));
console.log('Render called');

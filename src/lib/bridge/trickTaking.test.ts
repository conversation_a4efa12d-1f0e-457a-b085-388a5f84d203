/**
 * Tests for proper trick-taking logic implementation
 * Verifies Bridge rules for suit following, trump handling, and trick completion
 */

import {
  createNewGame,
  createPlayers,
  processBid,
  playCard,
  getValidCards
} from './gameStateUtils';

import {
  validateCardPlay,
  getTrickWinner
} from './trickUtils';

import {
  Position,
  GamePhase,
  BidLevel,
  BidSuit,
  SpecialBid,
  Suit,
  Rank,
  Card,
  Contract
} from '../../types/bridge';

describe('Trick-Taking Logic', () => {
  let gameState: any;

  beforeEach(() => {
    const players = createPlayers(Position.SOUTH);
    gameState = createNewGame('test-game', players, Position.NORTH);
    
    // Complete bidding to get to playing phase with 1♠ contract
    const bids = [
      { player: Position.EAST, value: { level: BidLevel.ONE, suit: BidSuit.SPADES }, timestamp: new Date() },
      { player: Position.SOUTH, value: SpecialBid.PASS, timestamp: new Date() },
      { player: Position.WEST, value: SpecialBid.PASS, timestamp: new Date() },
      { player: Position.NORTH, value: SpecialBid.PASS, timestamp: new Date() }
    ];
    
    for (const bid of bids) {
      gameState = processBid(gameState, bid);
    }
  });

  describe('Suit Following Rules', () => {
    it('should require following suit when possible', () => {
      // Set up a specific hand for testing
      const testHand = [
        { suit: Suit.HEARTS, rank: Rank.ACE },
        { suit: Suit.HEARTS, rank: Rank.KING },
        { suit: Suit.CLUBS, rank: Rank.QUEEN },
        { suit: Suit.DIAMONDS, rank: Rank.JACK }
      ];
      
      gameState.players[gameState.currentPlayer].hand = testHand;
      
      // Lead with a heart
      const leadCard = { suit: Suit.HEARTS, rank: Rank.TEN };
      gameState.currentTrick = [{ card: leadCard, player: Position.NORTH }];
      
      // Should only be able to play hearts
      const validCards = getValidCards(gameState);
      expect(validCards).toHaveLength(2);
      expect(validCards.every(card => card.suit === Suit.HEARTS)).toBe(true);
    });

    it('should allow any card when void in lead suit', () => {
      // Set up a hand with no hearts
      const testHand = [
        { suit: Suit.CLUBS, rank: Rank.ACE },
        { suit: Suit.CLUBS, rank: Rank.KING },
        { suit: Suit.DIAMONDS, rank: Rank.QUEEN },
        { suit: Suit.SPADES, rank: Rank.JACK }
      ];
      
      gameState.players[gameState.currentPlayer].hand = testHand;
      
      // Lead with a heart (player is void)
      const leadCard = { suit: Suit.HEARTS, rank: Rank.TEN };
      gameState.currentTrick = [{ card: leadCard, player: Position.NORTH }];
      
      // Should be able to play any card
      const validCards = getValidCards(gameState);
      expect(validCards).toHaveLength(4);
    });
  });

  describe('Trump Handling', () => {
    it('should correctly identify trump cards in spades contract', () => {
      const trick = [
        { card: { suit: Suit.HEARTS, rank: Rank.ACE }, player: Position.NORTH },
        { card: { suit: Suit.HEARTS, rank: Rank.KING }, player: Position.EAST },
        { card: { suit: Suit.SPADES, rank: Rank.TWO }, player: Position.SOUTH }, // Trump
        { card: { suit: Suit.HEARTS, rank: Rank.QUEEN }, player: Position.WEST }
      ];
      
      const winner = getTrickWinner(trick, gameState.contract);
      expect(winner).toBe(Position.SOUTH); // Trump wins
    });

    it('should handle no-trump contracts correctly', () => {
      // Change contract to no-trump
      gameState.contract = {
        level: BidLevel.THREE,
        suit: 'NT' as any,
        declarer: Position.EAST,
        doubled: 'none'
      };
      
      const trick = [
        { card: { suit: Suit.HEARTS, rank: Rank.KING }, player: Position.NORTH },
        { card: { suit: Suit.HEARTS, rank: Rank.ACE }, player: Position.EAST }, // Highest heart
        { card: { suit: Suit.SPADES, rank: Rank.ACE }, player: Position.SOUTH }, // Different suit
        { card: { suit: Suit.HEARTS, rank: Rank.QUEEN }, player: Position.WEST }
      ];
      
      const winner = getTrickWinner(trick, gameState.contract);
      expect(winner).toBe(Position.EAST); // Highest card of lead suit wins
    });
  });

  describe('Trick Completion', () => {
    it('should complete trick when 4 cards are played', () => {
      // Set up hands for all players
      const positions = [Position.NORTH, Position.EAST, Position.SOUTH, Position.WEST];
      positions.forEach(pos => {
        gameState.players[pos].hand = [
          { suit: Suit.HEARTS, rank: Rank.ACE },
          { suit: Suit.CLUBS, rank: Rank.KING }
        ];
      });
      
      let currentState = gameState;
      
      // Play 4 cards to complete a trick
      const cardsToPlay = [
        { suit: Suit.HEARTS, rank: Rank.ACE }, // North leads
        { suit: Suit.HEARTS, rank: Rank.ACE }, // East follows
        { suit: Suit.HEARTS, rank: Rank.ACE }, // South follows
        { suit: Suit.HEARTS, rank: Rank.ACE }  // West follows
      ];
      
      for (let i = 0; i < 4; i++) {
        const currentPlayer = currentState.currentPlayer;
        currentState = playCard(currentState, cardsToPlay[i], currentPlayer);
      }
      
      // Trick should be completed
      expect(currentState.tricks).toHaveLength(1);
      expect(currentState.currentTrick).toHaveLength(0);
      expect(currentState.tricks[0].cards).toHaveLength(4);
      expect(currentState.tricks[0].winner).toBeDefined();
    });

    it('should set trick winner as next player to lead', () => {
      // Set up a trick where South wins
      const positions = [Position.NORTH, Position.EAST, Position.SOUTH, Position.WEST];
      positions.forEach(pos => {
        gameState.players[pos].hand = [
          { suit: Suit.HEARTS, rank: pos === Position.SOUTH ? Rank.ACE : Rank.TWO }
        ];
      });
      
      let currentState = gameState;
      
      // Play 4 cards
      const cardsToPlay = [
        { suit: Suit.HEARTS, rank: Rank.TWO }, // North leads
        { suit: Suit.HEARTS, rank: Rank.TWO }, // East follows
        { suit: Suit.HEARTS, rank: Rank.ACE }, // South follows (wins)
        { suit: Suit.HEARTS, rank: Rank.TWO }  // West follows
      ];
      
      for (let i = 0; i < 4; i++) {
        const currentPlayer = currentState.currentPlayer;
        currentState = playCard(currentState, cardsToPlay[i], currentPlayer);
      }
      
      // South should be the current player (winner leads next)
      expect(currentState.currentPlayer).toBe(Position.SOUTH);
      expect(currentState.tricks[0].winner).toBe(Position.SOUTH);
    });
  });

  describe('Game Phase Transitions', () => {
    it('should move to scoring phase after 13 tricks', () => {
      // Simulate playing all 13 tricks
      let currentState = gameState;
      
      // Set up minimal hands for quick completion
      const positions = [Position.NORTH, Position.EAST, Position.SOUTH, Position.WEST];
      positions.forEach(pos => {
        const hand = [];
        for (let i = 0; i < 13; i++) {
          hand.push({ suit: Suit.HEARTS, rank: Rank.TWO });
        }
        currentState.players[pos].hand = hand;
      });
      
      // Play 13 complete tricks (52 cards total)
      for (let trick = 0; trick < 13; trick++) {
        for (let card = 0; card < 4; card++) {
          const cardToPlay = { suit: Suit.HEARTS, rank: Rank.TWO };
          const currentPlayer = currentState.currentPlayer;
          currentState = playCard(currentState, cardToPlay, currentPlayer);
        }
      }
      
      // Should be in scoring phase
      expect(currentState.phase).toBe(GamePhase.SCORING);
      expect(currentState.tricks).toHaveLength(13);
    });
  });

  describe('Card Play Validation', () => {
    it('should reject playing cards when not in playing phase', () => {
      // Set game to bidding phase
      gameState.phase = GamePhase.BIDDING;
      
      const card = { suit: Suit.HEARTS, rank: Rank.ACE };
      
      expect(() => {
        playCard(gameState, card, gameState.currentPlayer);
      }).toThrow('Cannot play cards outside of playing phase');
    });

    it('should reject playing cards without a contract', () => {
      gameState.contract = null;
      
      const card = { suit: Suit.HEARTS, rank: Rank.ACE };
      
      expect(() => {
        playCard(gameState, card, gameState.currentPlayer);
      }).toThrow('Cannot play cards without a contract');
    });
  });
});

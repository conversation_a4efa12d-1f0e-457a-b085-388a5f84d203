/**
 * Game state utility functions for managing bridge game state
 */

import {
  GameState,
  Player,
  Position,
  PlayerType,
  GamePhase,
  Card,
  Bid,
  Hand,
  Contract,
  PlayedCard
} from '../../types/bridge';
import { createDeck, shuffleDeck, dealCards } from './cardUtils';

/**
 * Gets the next player in clockwise order
 */
function getNextPlayer(position: Position): Position {
  const positions = [Position.NORTH, Position.EAST, Position.SOUTH, Position.WEST];
  const currentIndex = positions.indexOf(position);
  return positions[(currentIndex + 1) % 4];
}

/**
 * Creates a new game state
 */
export function createNewGame(
  gameId: string,
  players: Record<Position, Pick<Player, 'name' | 'type'>>,
  dealer: Position
): GameState {
  const deck = shuffleDeck(createDeck());
  const hands = dealCards(deck);
  
  const gameState: GameState = {
    id: gameId,
    phase: GamePhase.BIDDING,
    dealer,
    currentPlayer: getNextP<PERSON>(dealer), // FIX: Next player after dealer starts bidding
    hands: {
      [Position.NORTH]: hands[0],
      [Position.EAST]: hands[1],
      [Position.SOUTH]: hands[2],
      [Position.WEST]: hands[3]
    },
    players: {
      [Position.NORTH]: {
        id: 'north',
        name: players[Position.NORTH].name,
        position: Position.NORTH,
        type: players[Position.NORTH].type,
        hand: hands[0]
      },
      [Position.SOUTH]: {
        id: 'south',
        name: players[Position.SOUTH].name,
        position: Position.SOUTH,
        type: players[Position.SOUTH].type,
        hand: hands[1]
      },
      [Position.EAST]: {
        id: 'east',
        name: players[Position.EAST].name,
        position: Position.EAST,
        type: players[Position.EAST].type,
        hand: hands[2]
      },
      [Position.WEST]: {
        id: 'west',
        name: players[Position.WEST].name,
        position: Position.WEST,
        type: players[Position.WEST].type,
        hand: hands[3]
      }
    },
    auction: [],
    contract: null,
    tricks: [],
    currentTrick: [],
    dummy: null,
    vulnerabilities: { northSouth: false, eastWest: false },
    score: { northSouth: 0, eastWest: 0 },
    gameNumber: 1,
    rubberScore: { northSouth: 0, eastWest: 0 }
  };
  
  return gameState;
}

/**
 * Creates player objects for a new game
 */
export function createPlayers(
  humanPosition: Position,
  humanName: string
): Record<Position, Pick<Player, 'name' | 'type'>> {
  const players: Record<Position, Pick<Player, 'name' | 'type'>> = {
    [Position.NORTH]: { name: 'AI North', type: PlayerType.AI },
    [Position.SOUTH]: { name: 'AI South', type: PlayerType.AI },
    [Position.EAST]: { name: 'AI East', type: PlayerType.AI },
    [Position.WEST]: { name: 'AI West', type: PlayerType.AI }
  };
  
  players[humanPosition] = { name: humanName, type: PlayerType.HUMAN };
  
  return players;
}

/**
 * Determines if a player can see another player's cards
 */
export function canSeeCards(
  gameState: GameState,
  viewerPosition: Position,
  targetPosition: Position
): boolean {
  // Can always see your own cards
  if (viewerPosition === targetPosition) {
    return true;
  }
  
  // Can see dummy's cards if you're the declarer and dummy is revealed
  if (gameState.dummy === targetPosition && 
      gameState.contract?.declarer === viewerPosition) {
    return true;
  }
  
  // Otherwise can't see other players' cards
  return false;
}

/**
 * Gets the current player's hand
 */
export function getCurrentPlayerHand(gameState: GameState): Card[] {
  return gameState.players[gameState.currentPlayer].hand;
}

/**
 * Gets the dummy's hand (if dummy is revealed)
 */
export function getDummyHand(gameState: GameState): Card[] | null {
  if (!gameState.dummy) {
    return null;
  }
  return gameState.players[gameState.dummy].hand;
}

/**
 * Checks if the game is complete
 */
export function isGameComplete(gameState: GameState): boolean {
  return gameState.phase === GamePhase.COMPLETE;
}

/**
 * Validates the game state
 */
export function validateGameState(gameState: GameState): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  // Check that all players have the correct number of cards
  const totalCards = Object.values(gameState.players).reduce((sum, player) => sum + player.hand.length, 0);
  if (totalCards !== 52) {
    errors.push(`Invalid total cards: ${totalCards}, expected 52`);
  }
  
  // Check that each player has 13 cards (at start of game)
  if (gameState.phase === GamePhase.BIDDING) {
    for (const [position, player] of Object.entries(gameState.players)) {
      if (player.hand.length !== 13) {
        errors.push(`Player ${position} has ${player.hand.length} cards, expected 13`);
      }
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Gets valid cards that can be played in the current situation
 */
export function getValidCards(gameState: GameState): Card[] {
  if (gameState.phase !== GamePhase.PLAYING) {
    return [];
  }
  
  const currentPlayer = gameState.players[gameState.currentPlayer];
  const hand = currentPlayer.hand;
  
  // If no cards played yet in trick, any card is valid
  if (gameState.currentTrick.length === 0) {
    return [...hand];
  }
  
  // Must follow suit if possible
  const leadSuit = gameState.currentTrick[0].card.suit;
  const suitCards = hand.filter(card => card.suit === leadSuit);
  
  if (suitCards.length > 0) {
    return suitCards;
  }
  
  // Can't follow suit, any card is valid
  return [...hand];
}

/**
 * Processes a card play
 */
export function playCard(
  gameState: GameState,
  card: Card,
  player: Position
): GameState {
  // This is a simplified implementation
  // In a real game, you'd validate the play and update the game state accordingly
  const newGameState = { ...gameState };
  
  // Remove card from player's hand
  const playerHand = newGameState.players[player].hand;
  const cardIndex = playerHand.findIndex(c => c.rank === card.rank && c.suit === card.suit);
  if (cardIndex >= 0) {
    playerHand.splice(cardIndex, 1);
  }
  
  // Add to current trick
  newGameState.currentTrick = [
    ...newGameState.currentTrick,
    { card, player }
  ];
  
  // Move to next player (simplified)
  newGameState.currentPlayer = getNextPlayer(gameState.currentPlayer);
  
  return newGameState;
}

/**
 * Processes a bid with proper validation
 */
export function processBid(gameState: GameState, bid: Bid): GameState {
  // Validate it's the correct player's turn
  if (bid.player !== gameState.currentPlayer) {
    throw new Error(`It's not ${bid.player}'s turn to bid`);
  }
  
  // Validate the bid is legal (simplified validation)
  if (bid.level < 1 || bid.level > 7) {
    throw new Error('Invalid bid');
  }
  
  const newGameState = { ...gameState };
  
  // Add bid to auction
  newGameState.auction = [...newGameState.auction, bid];
  
  // Move to next player
  newGameState.currentPlayer = getNextPlayer(gameState.currentPlayer);
  
  return newGameState;
}

/**
 * Completes scoring for the game
 */
export function completeScoring(gameState: GameState): GameState {
  // This is a placeholder implementation
  const newGameState = { ...gameState };
  newGameState.phase = GamePhase.COMPLETE;
  return newGameState;
}

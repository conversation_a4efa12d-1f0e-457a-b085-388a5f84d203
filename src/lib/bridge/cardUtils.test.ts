/**
 * Unit tests for cardUtils module
 * Tests deck creation, shuffling, dealing, and card operations
 */

import * as fc from 'fast-check';
import {
  createDeck,
  shuffleDeck,
  dealCards,
  shuffleAndDeal,
  cardToString,
  stringToCard,
  cardsEqual,
  getRankValue,
  compareCardsByRank,
  sortHand,
  getCardsOfSuit,
  handContainsCard,
  removeCardFromHand,
  getHighestCardOfSuit,
  getLowestCardOfSuit,
  validateDeck,
  validateDealtHands
} from './cardUtils';

import {
  Card,
  Suit,
  Rank,
  Position,
  TOTAL_CARDS,
  CARDS_PER_HAND,
  SUITS_IN_ORDER,
  RANKS_IN_ORDER
} from '../../types/bridge';

describe('cardUtils', () => {
  describe('createDeck', () => {
    it('should create a deck with 52 cards', () => {
      const deck = createDeck();
      expect(deck).toHaveLength(TOTAL_CARDS);
    });

    it('should contain exactly one of each card', () => {
      const deck = createDeck();
      const cardStrings = deck.map(cardToString);
      const uniqueCards = new Set(cardStrings);
      expect(uniqueCards.size).toBe(TOTAL_CARDS);
    });

    it('should contain all suits and ranks', () => {
      const deck = createDeck();
      
      for (const suit of SUITS_IN_ORDER) {
        for (const rank of RANKS_IN_ORDER) {
          const expectedCard = { suit, rank };
          expect(deck.some(card => cardsEqual(card, expectedCard))).toBe(true);
        }
      }
    });

    it('should validate as a proper deck', () => {
      const deck = createDeck();
      const validation = validateDeck(deck);
      expect(validation.isValid).toBe(true);
    });
  });

  describe('shuffleDeck', () => {
    it('should return a deck with the same cards', () => {
      const originalDeck = createDeck();
      const shuffled = shuffleDeck(originalDeck);
      
      expect(shuffled).toHaveLength(TOTAL_CARDS);
      
      // Check that all original cards are still present
      for (const card of originalDeck) {
        expect(shuffled.some(shuffledCard => cardsEqual(shuffledCard, card))).toBe(true);
      }
    });

    it('should not modify the original deck', () => {
      const originalDeck = createDeck();
      const originalFirst = originalDeck[0];
      shuffleDeck(originalDeck);
      expect(originalDeck[0]).toEqual(originalFirst);
    });

    it('should produce different orders with different random functions', () => {
      const deck = createDeck();
      
      // Use deterministic random functions
      const shuffled1 = shuffleDeck(deck, () => 0.1);
      const shuffled2 = shuffleDeck(deck, () => 0.9);
      
      // They should be different (very high probability)
      expect(shuffled1).not.toEqual(shuffled2);
    });

    it('should be deterministic with same random function', () => {
      const deck = createDeck();
      const random = () => 0.5;
      
      const shuffled1 = shuffleDeck(deck, random);
      const shuffled2 = shuffleDeck(deck, random);
      
      expect(shuffled1).toEqual(shuffled2);
    });
  });

  describe('dealCards', () => {
    it('should deal 13 cards to each player', () => {
      const deck = createDeck();
      const hands = dealCards(deck, Position.NORTH);
      
      for (const position of [Position.NORTH, Position.EAST, Position.SOUTH, Position.WEST]) {
        expect(hands[position]).toHaveLength(CARDS_PER_HAND);
      }
    });

    it('should deal all cards from the deck', () => {
      const deck = createDeck();
      const hands = dealCards(deck, Position.NORTH);
      
      const allDealtCards: Card[] = [];
      for (const position of [Position.NORTH, Position.EAST, Position.SOUTH, Position.WEST]) {
        allDealtCards.push(...hands[position]);
      }
      
      expect(allDealtCards).toHaveLength(TOTAL_CARDS);
    });

    it('should deal unique cards to each player', () => {
      const deck = createDeck();
      const hands = dealCards(deck, Position.NORTH);
      
      const validation = validateDealtHands(hands);
      expect(validation.isValid).toBe(true);
    });

    it('should start dealing to the position after the dealer', () => {
      // Create a known deck order
      const deck = createDeck();
      const hands = dealCards(deck, Position.NORTH);
      
      // First card should go to East (next after North)
      expect(hands[Position.EAST]).toContain(deck[0]);
      expect(hands[Position.SOUTH]).toContain(deck[1]);
      expect(hands[Position.WEST]).toContain(deck[2]);
      expect(hands[Position.NORTH]).toContain(deck[3]);
    });

    it('should throw error for invalid deck size', () => {
      const invalidDeck = createDeck().slice(0, 51); // Remove one card
      expect(() => dealCards(invalidDeck, Position.NORTH)).toThrow('Invalid deck size');
    });
  });

  describe('cardToString and stringToCard', () => {
    it('should convert cards to strings correctly', () => {
      const card: Card = { rank: Rank.ACE, suit: Suit.SPADES };
      expect(cardToString(card)).toBe('AS');
    });

    it('should parse strings to cards correctly', () => {
      const card = stringToCard('AS');
      expect(card).toEqual({ rank: Rank.ACE, suit: Suit.SPADES });
    });

    it('should be reversible', () => {
      const originalCard: Card = { rank: Rank.KING, suit: Suit.HEARTS };
      const cardString = cardToString(originalCard);
      const parsedCard = stringToCard(cardString);
      expect(parsedCard).toEqual(originalCard);
    });

    it('should handle all valid cards', () => {
      for (const suit of SUITS_IN_ORDER) {
        for (const rank of RANKS_IN_ORDER) {
          const card: Card = { rank, suit };
          const cardString = cardToString(card);
          const parsedCard = stringToCard(cardString);
          expect(parsedCard).toEqual(card);
        }
      }
    });

    it('should throw error for invalid card strings', () => {
      expect(() => stringToCard('XX')).toThrow('Invalid rank');
      expect(() => stringToCard('AZ')).toThrow('Invalid suit');
      expect(() => stringToCard('A')).toThrow('Invalid card string');
      expect(() => stringToCard('ASS')).toThrow('Invalid card string');
    });
  });

  describe('getRankValue', () => {
    it('should return correct numeric values', () => {
      expect(getRankValue(Rank.TWO)).toBe(2);
      expect(getRankValue(Rank.TEN)).toBe(10);
      expect(getRankValue(Rank.JACK)).toBe(11);
      expect(getRankValue(Rank.QUEEN)).toBe(12);
      expect(getRankValue(Rank.KING)).toBe(13);
      expect(getRankValue(Rank.ACE)).toBe(14);
    });

    it('should maintain rank order', () => {
      for (let i = 0; i < RANKS_IN_ORDER.length - 1; i++) {
        const lowerRank = RANKS_IN_ORDER[i];
        const higherRank = RANKS_IN_ORDER[i + 1];
        expect(getRankValue(lowerRank)).toBeLessThan(getRankValue(higherRank));
      }
    });
  });

  describe('hand operations', () => {
    const testHand: Card[] = [
      { rank: Rank.ACE, suit: Suit.SPADES },
      { rank: Rank.KING, suit: Suit.HEARTS },
      { rank: Rank.TWO, suit: Suit.CLUBS },
      { rank: Rank.QUEEN, suit: Suit.SPADES }
    ];

    describe('getCardsOfSuit', () => {
      it('should return cards of specified suit', () => {
        const spades = getCardsOfSuit(testHand, Suit.SPADES);
        expect(spades).toHaveLength(2);
        expect(spades.every(card => card.suit === Suit.SPADES)).toBe(true);
      });

      it('should return empty array for suit not in hand', () => {
        const diamonds = getCardsOfSuit(testHand, Suit.DIAMONDS);
        expect(diamonds).toHaveLength(0);
      });
    });

    describe('handContainsCard', () => {
      it('should return true for cards in hand', () => {
        const aceOfSpades = { rank: Rank.ACE, suit: Suit.SPADES };
        expect(handContainsCard(testHand, aceOfSpades)).toBe(true);
      });

      it('should return false for cards not in hand', () => {
        const aceOfDiamonds = { rank: Rank.ACE, suit: Suit.DIAMONDS };
        expect(handContainsCard(testHand, aceOfDiamonds)).toBe(false);
      });
    });

    describe('removeCardFromHand', () => {
      it('should remove specified card', () => {
        const cardToRemove = { rank: Rank.ACE, suit: Suit.SPADES };
        const newHand = removeCardFromHand(testHand, cardToRemove);
        
        expect(newHand).toHaveLength(testHand.length - 1);
        expect(handContainsCard(newHand, cardToRemove)).toBe(false);
      });

      it('should not modify original hand', () => {
        const originalLength = testHand.length;
        const cardToRemove = { rank: Rank.ACE, suit: Suit.SPADES };
        removeCardFromHand(testHand, cardToRemove);
        
        expect(testHand).toHaveLength(originalLength);
      });

      it('should throw error for card not in hand', () => {
        const cardNotInHand = { rank: Rank.ACE, suit: Suit.DIAMONDS };
        expect(() => removeCardFromHand(testHand, cardNotInHand)).toThrow('not found in hand');
      });
    });
  });

  // Property-based tests using fast-check
  describe('property-based tests', () => {
    it('shuffling preserves all cards', () => {
      fc.assert(fc.property(
        fc.float({ min: 0, max: 1 }), // seed for deterministic random
        (seed) => {
          const deck = createDeck();
          const random = () => seed;
          const shuffled = shuffleDeck(deck, random);
          
          // All original cards should be present
          for (const card of deck) {
            expect(shuffled.some(shuffledCard => cardsEqual(shuffledCard, card))).toBe(true);
          }
          
          expect(shuffled).toHaveLength(deck.length);
        }
      ));
    });

    it('dealing preserves all cards', () => {
      fc.assert(fc.property(
        fc.constantFrom(...Object.values(Position)),
        (dealer) => {
          const deck = createDeck();
          const hands = dealCards(deck, dealer);
          
          const allDealtCards: Card[] = [];
          Object.values(hands).forEach(hand => allDealtCards.push(...hand));
          
          expect(allDealtCards).toHaveLength(TOTAL_CARDS);
          
          // All original cards should be dealt
          for (const card of deck) {
            expect(allDealtCards.some(dealtCard => cardsEqual(dealtCard, card))).toBe(true);
          }
        }
      ));
    });
  });

  // Performance tests
  describe('performance tests', () => {
    it('should create and shuffle deck quickly', () => {
      const start = Date.now();
      
      for (let i = 0; i < 1000; i++) {
        const deck = createDeck();
        shuffleDeck(deck);
      }
      
      const elapsed = Date.now() - start;
      expect(elapsed).toBeLessThan(1000); // Should complete in under 1 second
    });

    it('should deal cards quickly', () => {
      const deck = createDeck();
      const start = Date.now();
      
      for (let i = 0; i < 1000; i++) {
        dealCards(deck, Position.NORTH);
      }
      
      const elapsed = Date.now() - start;
      expect(elapsed).toBeLessThan(100); // Should complete in under 100ms
    });
  });
});

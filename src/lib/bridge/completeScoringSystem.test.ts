/**
 * Tests for complete Bridge scoring system
 * Verifies contract points, overtricks, undertricks, bonuses, and penalties
 */

import {
  createNewGame,
  createPlayers,
  processBid,
  playCard,
  processScoring,
  getDetailedScoring
} from './gameStateUtils';

import {
  calculateContractScore,
  calculateDetailedScore,
  formatScore,
  isGameWon,
  calculateRubberBonus
} from './scoringUtils';

import {
  Position,
  GamePhase,
  BidLevel,
  BidSuit,
  SpecialBid,
  Suit,
  Rank,
  Contract
} from '../../types/bridge';

describe('Complete Scoring System', () => {
  let gameState: any;

  beforeEach(() => {
    const players = createPlayers(Position.SOUTH);
    gameState = createNewGame('test-game', players, Position.NORTH);
  });

  describe('Basic Contract Scoring', () => {
    it('should score minor suit contracts correctly', () => {
      const contract: Contract = {
        level: BidLevel.FIVE,
        suit: BidSuit.CLUBS,
        declarer: Position.SOUTH,
        doubled: 'none'
      };

      // Made exactly (11 tricks)
      const score = calculateContractScore(contract, 11, false);
      expect(score).toBe(400); // 100 basic + 300 game bonus

      // Made with overtrick (12 tricks)
      const scoreWithOvertrick = calculateContractScore(contract, 12, false);
      expect(scoreWithOvertrick).toBe(420); // 100 basic + 300 game + 20 overtrick
    });

    it('should score major suit contracts correctly', () => {
      const contract: Contract = {
        level: BidLevel.FOUR,
        suit: BidSuit.SPADES,
        declarer: Position.SOUTH,
        doubled: 'none'
      };

      // Made exactly (10 tricks)
      const score = calculateContractScore(contract, 10, false);
      expect(score).toBe(420); // 120 basic + 300 game bonus
    });

    it('should score no-trump contracts correctly', () => {
      const contract: Contract = {
        level: BidLevel.THREE,
        suit: BidSuit.NO_TRUMP,
        declarer: Position.SOUTH,
        doubled: 'none'
      };

      // Made exactly (9 tricks)
      const score = calculateContractScore(contract, 9, false);
      expect(score).toBe(400); // 100 basic (40+30+30) + 300 game bonus
    });
  });

  describe('Vulnerability Effects', () => {
    it('should apply vulnerability bonuses correctly', () => {
      const contract: Contract = {
        level: BidLevel.THREE,
        suit: BidSuit.NO_TRUMP,
        declarer: Position.SOUTH,
        doubled: 'none'
      };

      // Not vulnerable
      const scoreNotVul = calculateContractScore(contract, 9, false);
      expect(scoreNotVul).toBe(400); // 100 + 300 game bonus

      // Vulnerable
      const scoreVul = calculateContractScore(contract, 9, true);
      expect(scoreVul).toBe(600); // 100 + 500 game bonus
    });

    it('should apply vulnerability to penalties correctly', () => {
      const contract: Contract = {
        level: BidLevel.THREE,
        suit: BidSuit.NO_TRUMP,
        declarer: Position.SOUTH,
        doubled: 'none'
      };

      // Down 1, not vulnerable
      const penaltyNotVul = calculateContractScore(contract, 8, false);
      expect(penaltyNotVul).toBe(-50);

      // Down 1, vulnerable
      const penaltyVul = calculateContractScore(contract, 8, true);
      expect(penaltyVul).toBe(-100);
    });
  });

  describe('Doubled Contracts', () => {
    it('should score doubled contracts correctly', () => {
      const contract: Contract = {
        level: BidLevel.ONE,
        suit: BidSuit.CLUBS,
        declarer: Position.SOUTH,
        doubled: 'doubled'
      };

      // Made exactly
      const score = calculateContractScore(contract, 7, false);
      expect(score).toBe(90); // 40 basic (20*2) + 50 part game + 50 double bonus

      // Failed
      const penalty = calculateContractScore(contract, 6, false);
      expect(penalty).toBe(-100); // Doubled penalty
    });

    it('should score redoubled contracts correctly', () => {
      const contract: Contract = {
        level: BidLevel.ONE,
        suit: BidSuit.CLUBS,
        declarer: Position.SOUTH,
        doubled: 'redoubled'
      };

      // Made exactly
      const score = calculateContractScore(contract, 7, false);
      expect(score).toBe(130); // 80 basic (20*4) + 50 part game + 100 redouble bonus
    });
  });

  describe('Slam Bonuses', () => {
    it('should award small slam bonuses', () => {
      const contract: Contract = {
        level: BidLevel.SIX,
        suit: BidSuit.SPADES,
        declarer: Position.SOUTH,
        doubled: 'none'
      };

      // Not vulnerable
      const scoreNotVul = calculateContractScore(contract, 12, false);
      expect(scoreNotVul).toBe(1430); // 180 basic + 500 game + 500 small slam + 250 for making

      // Vulnerable
      const scoreVul = calculateContractScore(contract, 12, true);
      expect(scoreVul).toBe(1930); // 180 basic + 500 game + 750 small slam + 500 for making
    });

    it('should award grand slam bonuses', () => {
      const contract: Contract = {
        level: BidLevel.SEVEN,
        suit: BidSuit.NO_TRUMP,
        declarer: Position.SOUTH,
        doubled: 'none'
      };

      // Not vulnerable
      const scoreNotVul = calculateContractScore(contract, 13, false);
      expect(scoreNotVul).toBe(2220); // 220 basic + 500 game + 1000 grand slam + 500 for making

      // Vulnerable
      const scoreVul = calculateContractScore(contract, 13, true);
      expect(scoreVul).toBe(2720); // 220 basic + 500 game + 1500 grand slam + 720 for making
    });
  });

  describe('Detailed Score Breakdown', () => {
    it('should provide detailed score breakdown for made contracts', () => {
      const contract: Contract = {
        level: BidLevel.THREE,
        suit: BidSuit.NO_TRUMP,
        declarer: Position.SOUTH,
        doubled: 'none'
      };

      const detailedScore = calculateDetailedScore(contract, 10, false); // Made with 1 overtrick

      expect(detailedScore.northSouth.contractPoints).toBe(120); // 100 basic + 20 overtrick
      expect(detailedScore.northSouth.bonuses.game).toBe(300);
      expect(detailedScore.northSouth.bonuses.slam).toBe(0);
      expect(detailedScore.northSouth.bonuses.insult).toBe(0);
      expect(detailedScore.northSouth.totalScore).toBe(420);
      expect(detailedScore.northSouth.overtricks).toBe(1);
      expect(detailedScore.northSouth.undertricks).toBe(0);

      expect(detailedScore.eastWest.totalScore).toBe(0);
      expect(detailedScore.eastWest.penalties).toBe(0);
    });

    it('should provide detailed score breakdown for failed contracts', () => {
      const contract: Contract = {
        level: BidLevel.THREE,
        suit: BidSuit.NO_TRUMP,
        declarer: Position.SOUTH,
        doubled: 'none'
      };

      const detailedScore = calculateDetailedScore(contract, 7, false); // Down 2

      expect(detailedScore.northSouth.totalScore).toBe(0);
      expect(detailedScore.northSouth.contractPoints).toBe(0);
      expect(detailedScore.northSouth.undertricks).toBe(2);

      expect(detailedScore.eastWest.penalties).toBe(100); // 2 * 50
      expect(detailedScore.eastWest.totalScore).toBe(100);
    });
  });

  describe('Game Integration', () => {
    it('should integrate scoring with complete game flow', async () => {
      // Complete bidding
      const bids = [
        { player: Position.EAST, value: { level: BidLevel.THREE, suit: BidSuit.NO_TRUMP }, timestamp: new Date() },
        { player: Position.SOUTH, value: SpecialBid.PASS, timestamp: new Date() },
        { player: Position.WEST, value: SpecialBid.PASS, timestamp: new Date() },
        { player: Position.NORTH, value: SpecialBid.PASS, timestamp: new Date() }
      ];

      let currentState = gameState;
      for (const bid of bids) {
        currentState = processBid(currentState, bid);
      }

      expect(currentState.phase).toBe(GamePhase.PLAYING);
      expect(currentState.contract!.level).toBe(BidLevel.THREE);
      expect(currentState.contract!.suit).toBe(BidSuit.NO_TRUMP);

      // Set up hands for controlled play
      const positions = [Position.SOUTH, Position.WEST, Position.NORTH, Position.EAST];
      positions.forEach(pos => {
        const hand = [];
        for (let i = 0; i < 13; i++) {
          hand.push({ suit: Suit.HEARTS, rank: Rank.TWO });
        }
        currentState.players[pos].hand = hand;
      });

      // Play all 13 tricks (declarer makes exactly 9)
      for (let trick = 0; trick < 13; trick++) {
        for (let card = 0; card < 4; card++) {
          const cardToPlay = { suit: Suit.HEARTS, rank: Rank.TWO };
          const currentPlayer = currentState.currentPlayer;
          currentState = playCard(currentState, cardToPlay, currentPlayer);
        }
      }

      expect(currentState.phase).toBe(GamePhase.SCORING);

      // Process scoring
      const scoredState = processScoring(currentState);
      expect(scoredState.phase).toBe(GamePhase.COMPLETE);

      // Check that scores were applied (East-West made 3NT)
      expect(scoredState.score.eastWest).toBeGreaterThan(0);
      expect(scoredState.score.northSouth).toBe(0);
    });

    it('should provide detailed scoring information', () => {
      // Set up a completed game
      gameState.phase = GamePhase.SCORING;
      gameState.contract = {
        level: BidLevel.ONE,
        suit: BidSuit.SPADES,
        declarer: Position.SOUTH,
        doubled: 'none'
      };

      // Create 13 tricks where South wins 8 (made with 1 overtrick)
      gameState.tricks = [];
      for (let i = 0; i < 13; i++) {
        gameState.tricks.push({
          id: i + 1,
          cards: [
            { card: { suit: Suit.HEARTS, rank: Rank.ACE }, player: Position.NORTH },
            { card: { suit: Suit.HEARTS, rank: Rank.KING }, player: Position.EAST },
            { card: { suit: Suit.HEARTS, rank: Rank.QUEEN }, player: Position.SOUTH },
            { card: { suit: Suit.HEARTS, rank: Rank.JACK }, player: Position.WEST }
          ],
          winner: i < 8 ? Position.SOUTH : Position.NORTH, // South wins first 8 tricks
          leadPlayer: Position.NORTH
        });
      }

      const detailedScore = getDetailedScoring(gameState);
      expect(detailedScore).not.toBeNull();
      expect(detailedScore.northSouth.contractPoints).toBe(50); // 30 basic + 20 overtrick
      expect(detailedScore.northSouth.bonuses.game).toBe(50); // Part game
      expect(detailedScore.northSouth.totalScore).toBe(100);
    });
  });

  describe('Utility Functions', () => {
    it('should format scores correctly', () => {
      expect(formatScore(0)).toBe('0');
      expect(formatScore(420)).toBe('+420');
      expect(formatScore(-100)).toBe('-100');
    });

    it('should identify game contracts', () => {
      expect(isGameWon(100)).toBe(true);
      expect(isGameWon(99)).toBe(false);
      expect(isGameWon(420)).toBe(true);
    });

    it('should calculate rubber bonuses', () => {
      expect(calculateRubberBonus(2, 0)).toBe(700); // Won 2-0
      expect(calculateRubberBonus(2, 1)).toBe(500); // Won 2-1
      expect(calculateRubberBonus(1, 1)).toBe(0);   // Not won yet
    });
  });
});

/**
 * Bidding validation and utilities for Bridge game
 * Handles bid validation, auction logic, and contract determination
 */

import {
  Bid,
  BidValue,
  BidLevel,
  BidSuit,
  SpecialBid,
  Contract,
  Auction,
  Position,
  ValidationResult,
  getNextPosition
} from '../../types/bridge';

// ============================================================================
// BID COMPARISON AND VALIDATION
// ============================================================================

/**
 * Gets the numeric value of a bid suit for comparison
 * Clubs=1, Diamonds=2, Hearts=3, Spades=4, No Trump=5
 */
export function getBidSuitValue(suit: BidSuit): number {
  switch (suit) {
    case BidSuit.CLUBS: return 1;
    case BidSuit.DIAMONDS: return 2;
    case BidSuit.HEARTS: return 3;
    case BidSuit.SPADES: return 4;
    case BidSuit.NO_TRUMP: return 5;
  }
}

/**
 * Gets the total value of a bid for comparison
 * Level 1 Clubs = 6, Level 1 Diamonds = 7, ..., Level 7 No Trump = 40
 */
export function getBidValue(bid: BidValue): number {
  if (typeof bid === 'string') {
    // Special bids (PASS, DOUBLE, REDOUBLE) don't have numeric values
    return 0;
  }
  
  return (bid.level - 1) * 5 + getBidSuitValue(bid.suit);
}

/**
 * Compares two bids to determine which is higher
 * Returns positive if bid1 > bid2, negative if bid1 < bid2, 0 if equal
 */
export function compareBids(bid1: BidValue, bid2: BidValue): number {
  return getBidValue(bid1) - getBidValue(bid2);
}

/**
 * Checks if a bid is higher than another bid
 */
export function isBidHigher(newBid: BidValue, currentBid: BidValue): boolean {
  return compareBids(newBid, currentBid) > 0;
}

/**
 * Validates that a bid is legal given the current auction state
 */
export function validateBid(
  bid: BidValue,
  auction: Auction,
  currentPlayer: Position
): ValidationResult {
  // Get the last non-pass bid to determine minimum bid level
  const lastContractBid = getLastContractBid(auction);
  
  if (typeof bid === 'string') {
    // Handle special bids
    switch (bid) {
      case SpecialBid.PASS:
        return { isValid: true };
        
      case SpecialBid.DOUBLE:
        return validateDouble(auction, currentPlayer);
        
      case SpecialBid.REDOUBLE:
        return validateRedouble(auction, currentPlayer);
        
      default:
        return {
          isValid: false,
          error: 'Invalid special bid',
          details: `Unknown special bid: ${bid}`
        };
    }
  }
  
  // Validate contract bid
  if (bid.level < BidLevel.ONE || bid.level > BidLevel.SEVEN) {
    return {
      isValid: false,
      error: 'Invalid bid level',
      details: `Bid level must be between 1 and 7, got ${bid.level}`
    };
  }
  
  // Check if bid is higher than the last contract bid
  if (lastContractBid && !isBidHigher(bid, lastContractBid.value)) {
    return {
      isValid: false,
      error: 'Bid too low',
      details: `Bid must be higher than ${formatBid(lastContractBid.value)}`
    };
  }
  
  return { isValid: true };
}

/**
 * Validates a double bid
 */
function validateDouble(auction: Auction, currentPlayer: Position): ValidationResult {
  if (auction.length === 0) {
    return {
      isValid: false,
      error: 'Cannot double without a bid',
      details: 'There must be a contract bid to double'
    };
  }
  
  const lastBid = auction[auction.length - 1];
  
  // Can only double contract bids, not special bids
  if (typeof lastBid.value === 'string') {
    return {
      isValid: false,
      error: 'Cannot double special bid',
      details: 'Can only double contract bids'
    };
  }
  
  // Cannot double your own partnership's bid
  if (isPartnership(currentPlayer, lastBid.player)) {
    return {
      isValid: false,
      error: 'Cannot double own partnership',
      details: 'Cannot double your own or partner\'s bid'
    };
  }
  
  // Check if bid is already doubled
  const lastContractBid = getLastContractBid(auction);
  if (lastContractBid && isLastBidDoubled(auction)) {
    return {
      isValid: false,
      error: 'Bid already doubled',
      details: 'This bid has already been doubled'
    };
  }
  
  return { isValid: true };
}

/**
 * Validates a redouble bid
 */
function validateRedouble(auction: Auction, currentPlayer: Position): ValidationResult {
  if (auction.length === 0) {
    return {
      isValid: false,
      error: 'Cannot redouble without a bid',
      details: 'There must be a doubled bid to redouble'
    };
  }
  
  // Check if the last bid was a double
  const lastBid = auction[auction.length - 1];
  if (lastBid.value !== SpecialBid.DOUBLE) {
    return {
      isValid: false,
      error: 'Cannot redouble without double',
      details: 'Can only redouble after a double'
    };
  }
  
  // Find the contract bid that was doubled
  const lastContractBid = getLastContractBid(auction);
  if (!lastContractBid) {
    return {
      isValid: false,
      error: 'No contract bid to redouble',
      details: 'Cannot find the contract bid that was doubled'
    };
  }
  
  // Can only redouble your own partnership's bid
  if (!isPartnership(currentPlayer, lastContractBid.player)) {
    return {
      isValid: false,
      error: 'Cannot redouble opponent bid',
      details: 'Can only redouble your own or partner\'s bid'
    };
  }
  
  return { isValid: true };
}

// ============================================================================
// AUCTION ANALYSIS
// ============================================================================

/**
 * Gets the last contract bid (non-special bid) from the auction
 */
export function getLastContractBid(auction: Auction): Bid | null {
  for (let i = auction.length - 1; i >= 0; i--) {
    const bid = auction[i];
    if (typeof bid.value !== 'string') {
      return bid;
    }
  }
  return null;
}

/**
 * Checks if the auction has ended (three consecutive passes after a bid)
 */
export function isAuctionComplete(auction: Auction): boolean {
  if (auction.length < 4) {
    return false; // Need at least one bid and three passes
  }
  
  // Check if last three bids are passes
  const lastThree = auction.slice(-3);
  const allPasses = lastThree.every(bid => bid.value === SpecialBid.PASS);
  
  if (!allPasses) {
    return false;
  }
  
  // Check if there was at least one contract bid before the passes
  return getLastContractBid(auction) !== null;
}

/**
 * Checks if all four players have passed (no contract)
 */
export function isAuctionPassedOut(auction: Auction): boolean {
  if (auction.length !== 4) {
    return false;
  }
  
  return auction.every(bid => bid.value === SpecialBid.PASS);
}

/**
 * Determines the final contract from a completed auction
 */
export function getContract(auction: Auction): Contract | null {
  if (!isAuctionComplete(auction) || isAuctionPassedOut(auction)) {
    return null;
  }
  
  const lastContractBid = getLastContractBid(auction);
  if (!lastContractBid || typeof lastContractBid.value === 'string') {
    return null;
  }
  
  // Find the declarer (first player in winning partnership to bid the suit)
  const declarer = findDeclarer(auction, lastContractBid.value);
  
  // Determine if the contract is doubled or redoubled
  const doubled = getContractDoubledStatus(auction);
  
  return {
    level: lastContractBid.value.level,
    suit: lastContractBid.value.suit,
    declarer,
    doubled
  };
}

/**
 * Finds the declarer for a contract (first in partnership to bid the suit)
 */
function findDeclarer(auction: Auction, finalBid: { level: BidLevel; suit: BidSuit }): Position {
  const lastContractBid = getLastContractBid(auction);
  if (!lastContractBid) {
    throw new Error('No contract bid found');
  }
  
  const winningPartnership = [lastContractBid.player, getPartner(lastContractBid.player)];
  
  // Find the first player in the winning partnership to bid the final suit
  for (const bid of auction) {
    if (typeof bid.value !== 'string' && 
        bid.value.suit === finalBid.suit && 
        winningPartnership.includes(bid.player)) {
      return bid.player;
    }
  }
  
  return lastContractBid.player; // Fallback
}

/**
 * Determines if the contract is doubled or redoubled
 */
function getContractDoubledStatus(auction: Auction): 'none' | 'doubled' | 'redoubled' {
  const lastContractBid = getLastContractBid(auction);
  if (!lastContractBid) {
    return 'none';
  }
  
  // Look for doubles/redoubles after the last contract bid
  const lastContractIndex = auction.lastIndexOf(lastContractBid);
  const subsequentBids = auction.slice(lastContractIndex + 1);
  
  let doubled: 'none' | 'doubled' | 'redoubled' = 'none';
  
  for (const bid of subsequentBids) {
    if (bid.value === SpecialBid.DOUBLE) {
      doubled = 'doubled';
    } else if (bid.value === SpecialBid.REDOUBLE) {
      doubled = 'redoubled';
    } else if (typeof bid.value !== 'string') {
      // New contract bid resets doubling
      doubled = 'none';
    }
  }
  
  return doubled;
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Checks if two positions are in the same partnership
 */
function isPartnership(pos1: Position, pos2: Position): boolean {
  return (pos1 === Position.NORTH && pos2 === Position.SOUTH) ||
         (pos1 === Position.SOUTH && pos2 === Position.NORTH) ||
         (pos1 === Position.EAST && pos2 === Position.WEST) ||
         (pos1 === Position.WEST && pos2 === Position.EAST);
}

/**
 * Gets the partner of a position
 */
function getPartner(position: Position): Position {
  switch (position) {
    case Position.NORTH: return Position.SOUTH;
    case Position.SOUTH: return Position.NORTH;
    case Position.EAST: return Position.WEST;
    case Position.WEST: return Position.EAST;
  }
}

/**
 * Checks if the last contract bid is currently doubled
 */
function isLastBidDoubled(auction: Auction): boolean {
  return getContractDoubledStatus(auction) !== 'none';
}

/**
 * Formats a bid value for display
 */
export function formatBid(bid: BidValue): string {
  if (typeof bid === 'string') {
    switch (bid) {
      case SpecialBid.PASS: return 'Pass';
      case SpecialBid.DOUBLE: return 'Double';
      case SpecialBid.REDOUBLE: return 'Redouble';
    }
  }
  
  const suitSymbol = bid.suit === BidSuit.NO_TRUMP ? 'NT' : bid.suit;
  return `${bid.level}${suitSymbol}`;
}

/**
 * Gets the next player in the bidding sequence
 */
export function getNextBidder(currentPlayer: Position): Position {
  return getNextPosition(currentPlayer);
}

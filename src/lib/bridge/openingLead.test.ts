/**
 * Tests for opening lead rules and game phase transitions
 * Verifies that the correct player leads after bidding completes
 */

import {
  createNewGame,
  createPlayers,
  processBid
} from './gameStateUtils';

import {
  Position,
  GamePhase,
  BidLevel,
  BidSuit,
  SpecialBid
} from '../../types/bridge';

describe('Opening Lead Rules', () => {
  let gameState: any;

  beforeEach(() => {
    const players = createPlayers(Position.SOUTH);
    gameState = createNewGame('test-game', players, Position.NORTH);
  });

  describe('Left of Declarer Leads Rule', () => {
    it('should set South to lead when East is declarer', () => {
      const bids = [
        { player: Position.EAST, value: { level: BidLevel.ONE, suit: BidSuit.CLUBS }, timestamp: new Date() },
        { player: Position.SOUTH, value: SpecialBid.PASS, timestamp: new Date() },
        { player: Position.WEST, value: SpecialBid.PASS, timestamp: new Date() },
        { player: Position.NORTH, value: SpecialBid.PASS, timestamp: new Date() }
      ];
      
      let currentState = gameState;
      for (const bid of bids) {
        currentState = processBid(currentState, bid);
      }
      
      expect(currentState.contract.declarer).toBe(Position.EAST);
      expect(currentState.currentPlayer).toBe(Position.SOUTH); // Left of East
    });

    it('should set West to lead when North is declarer', () => {
      const bids = [
        { player: Position.EAST, value: SpecialBid.PASS, timestamp: new Date() },
        { player: Position.SOUTH, value: SpecialBid.PASS, timestamp: new Date() },
        { player: Position.WEST, value: SpecialBid.PASS, timestamp: new Date() },
        { player: Position.NORTH, value: { level: BidLevel.ONE, suit: BidSuit.HEARTS }, timestamp: new Date() },
        { player: Position.EAST, value: SpecialBid.PASS, timestamp: new Date() },
        { player: Position.SOUTH, value: SpecialBid.PASS, timestamp: new Date() },
        { player: Position.WEST, value: SpecialBid.PASS, timestamp: new Date() }
      ];
      
      let currentState = gameState;
      for (const bid of bids) {
        currentState = processBid(currentState, bid);
      }
      
      expect(currentState.contract.declarer).toBe(Position.NORTH);
      expect(currentState.currentPlayer).toBe(Position.WEST); // Left of North
    });

    it('should set North to lead when South is declarer', () => {
      const bids = [
        { player: Position.EAST, value: SpecialBid.PASS, timestamp: new Date() },
        { player: Position.SOUTH, value: { level: BidLevel.ONE, suit: BidSuit.DIAMONDS }, timestamp: new Date() },
        { player: Position.WEST, value: SpecialBid.PASS, timestamp: new Date() },
        { player: Position.NORTH, value: SpecialBid.PASS, timestamp: new Date() },
        { player: Position.EAST, value: SpecialBid.PASS, timestamp: new Date() }
      ];
      
      let currentState = gameState;
      for (const bid of bids) {
        currentState = processBid(currentState, bid);
      }
      
      expect(currentState.contract.declarer).toBe(Position.SOUTH);
      expect(currentState.currentPlayer).toBe(Position.NORTH); // Left of South
    });

    it('should set East to lead when West is declarer', () => {
      const bids = [
        { player: Position.EAST, value: SpecialBid.PASS, timestamp: new Date() },
        { player: Position.SOUTH, value: SpecialBid.PASS, timestamp: new Date() },
        { player: Position.WEST, value: { level: BidLevel.ONE, suit: BidSuit.SPADES }, timestamp: new Date() },
        { player: Position.NORTH, value: SpecialBid.PASS, timestamp: new Date() },
        { player: Position.EAST, value: SpecialBid.PASS, timestamp: new Date() },
        { player: Position.SOUTH, value: SpecialBid.PASS, timestamp: new Date() }
      ];
      
      let currentState = gameState;
      for (const bid of bids) {
        currentState = processBid(currentState, bid);
      }
      
      expect(currentState.contract.declarer).toBe(Position.WEST);
      expect(currentState.currentPlayer).toBe(Position.EAST); // Left of West
    });
  });

  describe('Game Phase Transitions', () => {
    it('should transition from bidding to playing phase', () => {
      expect(gameState.phase).toBe(GamePhase.BIDDING);
      
      const bids = [
        { player: Position.EAST, value: { level: BidLevel.ONE, suit: BidSuit.CLUBS }, timestamp: new Date() },
        { player: Position.SOUTH, value: SpecialBid.PASS, timestamp: new Date() },
        { player: Position.WEST, value: SpecialBid.PASS, timestamp: new Date() },
        { player: Position.NORTH, value: SpecialBid.PASS, timestamp: new Date() }
      ];
      
      let currentState = gameState;
      for (const bid of bids) {
        currentState = processBid(currentState, bid);
      }
      
      expect(currentState.phase).toBe(GamePhase.PLAYING);
    });

    it('should set up contract correctly after bidding', () => {
      const bids = [
        { player: Position.EAST, value: { level: BidLevel.THREE, suit: BidSuit.NT }, timestamp: new Date() },
        { player: Position.SOUTH, value: SpecialBid.PASS, timestamp: new Date() },
        { player: Position.WEST, value: SpecialBid.PASS, timestamp: new Date() },
        { player: Position.NORTH, value: SpecialBid.PASS, timestamp: new Date() }
      ];
      
      let currentState = gameState;
      for (const bid of bids) {
        currentState = processBid(currentState, bid);
      }
      
      expect(currentState.contract).not.toBeNull();
      expect(currentState.contract.level).toBe(BidLevel.THREE);
      expect(currentState.contract.suit).toBe(BidSuit.NT);
      expect(currentState.contract.declarer).toBe(Position.EAST);
      expect(currentState.contract.doubled).toBe('none');
    });

    it('should set dummy correctly (partner of declarer)', () => {
      const bids = [
        { player: Position.EAST, value: { level: BidLevel.ONE, suit: BidSuit.HEARTS }, timestamp: new Date() },
        { player: Position.SOUTH, value: SpecialBid.PASS, timestamp: new Date() },
        { player: Position.WEST, value: SpecialBid.PASS, timestamp: new Date() },
        { player: Position.NORTH, value: SpecialBid.PASS, timestamp: new Date() }
      ];
      
      let currentState = gameState;
      for (const bid of bids) {
        currentState = processBid(currentState, bid);
      }
      
      expect(currentState.contract.declarer).toBe(Position.EAST);
      expect(currentState.dummy).toBe(Position.WEST); // Partner of East
    });

    it('should handle all-pass auctions correctly', () => {
      const bids = [
        { player: Position.EAST, value: SpecialBid.PASS, timestamp: new Date() },
        { player: Position.SOUTH, value: SpecialBid.PASS, timestamp: new Date() },
        { player: Position.WEST, value: SpecialBid.PASS, timestamp: new Date() },
        { player: Position.NORTH, value: SpecialBid.PASS, timestamp: new Date() }
      ];
      
      let currentState = gameState;
      for (const bid of bids) {
        currentState = processBid(currentState, bid);
      }
      
      expect(currentState.phase).toBe(GamePhase.FINISHED);
      expect(currentState.contract).toBeNull();
    });
  });

  describe('Complex Bidding Scenarios', () => {
    it('should handle competitive bidding with correct opening leader', () => {
      const bids = [
        { player: Position.EAST, value: { level: BidLevel.ONE, suit: BidSuit.CLUBS }, timestamp: new Date() },
        { player: Position.SOUTH, value: { level: BidLevel.ONE, suit: BidSuit.HEARTS }, timestamp: new Date() },
        { player: Position.WEST, value: { level: BidLevel.TWO, suit: BidSuit.CLUBS }, timestamp: new Date() },
        { player: Position.NORTH, value: { level: BidLevel.TWO, suit: BidSuit.HEARTS }, timestamp: new Date() },
        { player: Position.EAST, value: SpecialBid.PASS, timestamp: new Date() },
        { player: Position.SOUTH, value: SpecialBid.PASS, timestamp: new Date() },
        { player: Position.WEST, value: SpecialBid.PASS, timestamp: new Date() }
      ];
      
      let currentState = gameState;
      for (const bid of bids) {
        currentState = processBid(currentState, bid);
      }
      
      // North made the final contract bid
      expect(currentState.contract.declarer).toBe(Position.SOUTH); // First to bid hearts in N-S
      expect(currentState.currentPlayer).toBe(Position.NORTH); // Left of South
      expect(currentState.dummy).toBe(Position.NORTH); // Partner of South
    });

    it('should handle doubled contracts with correct setup', () => {
      const bids = [
        { player: Position.EAST, value: { level: BidLevel.ONE, suit: BidSuit.SPADES }, timestamp: new Date() },
        { player: Position.SOUTH, value: SpecialBid.DOUBLE, timestamp: new Date() },
        { player: Position.WEST, value: SpecialBid.PASS, timestamp: new Date() },
        { player: Position.NORTH, value: SpecialBid.PASS, timestamp: new Date() }
      ];
      
      let currentState = gameState;
      for (const bid of bids) {
        currentState = processBid(currentState, bid);
      }
      
      expect(currentState.contract.declarer).toBe(Position.EAST);
      expect(currentState.contract.doubled).toBe('doubled');
      expect(currentState.currentPlayer).toBe(Position.SOUTH); // Left of East
      expect(currentState.dummy).toBe(Position.WEST); // Partner of East
    });
  });

  describe('Partnership Logic', () => {
    it('should correctly identify partnerships for dummy assignment', () => {
      // Test North-South partnership
      const bidsNS = [
        { player: Position.EAST, value: SpecialBid.PASS, timestamp: new Date() },
        { player: Position.SOUTH, value: { level: BidLevel.ONE, suit: BidSuit.CLUBS }, timestamp: new Date() },
        { player: Position.WEST, value: SpecialBid.PASS, timestamp: new Date() },
        { player: Position.NORTH, value: SpecialBid.PASS, timestamp: new Date() },
        { player: Position.EAST, value: SpecialBid.PASS, timestamp: new Date() }
      ];
      
      let currentState = gameState;
      for (const bid of bidsNS) {
        currentState = processBid(currentState, bid);
      }
      
      expect(currentState.contract.declarer).toBe(Position.SOUTH);
      expect(currentState.dummy).toBe(Position.NORTH); // Partner
      
      // Test East-West partnership
      gameState = createNewGame('test-game-2', createPlayers(Position.SOUTH), Position.NORTH);
      const bidsEW = [
        { player: Position.EAST, value: { level: BidLevel.ONE, suit: BidSuit.DIAMONDS }, timestamp: new Date() },
        { player: Position.SOUTH, value: SpecialBid.PASS, timestamp: new Date() },
        { player: Position.WEST, value: SpecialBid.PASS, timestamp: new Date() },
        { player: Position.NORTH, value: SpecialBid.PASS, timestamp: new Date() }
      ];
      
      currentState = gameState;
      for (const bid of bidsEW) {
        currentState = processBid(currentState, bid);
      }
      
      expect(currentState.contract.declarer).toBe(Position.EAST);
      expect(currentState.dummy).toBe(Position.WEST); // Partner
    });
  });
});

/**
 * AI Player implementation for Bridge game
 * Implements SAYC (Standard American Yellow Card) bidding system
 * and basic card play logic
 */

import {
  Card,
  Suit,
  Rank,
  Position,
  Hand,
  Bid,
  BidValue,
  BidLevel,
  BidSuit,
  SpecialBid,
  Contract,
  Auction,
  PlayedCard,
  GameState
} from '../../types/bridge';

import {
  getCardsOfSuit,
  getRankValue,
  sortHand
} from './cardUtils';

import {
  getLastContractBid,
  formatBid
} from './biddingUtils';

// ============================================================================
// HAND EVALUATION CONSTANTS (SAYC)
// ============================================================================

// High Card Points (HCP)
const HCP_VALUES = {
  [Rank.ACE]: 4,
  [Rank.KING]: 3,
  [Rank.QUEEN]: 2,
  [Rank.JACK]: 1,
  [Rank.TEN]: 0,
  [Rank.NINE]: 0,
  [Rank.EIGHT]: 0,
  [Rank.SEVEN]: 0,
  [Rank.SIX]: 0,
  [Rank.FIVE]: 0,
  [Rank.FOUR]: 0,
  [Rank.THREE]: 0,
  [Rank.TWO]: 0
};

// Distribution Points
const DISTRIBUTION_POINTS = {
  VOID: 3,
  SINGLETON: 2,
  DOUBLETON: 1
};

// Opening bid requirements
const OPENING_BID_REQUIREMENTS = {
  MIN_HCP: 12,
  MIN_TOTAL_POINTS: 13,
  STRONG_2_MIN_HCP: 22,
  WEAK_2_HCP_RANGE: [6, 10],
  PREEMPT_HCP_RANGE: [5, 9]
};

// Suit length requirements
const SUIT_LENGTH_REQUIREMENTS = {
  MAJOR_OPENING: 5,
  MINOR_OPENING: 3,
  WEAK_2: 6,
  PREEMPT_3: 7,
  PREEMPT_4: 8
};

// Response requirements
const RESPONSE_REQUIREMENTS = {
  MIN_HCP: 6,
  GAME_FORCE_HCP: 13,
  SLAM_INTEREST_HCP: 16
};

// ============================================================================
// HAND EVALUATION FUNCTIONS
// ============================================================================

/**
 * Calculates High Card Points (HCP) for a hand
 */
export function calculateHCP(hand: Hand): number {
  return hand.reduce((total, card) => total + HCP_VALUES[card.rank], 0);
}

/**
 * Calculates distribution points for a hand
 */
export function calculateDistributionPoints(hand: Hand): number {
  const suitLengths = getSuitLengths(hand);
  let points = 0;
  
  for (const length of Object.values(suitLengths)) {
    if (length === 0) points += DISTRIBUTION_POINTS.VOID;
    else if (length === 1) points += DISTRIBUTION_POINTS.SINGLETON;
    else if (length === 2) points += DISTRIBUTION_POINTS.DOUBLETON;
  }
  
  return points;
}

/**
 * Gets the length of each suit in a hand
 */
export function getSuitLengths(hand: Hand): Record<Suit, number> {
  const lengths = {
    [Suit.CLUBS]: 0,
    [Suit.DIAMONDS]: 0,
    [Suit.HEARTS]: 0,
    [Suit.SPADES]: 0
  };
  
  for (const card of hand) {
    lengths[card.suit]++;
  }
  
  return lengths;
}

/**
 * Calculates total points (HCP + distribution) for a hand
 */
export function calculateTotalPoints(hand: Hand): number {
  return calculateHCP(hand) + calculateDistributionPoints(hand);
}

/**
 * Determines if a suit is biddable (has minimum length and quality)
 */
export function isSuitBiddable(hand: Hand, suit: Suit, minLength: number = 4): boolean {
  const suitCards = getCardsOfSuit(hand, suit);
  
  if (suitCards.length < minLength) {
    return false;
  }
  
  // Check suit quality - need at least one honor in 4-card suits
  if (suitCards.length === 4) {
    const hasHonor = suitCards.some(card => 
      [Rank.ACE, Rank.KING, Rank.QUEEN, Rank.JACK].includes(card.rank)
    );
    return hasHonor;
  }
  
  return true; // 5+ card suits are always biddable
}

/**
 * Gets the longest suit(s) in a hand
 */
export function getLongestSuits(hand: Hand): Suit[] {
  const lengths = getSuitLengths(hand);
  const maxLength = Math.max(...Object.values(lengths));
  
  return Object.entries(lengths)
    .filter(([_, length]) => length === maxLength)
    .map(([suit, _]) => suit as Suit);
}

/**
 * Determines if a hand is balanced (no singletons/voids, at most one doubleton)
 */
export function isBalancedHand(hand: Hand): boolean {
  const lengths = Object.values(getSuitLengths(hand));
  const doubletons = lengths.filter(len => len === 2).length;
  const singletons = lengths.filter(len => len === 1).length;
  const voids = lengths.filter(len => len === 0).length;
  
  return singletons === 0 && voids === 0 && doubletons <= 1;
}

// ============================================================================
// SAYC BIDDING LOGIC
// ============================================================================

/**
 * AI Player class implementing SAYC bidding system
 */
export class AIPlayer {
  private position: Position;
  private difficulty: 'beginner' | 'intermediate' | 'advanced';
  
  constructor(position: Position, difficulty: 'beginner' | 'intermediate' | 'advanced' = 'intermediate') {
    this.position = position;
    this.difficulty = difficulty;
  }
  
  /**
   * Makes a bidding decision based on hand and auction
   */
  public makeBid(hand: Hand, auction: Auction): BidValue {
    const hcp = calculateHCP(hand);
    const totalPoints = calculateTotalPoints(hand);
    const isBalanced = isBalancedHand(hand);
    const suitLengths = getSuitLengths(hand);
    
    // If this is the opening bid
    if (auction.length === 0 || auction.every(bid => bid.value === SpecialBid.PASS)) {
      return this.makeOpeningBid(hand, hcp, totalPoints, isBalanced, suitLengths);
    }
    
    // If partner has opened
    const lastContractBid = getLastContractBid(auction);
    if (lastContractBid && this.isPartner(lastContractBid.player)) {
      return this.makeResponseBid(hand, auction, hcp, totalPoints, isBalanced, suitLengths);
    }
    
    // If opponents have opened
    if (lastContractBid && !this.isPartner(lastContractBid.player)) {
      return this.makeCompetitiveBid(hand, auction, hcp, totalPoints, isBalanced, suitLengths);
    }
    
    // Default to pass
    return SpecialBid.PASS;
  }
  
  /**
   * Makes an opening bid
   */
  private makeOpeningBid(
    hand: Hand,
    hcp: number,
    totalPoints: number,
    isBalanced: boolean,
    suitLengths: Record<Suit, number>
  ): BidValue {
    // Not enough points to open
    if (hcp < OPENING_BID_REQUIREMENTS.MIN_HCP && totalPoints < OPENING_BID_REQUIREMENTS.MIN_TOTAL_POINTS) {
      return SpecialBid.PASS;
    }
    
    // Strong 2 Club opening (22+ HCP)
    if (hcp >= OPENING_BID_REQUIREMENTS.STRONG_2_MIN_HCP) {
      return { level: BidLevel.TWO, suit: BidSuit.CLUBS };
    }
    
    // Balanced hand openings
    if (isBalanced) {
      if (hcp >= 15 && hcp <= 17) {
        return { level: BidLevel.ONE, suit: BidSuit.NO_TRUMP };
      }
      if (hcp >= 20 && hcp <= 21) {
        return { level: BidLevel.TWO, suit: BidSuit.NO_TRUMP };
      }
    }
    
    // Suit openings - bid longest suit first
    const longestSuits = this.getLongestBiddableSuits(hand, suitLengths);
    
    if (longestSuits.length > 0) {
      // With equal length suits, bid higher ranking first
      const suitToBid = longestSuits.sort((a, b) => this.getSuitRank(b) - this.getSuitRank(a))[0];
      
      // Check for weak 2 bids (6-card suit, 6-10 HCP)
      if (suitLengths[suitToBid] >= SUIT_LENGTH_REQUIREMENTS.WEAK_2 &&
          hcp >= OPENING_BID_REQUIREMENTS.WEAK_2_HCP_RANGE[0] &&
          hcp <= OPENING_BID_REQUIREMENTS.WEAK_2_HCP_RANGE[1] &&
          (suitToBid === Suit.HEARTS || suitToBid === Suit.SPADES || suitToBid === Suit.DIAMONDS)) {
        return { level: BidLevel.TWO, suit: this.suitToBidSuit(suitToBid) };
      }
      
      return { level: BidLevel.ONE, suit: this.suitToBidSuit(suitToBid) };
    }
    
    // If no good suit, open 1 Club (could be short)
    if (totalPoints >= OPENING_BID_REQUIREMENTS.MIN_TOTAL_POINTS) {
      return { level: BidLevel.ONE, suit: BidSuit.CLUBS };
    }
    
    return SpecialBid.PASS;
  }
  
  /**
   * Makes a response bid to partner's opening
   */
  private makeResponseBid(
    hand: Hand,
    auction: Auction,
    hcp: number,
    totalPoints: number,
    isBalanced: boolean,
    suitLengths: Record<Suit, number>
  ): BidValue {
    // Not enough points to respond
    if (hcp < RESPONSE_REQUIREMENTS.MIN_HCP) {
      return SpecialBid.PASS;
    }
    
    const partnersBid = getLastContractBid(auction);
    if (!partnersBid || typeof partnersBid.value === 'string') {
      return SpecialBid.PASS;
    }
    
    // Response to 1NT opening
    if (partnersBid.value.level === BidLevel.ONE && partnersBid.value.suit === BidSuit.NO_TRUMP) {
      return this.respondTo1NT(hand, hcp, totalPoints, suitLengths);
    }
    
    // Response to suit opening
    if (partnersBid.value.level === BidLevel.ONE && partnersBid.value.suit !== BidSuit.NO_TRUMP) {
      return this.respondToSuitOpening(hand, partnersBid.value, hcp, totalPoints, isBalanced, suitLengths);
    }
    
    // For now, pass to other openings
    return SpecialBid.PASS;
  }
  
  /**
   * Makes a competitive bid over opponents' opening
   */
  private makeCompetitiveBid(
    hand: Hand,
    auction: Auction,
    hcp: number,
    totalPoints: number,
    isBalanced: boolean,
    suitLengths: Record<Suit, number>
  ): BidValue {
    // Simple overcall logic - need good suit and 8+ HCP
    if (hcp >= 8) {
      const longestSuits = this.getLongestBiddableSuits(hand, suitLengths, 5);
      if (longestSuits.length > 0) {
        const suitToBid = longestSuits[0];
        const lastBid = getLastContractBid(auction);
        
        if (lastBid && typeof lastBid.value !== 'string') {
          // Simple overcall at 1-level if possible
          if (this.canBidSuitAtLevel(suitToBid, BidLevel.ONE, lastBid.value)) {
            return { level: BidLevel.ONE, suit: this.suitToBidSuit(suitToBid) };
          }
          // Otherwise try 2-level
          if (this.canBidSuitAtLevel(suitToBid, BidLevel.TWO, lastBid.value) && hcp >= 10) {
            return { level: BidLevel.TWO, suit: this.suitToBidSuit(suitToBid) };
          }
        }
      }
    }
    
    return SpecialBid.PASS;
  }
  
  // ============================================================================
  // HELPER METHODS
  // ============================================================================
  
  private isPartner(position: Position): boolean {
    return (this.position === Position.NORTH && position === Position.SOUTH) ||
           (this.position === Position.SOUTH && position === Position.NORTH) ||
           (this.position === Position.EAST && position === Position.WEST) ||
           (this.position === Position.WEST && position === Position.EAST);
  }
  
  private getLongestBiddableSuits(hand: Hand, suitLengths: Record<Suit, number>, minLength: number = 4): Suit[] {
    const biddableSuits = Object.entries(suitLengths)
      .filter(([suit, length]) => length >= minLength && isSuitBiddable(hand, suit as Suit, minLength))
      .sort(([, lengthA], [, lengthB]) => lengthB - lengthA)
      .map(([suit, _]) => suit as Suit);
    
    return biddableSuits;
  }
  
  private getSuitRank(suit: Suit): number {
    switch (suit) {
      case Suit.CLUBS: return 1;
      case Suit.DIAMONDS: return 2;
      case Suit.HEARTS: return 3;
      case Suit.SPADES: return 4;
    }
  }
  
  private suitToBidSuit(suit: Suit): BidSuit {
    switch (suit) {
      case Suit.CLUBS: return BidSuit.CLUBS;
      case Suit.DIAMONDS: return BidSuit.DIAMONDS;
      case Suit.HEARTS: return BidSuit.HEARTS;
      case Suit.SPADES: return BidSuit.SPADES;
    }
  }
  
  private canBidSuitAtLevel(suit: Suit, level: BidLevel, lastBid: { level: BidLevel; suit: BidSuit }): boolean {
    const bidValue = (level - 1) * 5 + this.getSuitRank(suit);
    const lastBidValue = (lastBid.level - 1) * 5 + this.getBidSuitRank(lastBid.suit);
    return bidValue > lastBidValue;
  }
  
  private getBidSuitRank(suit: BidSuit): number {
    switch (suit) {
      case BidSuit.CLUBS: return 1;
      case BidSuit.DIAMONDS: return 2;
      case BidSuit.HEARTS: return 3;
      case BidSuit.SPADES: return 4;
      case BidSuit.NO_TRUMP: return 5;
    }
  }
  
  private respondTo1NT(hand: Hand, hcp: number, totalPoints: number, suitLengths: Record<Suit, number>): BidValue {
    // With 8+ HCP, consider game
    if (hcp >= 8) {
      // Look for 4-card major
      if (suitLengths[Suit.HEARTS] >= 4) {
        return { level: BidLevel.TWO, suit: BidSuit.HEARTS }; // Jacoby transfer
      }
      if (suitLengths[Suit.SPADES] >= 4) {
        return { level: BidLevel.TWO, suit: BidSuit.DIAMONDS }; // Jacoby transfer
      }
      
      // With balanced hand and 8-9 HCP, invite to game
      if (isBalancedHand(hand) && hcp >= 8 && hcp <= 9) {
        return { level: BidLevel.TWO, suit: BidSuit.NO_TRUMP };
      }
      
      // With 10+ HCP, bid game
      if (hcp >= 10) {
        return { level: BidLevel.THREE, suit: BidSuit.NO_TRUMP };
      }
    }
    
    return SpecialBid.PASS;
  }
  
  private respondToSuitOpening(
    hand: Hand,
    partnersBid: { level: BidLevel; suit: BidSuit },
    hcp: number,
    totalPoints: number,
    isBalanced: boolean,
    suitLengths: Record<Suit, number>
  ): BidValue {
    // With 6-9 HCP, make minimum response
    if (hcp >= 6 && hcp <= 9) {
      // Support partner's major with 3+ cards
      if ((partnersBid.suit === BidSuit.HEARTS || partnersBid.suit === BidSuit.SPADES)) {
        const partnerSuit = this.bidSuitToSuit(partnersBid.suit);
        if (suitLengths[partnerSuit] >= 3) {
          return { level: BidLevel.TWO, suit: partnersBid.suit };
        }
      }
      
      // Bid own 4+ card major
      if (suitLengths[Suit.HEARTS] >= 4 && this.canBidSuitAtLevel(Suit.HEARTS, BidLevel.ONE, partnersBid)) {
        return { level: BidLevel.ONE, suit: BidSuit.HEARTS };
      }
      if (suitLengths[Suit.SPADES] >= 4 && this.canBidSuitAtLevel(Suit.SPADES, BidLevel.ONE, partnersBid)) {
        return { level: BidLevel.ONE, suit: BidSuit.SPADES };
      }
      
      // Bid 1NT with balanced hand
      if (isBalanced && this.canBidSuitAtLevel(Suit.CLUBS, BidLevel.ONE, partnersBid)) {
        return { level: BidLevel.ONE, suit: BidSuit.NO_TRUMP };
      }
    }
    
    return SpecialBid.PASS;
  }
  
  private bidSuitToSuit(bidSuit: BidSuit): Suit {
    switch (bidSuit) {
      case BidSuit.CLUBS: return Suit.CLUBS;
      case BidSuit.DIAMONDS: return Suit.DIAMONDS;
      case BidSuit.HEARTS: return Suit.HEARTS;
      case BidSuit.SPADES: return Suit.SPADES;
      default: throw new Error(`Cannot convert ${bidSuit} to Suit`);
    }
  }

  // ============================================================================
  // CARD PLAY LOGIC
  // ============================================================================

  /**
   * Makes a card play decision based on game state
   */
  public playCard(gameState: GameState): Card {
    const hand = gameState.players[this.position].hand;
    const currentTrick = gameState.currentTrick;
    const contract = gameState.contract;
    
    if (!contract) {
      throw new Error('Cannot play card without a contract');
    }
    
    // If leading to a trick
    if (currentTrick.length === 0) {
      return this.makeLeadingPlay(hand, gameState);
    }
    
    // If following to a trick
    return this.makeFollowingPlay(hand, currentTrick, contract);
  }
  
  /**
   * Makes a leading play (first card of trick)
   */
  private makeLeadingPlay(hand: Hand, gameState: GameState): Card {
    const contract = gameState.contract!;
    const isDefender = this.position !== contract.declarer && 
                      this.position !== this.getDummyPosition(contract);
    
    if (isDefender) {
      return this.makeDefensiveLead(hand, contract, gameState);
    } else {
      return this.makeDeclarerLead(hand, contract, gameState);
    }
  }
  
  /**
   * Makes a defensive opening lead
   */
  private makeDefensiveLead(hand: Hand, contract: Contract, gameState: GameState): Card {
    const trumpSuit = contract.suit === BidSuit.NO_TRUMP ? null : this.bidSuitToSuit(contract.suit);
    const suitLengths = getSuitLengths(hand);
    
    // Against NT contracts
    if (!trumpSuit) {
      // Lead longest suit
      const longestSuits = Object.entries(suitLengths)
        .sort(([, lengthA], [, lengthB]) => lengthB - lengthA)
        .map(([suit, _]) => suit as Suit);
      
      const suitToLead = longestSuits[0];
      return this.getLeadCardFromSuit(hand, suitToLead, false);
    }
    
    // Against suit contracts - don't lead trump unless it's the only option
    const nonTrumpSuits = Object.entries(suitLengths)
      .filter(([suit, _]) => suit !== trumpSuit)
      .sort(([, lengthA], [, lengthB]) => lengthB - lengthA)
      .map(([suit, _]) => suit as Suit);
    
    if (nonTrumpSuits.length > 0) {
      const suitToLead = nonTrumpSuits[0];
      return this.getLeadCardFromSuit(hand, suitToLead, false);
    }
    
    // Must lead trump
    return this.getLeadCardFromSuit(hand, trumpSuit, true);
  }
  
  /**
   * Makes a declarer lead
   */
  private makeDeclarerLead(hand: Hand, contract: Contract, gameState: GameState): Card {
    // Simple strategy: lead towards dummy or establish long suits
    const suitLengths = getSuitLengths(hand);
    const longestSuits = Object.entries(suitLengths)
      .sort(([, lengthA], [, lengthB]) => lengthB - lengthA)
      .map(([suit, _]) => suit as Suit);
    
    const suitToLead = longestSuits[0];
    return this.getLeadCardFromSuit(hand, suitToLead, false);
  }
  
  /**
   * Gets the appropriate card to lead from a suit
   */
  private getLeadCardFromSuit(hand: Hand, suit: Suit, isTrump: boolean): Card {
    const suitCards = getCardsOfSuit(hand, suit).sort((a, b) => getRankValue(b.rank) - getRankValue(a.rank));
    
    if (suitCards.length === 0) {
      // This shouldn't happen, but fallback to any card
      return hand[0];
    }
    
    // Lead top of sequence (A-K, K-Q, Q-J, etc.)
    if (suitCards.length >= 2) {
      const topCard = suitCards[0];
      const secondCard = suitCards[1];
      
      if (getRankValue(topCard.rank) - getRankValue(secondCard.rank) === 1) {
        return topCard; // Top of sequence
      }
    }
    
    // Lead low from honor (fourth best)
    if (suitCards.length >= 4 && 
        [Rank.ACE, Rank.KING, Rank.QUEEN, Rank.JACK].includes(suitCards[0].rank)) {
      return suitCards[3]; // Fourth best
    }
    
    // Lead top of nothing
    return suitCards[0];
  }
  
  /**
   * Makes a following play (not leading)
   */
  private makeFollowingPlay(hand: Hand, currentTrick: PlayedCard[], contract: Contract): Card {
    const leadSuit = currentTrick[0].card.suit;
    const trumpSuit = contract.suit === BidSuit.NO_TRUMP ? null : this.bidSuitToSuit(contract.suit);
    const suitCards = getCardsOfSuit(hand, leadSuit);
    
    // Must follow suit if possible
    if (suitCards.length > 0) {
      return this.chooseCardFromSuit(suitCards, currentTrick, trumpSuit);
    }
    
    // Can't follow suit - choose discard or trump
    return this.chooseDiscardOrTrump(hand, currentTrick, trumpSuit);
  }

  /**
   * Chooses which card to play when following suit
   */
  private chooseCardFromSuit(suitCards: Card[], currentTrick: PlayedCard[], trumpSuit: Suit | null): Card {
    const sortedCards = suitCards.sort((a, b) => getRankValue(a.rank) - getRankValue(b.rank));
    const highestCardPlayed = this.getHighestCardInTrick(currentTrick, currentTrick[0].card.suit, trumpSuit);
    
    // Try to win the trick if possible
    const winningCards = sortedCards.filter(card => 
      getRankValue(card.rank) > getRankValue(highestCardPlayed.rank)
    );
    
    if (winningCards.length > 0) {
      // Play lowest card that wins
      return winningCards[0];
    }
    
    // Can't win - play lowest card
    return sortedCards[0];
  }
  
  /**
   * Chooses a discard or trump when can't follow suit
   */
  private chooseDiscardOrTrump(hand: Hand, currentTrick: PlayedCard[], trumpSuit: Suit | null): Card {
    // If we have trump and no one else has trumped
    if (trumpSuit) {
      const trumpCards = getCardsOfSuit(hand, trumpSuit);
      const trickHasTrump = currentTrick.some(played => played.card.suit === trumpSuit);
      
      if (trumpCards.length > 0 && !trickHasTrump) {
        // Trump with lowest trump
        return trumpCards.sort((a, b) => getRankValue(a.rank) - getRankValue(b.rank))[0];
      }
    }
    
    // Discard lowest card from longest suit
    const suitLengths = getSuitLengths(hand);
    const longestSuit = Object.entries(suitLengths)
      .sort(([, lengthA], [, lengthB]) => lengthB - lengthA)[0][0] as Suit;
    
    const discardCards = getCardsOfSuit(hand, longestSuit);
    if (discardCards.length > 0) {
      return discardCards.sort((a, b) => getRankValue(a.rank) - getRankValue(b.rank))[0];
    }
    
    // Fallback to any card
    return hand[0];
  }
  
  /**
   * Gets the highest card currently winning the trick
   */
  private getHighestCardInTrick(currentTrick: PlayedCard[], leadSuit: Suit, trumpSuit: Suit | null): Card {
    let winningCard = currentTrick[0].card;
    
    for (let i = 1; i < currentTrick.length; i++) {
      const card = currentTrick[i].card;
      
      if (this.isCardHigher(card, winningCard, leadSuit, trumpSuit)) {
        winningCard = card;
      }
    }
    
    return winningCard;
  }
  
  /**
   * Determines if card1 beats card2 in the context of a trick
   */
  private isCardHigher(card1: Card, card2: Card, leadSuit: Suit, trumpSuit: Suit | null): boolean {
    const card1IsTrump = trumpSuit !== null && card1.suit === trumpSuit;
    const card2IsTrump = trumpSuit !== null && card2.suit === trumpSuit;
    const card1FollowsSuit = card1.suit === leadSuit;
    const card2FollowsSuit = card2.suit === leadSuit;

    // Trump beats non-trump
    if (card1IsTrump && !card2IsTrump) return true;
    if (card2IsTrump && !card1IsTrump) return false;

    // Both trump: higher rank wins
    if (card1IsTrump && card2IsTrump) {
      return getRankValue(card1.rank) > getRankValue(card2.rank);
    }

    // Neither trump: only cards following suit can win
    if (!card1FollowsSuit && card2FollowsSuit) return false;
    if (card1FollowsSuit && !card2FollowsSuit) return true;

    // Both follow suit: higher rank wins
    if (card1FollowsSuit && card2FollowsSuit) {
      return getRankValue(card1.rank) > getRankValue(card2.rank);
    }

    // Neither follows suit: first card wins
    return false;
  }
  
  private getDummyPosition(contract: Contract): Position {
    switch (contract.declarer) {
      case Position.NORTH: return Position.SOUTH;
      case Position.SOUTH: return Position.NORTH;
      case Position.EAST: return Position.WEST;
      case Position.WEST: return Position.EAST;
    }
  }

  // ============================================================================
  // AI TIMING AND DECISION SIMULATION
  // ============================================================================

  /**
   * Simulates thinking time for AI decisions
   */
  public async makeDecisionWithTiming<T>(decisionFunction: () => T): Promise<T> {
    const thinkingTime = this.calculateThinkingTime();
    
    // Simulate thinking time
    await new Promise(resolve => setTimeout(resolve, thinkingTime));
    
    return decisionFunction();
  }
  
  /**
   * Calculates realistic thinking time based on difficulty and decision complexity
   */
  private calculateThinkingTime(): number {
    const baseTime = {
      beginner: 2000,    // 2 seconds
      intermediate: 1000, // 1 second
      advanced: 500      // 0.5 seconds
    }[this.difficulty];
    
    // Add some randomness to make it feel more human
    const randomFactor = 0.5 + Math.random(); // 0.5 to 1.5
    
    return Math.floor(baseTime * randomFactor);
  }
}

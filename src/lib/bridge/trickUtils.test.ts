/**
 * Unit tests for trickUtils module
 * Tests trick validation, winner determination, and play sequence
 */

import {
  validateCardPlay,
  getTrick<PERSON>inner,
  createTrick,
  addCardToTrick,
  isTrickComplete,
  getNextPlayerInTrick,
  getNextTrickLeader,
  areAll<PERSON><PERSON><PERSON><PERSON>plete,
  countTricks<PERSON>on,
  getDeclaringPartnership,
  calculateContractResult,
  getDummyPosition,
  isDummy,
  canDeclarerPlayFromDummy,
  validateTrickSequence,
  validateUniqueCards
} from './trickUtils';

import {
  Card,
  Suit,
  Rank,
  Position,
  PlayedCard,
  Trick,
  Contract,
  BidLevel,
  BidSuit
} from '../../types/bridge';

describe('trickUtils', () => {
  // Test data
  const aceOfSpades: Card = { rank: Rank.ACE, suit: Suit.SPADES };
  const kingOfSpades: Card = { rank: Rank.KING, suit: Suit.SPADES };
  const queenOfHearts: Card = { rank: Rank.QUEEN, suit: Suit.HEARTS };
  const jackOfClubs: Card = { rank: Rank.JACK, suit: Suit.CLUBS };
  const tenOfDiamonds: Card = { rank: Rank.TEN, suit: Suit.DIAMONDS };
  const twoOfSpades: Card = { rank: Rank.TWO, suit: Suit.SPADES };

  const testHand: Card[] = [
    aceOfSpades,
    kingOfSpades,
    queenOfHearts,
    jackOfClubs,
    tenOfDiamonds
  ];

  const spadesContract: Contract = {
    level: BidLevel.FOUR,
    suit: BidSuit.SPADES,
    declarer: Position.NORTH,
    doubled: 'none'
  };

  const noTrumpContract: Contract = {
    level: BidLevel.THREE,
    suit: BidSuit.NO_TRUMP,
    declarer: Position.SOUTH,
    doubled: 'none'
  };

  describe('validateCardPlay', () => {
    it('should allow any card when leading', () => {
      const result = validateCardPlay(aceOfSpades, testHand, [], spadesContract);
      expect(result.isValid).toBe(true);
    });

    it('should reject card not in hand', () => {
      const cardNotInHand: Card = { rank: Rank.NINE, suit: Suit.DIAMONDS };
      const result = validateCardPlay(cardNotInHand, testHand, [], spadesContract);
      expect(result.isValid).toBe(false);
      expect(result.error).toBe('Card not in hand');
    });

    it('should allow following suit', () => {
      const currentTrick: PlayedCard[] = [
        { card: kingOfSpades, player: Position.NORTH }
      ];
      
      const result = validateCardPlay(aceOfSpades, testHand, currentTrick, spadesContract);
      expect(result.isValid).toBe(true);
    });

    it('should require following suit when possible', () => {
      const currentTrick: PlayedCard[] = [
        { card: kingOfSpades, player: Position.NORTH }
      ];
      
      const result = validateCardPlay(queenOfHearts, testHand, currentTrick, spadesContract);
      expect(result.isValid).toBe(false);
      expect(result.error).toBe('Must follow suit');
    });

    it('should allow any card when void in lead suit', () => {
      const handVoidInSpades: Card[] = [queenOfHearts, jackOfClubs, tenOfDiamonds];
      const currentTrick: PlayedCard[] = [
        { card: kingOfSpades, player: Position.NORTH }
      ];
      
      const result = validateCardPlay(queenOfHearts, handVoidInSpades, currentTrick, spadesContract);
      expect(result.isValid).toBe(true);
    });
  });

  describe('getTrickWinner', () => {
    it('should determine winner in no trump contract', () => {
      const trick: PlayedCard[] = [
        { card: kingOfSpades, player: Position.NORTH },
        { card: aceOfSpades, player: Position.EAST },
        { card: twoOfSpades, player: Position.SOUTH },
        { card: queenOfHearts, player: Position.WEST }
      ];
      
      const winner = getTrickWinner(trick, noTrumpContract);
      expect(winner).toBe(Position.EAST); // Ace of Spades wins
    });

    it('should determine winner with trump suit', () => {
      const trick: PlayedCard[] = [
        { card: aceOfSpades, player: Position.NORTH }, // Trump
        { card: queenOfHearts, player: Position.EAST },
        { card: jackOfClubs, player: Position.SOUTH },
        { card: tenOfDiamonds, player: Position.WEST }
      ];
      
      const winner = getTrickWinner(trick, spadesContract);
      expect(winner).toBe(Position.NORTH); // Trump wins
    });

    it('should handle trump vs trump', () => {
      const trick: PlayedCard[] = [
        { card: kingOfSpades, player: Position.NORTH }, // Trump
        { card: aceOfSpades, player: Position.EAST }, // Higher trump
        { card: queenOfHearts, player: Position.SOUTH },
        { card: jackOfClubs, player: Position.WEST }
      ];
      
      const winner = getTrickWinner(trick, spadesContract);
      expect(winner).toBe(Position.EAST); // Higher trump wins
    });

    it('should handle off-suit cards', () => {
      const trick: PlayedCard[] = [
        { card: queenOfHearts, player: Position.NORTH }, // Lead suit
        { card: jackOfClubs, player: Position.EAST }, // Off-suit
        { card: tenOfDiamonds, player: Position.SOUTH }, // Off-suit
        { card: { rank: Rank.KING, suit: Suit.HEARTS }, player: Position.WEST } // Higher in lead suit
      ];
      
      const winner = getTrickWinner(trick, spadesContract);
      expect(winner).toBe(Position.WEST); // King of Hearts wins
    });

    it('should throw error for incomplete trick', () => {
      const incompleteTrick: PlayedCard[] = [
        { card: aceOfSpades, player: Position.NORTH }
      ];
      
      expect(() => getTrickWinner(incompleteTrick, spadesContract)).toThrow('exactly 4 cards');
    });
  });

  describe('trick management', () => {
    describe('createTrick', () => {
      it('should create empty trick with correct properties', () => {
        const trick = createTrick(1, Position.NORTH);
        expect(trick.id).toBe(1);
        expect(trick.leadPlayer).toBe(Position.NORTH);
        expect(trick.cards).toHaveLength(0);
        expect(trick.winner).toBe(Position.NORTH);
      });
    });

    describe('addCardToTrick', () => {
      it('should add card to trick', () => {
        const trick = createTrick(1, Position.NORTH);
        const newTrick = addCardToTrick(trick, aceOfSpades, Position.NORTH);
        
        expect(newTrick.cards).toHaveLength(1);
        expect(newTrick.cards[0].card).toEqual(aceOfSpades);
        expect(newTrick.cards[0].player).toBe(Position.NORTH);
      });

      it('should not modify original trick', () => {
        const trick = createTrick(1, Position.NORTH);
        addCardToTrick(trick, aceOfSpades, Position.NORTH);
        
        expect(trick.cards).toHaveLength(0);
      });

      it('should throw error when adding to full trick', () => {
        let trick = createTrick(1, Position.NORTH);
        trick = addCardToTrick(trick, aceOfSpades, Position.NORTH);
        trick = addCardToTrick(trick, kingOfSpades, Position.EAST);
        trick = addCardToTrick(trick, queenOfHearts, Position.SOUTH);
        trick = addCardToTrick(trick, jackOfClubs, Position.WEST);
        
        expect(() => addCardToTrick(trick, tenOfDiamonds, Position.NORTH))
          .toThrow('already has 4 cards');
      });
    });

    describe('isTrickComplete', () => {
      it('should return false for empty trick', () => {
        const trick = createTrick(1, Position.NORTH);
        expect(isTrickComplete(trick)).toBe(false);
      });

      it('should return true for full trick', () => {
        let trick = createTrick(1, Position.NORTH);
        trick = addCardToTrick(trick, aceOfSpades, Position.NORTH);
        trick = addCardToTrick(trick, kingOfSpades, Position.EAST);
        trick = addCardToTrick(trick, queenOfHearts, Position.SOUTH);
        trick = addCardToTrick(trick, jackOfClubs, Position.WEST);
        
        expect(isTrickComplete(trick)).toBe(true);
      });
    });

    describe('getNextPlayerInTrick', () => {
      it('should return lead player for empty trick', () => {
        const trick = createTrick(1, Position.NORTH);
        const nextPlayer = getNextPlayerInTrick(trick, Position.NORTH);
        expect(nextPlayer).toBe(Position.NORTH);
      });

      it('should return next player in sequence', () => {
        let trick = createTrick(1, Position.NORTH);
        trick = addCardToTrick(trick, aceOfSpades, Position.NORTH);
        
        const nextPlayer = getNextPlayerInTrick(trick, Position.NORTH);
        expect(nextPlayer).toBe(Position.EAST);
      });

      it('should throw error for complete trick', () => {
        let trick = createTrick(1, Position.NORTH);
        trick = addCardToTrick(trick, aceOfSpades, Position.NORTH);
        trick = addCardToTrick(trick, kingOfSpades, Position.EAST);
        trick = addCardToTrick(trick, queenOfHearts, Position.SOUTH);
        trick = addCardToTrick(trick, jackOfClubs, Position.WEST);
        
        expect(() => getNextPlayerInTrick(trick, Position.NORTH))
          .toThrow('already complete');
      });
    });
  });

  describe('game flow utilities', () => {
    describe('countTricksWon', () => {
      it('should count tricks correctly', () => {
        const tricks: Trick[] = [
          {
            id: 1,
            leadPlayer: Position.NORTH,
            winner: Position.NORTH,
            cards: [
              { card: aceOfSpades, player: Position.NORTH },
              { card: kingOfSpades, player: Position.EAST },
              { card: queenOfHearts, player: Position.SOUTH },
              { card: jackOfClubs, player: Position.WEST }
            ]
          },
          {
            id: 2,
            leadPlayer: Position.EAST,
            winner: Position.EAST,
            cards: [
              { card: { rank: Rank.ACE, suit: Suit.HEARTS }, player: Position.EAST },
              { card: { rank: Rank.KING, suit: Suit.HEARTS }, player: Position.SOUTH },
              { card: { rank: Rank.QUEEN, suit: Suit.CLUBS }, player: Position.WEST },
              { card: { rank: Rank.JACK, suit: Suit.DIAMONDS }, player: Position.NORTH }
            ]
          }
        ];
        
        const count = countTricksWon(tricks, noTrumpContract);
        expect(count.northSouth).toBe(1); // North won trick 1
        expect(count.eastWest).toBe(1); // East won trick 2
      });
    });

    describe('getDeclaringPartnership', () => {
      it('should return correct partnership for North declarer', () => {
        const partnership = getDeclaringPartnership(spadesContract);
        expect(partnership).toBe('northSouth');
      });

      it('should return correct partnership for East declarer', () => {
        const eastContract: Contract = {
          ...spadesContract,
          declarer: Position.EAST
        };
        const partnership = getDeclaringPartnership(eastContract);
        expect(partnership).toBe('eastWest');
      });
    });

    describe('calculateContractResult', () => {
      it('should calculate made contract correctly', () => {
        // Create 10 tricks for North-South (4 Spades contract needs 10 tricks)
        const tricks: Trick[] = [];
        for (let i = 0; i < 13; i++) {
          tricks.push({
            id: i + 1,
            leadPlayer: Position.NORTH,
            winner: i < 10 ? Position.NORTH : Position.EAST, // NS wins first 10, EW wins last 3
            cards: [
              { card: aceOfSpades, player: Position.NORTH },
              { card: kingOfSpades, player: Position.EAST },
              { card: queenOfHearts, player: Position.SOUTH },
              { card: jackOfClubs, player: Position.WEST }
            ]
          });
        }
        
        const result = calculateContractResult(tricks, spadesContract);
        expect(result.made).toBe(true);
        expect(result.tricksNeeded).toBe(10);
        expect(result.tricksMade).toBe(10);
        expect(result.overtricks).toBe(0);
        expect(result.undertricks).toBe(0);
      });

      it('should calculate failed contract correctly', () => {
        // Create only 8 tricks for North-South (4 Spades contract needs 10 tricks)
        const tricks: Trick[] = [];
        for (let i = 0; i < 13; i++) {
          tricks.push({
            id: i + 1,
            leadPlayer: Position.NORTH,
            winner: i < 8 ? Position.NORTH : Position.EAST, // NS wins first 8, EW wins last 5
            cards: [
              { card: aceOfSpades, player: Position.NORTH },
              { card: kingOfSpades, player: Position.EAST },
              { card: queenOfHearts, player: Position.SOUTH },
              { card: jackOfClubs, player: Position.WEST }
            ]
          });
        }
        
        const result = calculateContractResult(tricks, spadesContract);
        expect(result.made).toBe(false);
        expect(result.tricksNeeded).toBe(10);
        expect(result.tricksMade).toBe(8);
        expect(result.overtricks).toBe(0);
        expect(result.undertricks).toBe(2);
      });
    });
  });

  describe('dummy utilities', () => {
    describe('getDummyPosition', () => {
      it('should return correct dummy for each declarer', () => {
        expect(getDummyPosition({ ...spadesContract, declarer: Position.NORTH }))
          .toBe(Position.SOUTH);
        expect(getDummyPosition({ ...spadesContract, declarer: Position.SOUTH }))
          .toBe(Position.NORTH);
        expect(getDummyPosition({ ...spadesContract, declarer: Position.EAST }))
          .toBe(Position.WEST);
        expect(getDummyPosition({ ...spadesContract, declarer: Position.WEST }))
          .toBe(Position.EAST);
      });
    });

    describe('isDummy', () => {
      it('should correctly identify dummy', () => {
        expect(isDummy(Position.SOUTH, spadesContract)).toBe(true);
        expect(isDummy(Position.NORTH, spadesContract)).toBe(false);
        expect(isDummy(Position.EAST, spadesContract)).toBe(false);
        expect(isDummy(Position.WEST, spadesContract)).toBe(false);
      });

      it('should return false when no contract', () => {
        expect(isDummy(Position.SOUTH, null)).toBe(false);
      });
    });

    describe('canDeclarerPlayFromDummy', () => {
      it('should allow declarer to play from dummy position', () => {
        // When it's dummy's turn but declarer controls dummy
        expect(canDeclarerPlayFromDummy(Position.SOUTH, spadesContract)).toBe(true);
      });

      it('should not allow non-declarer to play from dummy', () => {
        expect(canDeclarerPlayFromDummy(Position.NORTH, spadesContract)).toBe(false);
      });

      it('should return false when no contract', () => {
        expect(canDeclarerPlayFromDummy(Position.SOUTH, null)).toBe(false);
      });
    });
  });

  describe('validation utilities', () => {
    describe('validateTrickSequence', () => {
      it('should validate correct sequence', () => {
        const trick: Trick = {
          id: 1,
          leadPlayer: Position.NORTH,
          winner: Position.NORTH,
          cards: [
            { card: aceOfSpades, player: Position.NORTH },
            { card: kingOfSpades, player: Position.EAST },
            { card: queenOfHearts, player: Position.SOUTH }
          ]
        };
        
        const result = validateTrickSequence(trick, Position.NORTH);
        expect(result.isValid).toBe(true);
      });

      it('should reject wrong lead player', () => {
        const trick: Trick = {
          id: 1,
          leadPlayer: Position.EAST,
          winner: Position.EAST,
          cards: [
              { card: aceOfSpades, player: Position.NORTH },
              { card: kingOfSpades, player: Position.EAST },
              { card: queenOfHearts, player: Position.SOUTH },
              { card: jackOfClubs, player: Position.WEST }
            ]
        };
        
        const result = validateTrickSequence(trick, Position.NORTH);
        expect(result.isValid).toBe(false);
        expect(result.error).toBe('Wrong lead player');
      });

      it('should reject wrong player sequence', () => {
        const trick: Trick = {
          id: 1,
          leadPlayer: Position.NORTH,
          winner: Position.NORTH,
          cards: [
            { card: aceOfSpades, player: Position.NORTH },
            { card: kingOfSpades, player: Position.SOUTH } // Should be East
          ]
        };
        
        const result = validateTrickSequence(trick, Position.NORTH);
        expect(result.isValid).toBe(false);
        expect(result.error).toBe('Wrong player sequence');
      });
    });

    describe('validateUniqueCards', () => {
      it('should pass for unique cards', () => {
        const tricks: Trick[] = [
          {
            id: 1,
            leadPlayer: Position.NORTH,
            winner: Position.NORTH,
            cards: [
              { card: aceOfSpades, player: Position.NORTH },
              { card: kingOfSpades, player: Position.EAST }
            ]
          }
        ];
        
        const result = validateUniqueCards(tricks);
        expect(result.isValid).toBe(true);
      });

      it('should reject duplicate cards', () => {
        const tricks: Trick[] = [
          {
            id: 1,
            leadPlayer: Position.NORTH,
            winner: Position.NORTH,
            cards: [
              { card: aceOfSpades, player: Position.NORTH },
              { card: aceOfSpades, player: Position.EAST } // Duplicate
            ]
          }
        ];
        
        const result = validateUniqueCards(tricks);
        expect(result.isValid).toBe(false);
        expect(result.error).toBe('Duplicate card played');
      });
    });
  });
});

/**
 * Unit tests for gameStateUtils module
 * Tests game initialization, state transitions, and validation
 */

import {
  createNewGame,
  createPlayers,
  processBid,
  playCard,
  completeScoring,
  getCurrentPlayerHand,
  getValidCards,
  isGameComplete,
  getDummyHand,
  canSeeCards,
  validateGameState
} from './gameStateUtils';

import {
  Position,
  PlayerType,
  GamePhase,
  BidLevel,
  BidSuit,
  SpecialBid,
  Suit,
  Rank
} from '../../types/bridge';

describe('gameStateUtils', () => {
  describe('game initialization', () => {
    describe('createPlayers', () => {
      it('should create players with human and AI types', () => {
        const players = createPlayers(Position.SOUTH, 'Test Player');
        
        expect(players[Position.SOUTH].name).toBe('Test Player');
        expect(players[Position.SOUTH].type).toBe(PlayerType.HUMAN);
        expect(players[Position.NORTH].type).toBe(PlayerType.AI);
        expect(players[Position.EAST].type).toBe(PlayerType.AI);
        expect(players[Position.WEST].type).toBe(PlayerType.AI);
      });
    });

    describe('createNewGame', () => {
      it('should create a new game with correct initial state', () => {
        const players = createPlayers(Position.SOUTH);
        const gameState = createNewGame('test-game', players, Position.NORTH);
        
        expect(gameState.id).toBe('test-game');
        expect(gameState.phase).toBe(GamePhase.BIDDING);
        expect(gameState.dealer).toBe(Position.NORTH);
        expect(gameState.currentPlayer).toBe(Position.EAST); // Next after dealer
        expect(gameState.auction).toHaveLength(0);
        expect(gameState.contract).toBeNull();
        expect(gameState.tricks).toHaveLength(0);
        expect(gameState.currentTrick).toHaveLength(0);
        expect(gameState.dummy).toBeNull();
      });

      it('should deal 13 cards to each player', () => {
        const players = createPlayers(Position.SOUTH);
        const gameState = createNewGame('test-game', players);
        
        for (const position of Object.values(Position)) {
          expect(gameState.players[position].hand).toHaveLength(13);
        }
      });

      it('should validate the deal', () => {
        const players = createPlayers(Position.SOUTH);
        const gameState = createNewGame('test-game', players);
        
        const validation = validateGameState(gameState);
        expect(validation.isValid).toBe(true);
      });
    });
  });

  describe('bidding phase', () => {
    let gameState: any;

    beforeEach(() => {
      const players = createPlayers(Position.SOUTH);
      gameState = createNewGame('test-game', players, Position.NORTH);
    });

    describe('processBid', () => {
      it('should process a valid bid', () => {
        const bid = {
          player: Position.EAST,
          value: { level: BidLevel.ONE, suit: BidSuit.CLUBS },
          timestamp: new Date()
        };
        
        const newState = processBid(gameState, bid);
        
        expect(newState.auction).toHaveLength(1);
        expect(newState.auction[0]).toEqual(bid);
        expect(newState.currentPlayer).toBe(Position.SOUTH);
        expect(newState.phase).toBe(GamePhase.BIDDING);
      });

      it('should reject bid from wrong player', () => {
        const bid = {
          player: Position.SOUTH, // Wrong player
          value: { level: BidLevel.ONE, suit: BidSuit.CLUBS },
          timestamp: new Date()
        };
        
        expect(() => processBid(gameState, bid)).toThrow("It's not S's turn to bid");
      });

      it('should reject invalid bid', () => {
        // First make a valid bid
        const firstBid = {
          player: Position.EAST,
          value: { level: BidLevel.ONE, suit: BidSuit.HEARTS },
          timestamp: new Date()
        };
        gameState = processBid(gameState, firstBid);
        
        // Try to make a lower bid
        const invalidBid = {
          player: Position.SOUTH,
          value: { level: BidLevel.ONE, suit: BidSuit.CLUBS }, // Lower than 1H
          timestamp: new Date()
        };
        
        expect(() => processBid(gameState, invalidBid)).toThrow('Invalid bid');
      });

      it('should complete auction and move to playing phase', () => {
        // Create a complete auction: 1C - Pass - Pass - Pass
        const bids = [
          { player: Position.EAST, value: { level: BidLevel.ONE, suit: BidSuit.CLUBS }, timestamp: new Date() },
          { player: Position.SOUTH, value: SpecialBid.PASS, timestamp: new Date() },
          { player: Position.WEST, value: SpecialBid.PASS, timestamp: new Date() },
          { player: Position.NORTH, value: SpecialBid.PASS, timestamp: new Date() }
        ];
        
        let currentState = gameState;
        for (const bid of bids) {
          currentState = processBid(currentState, bid);
        }
        
        expect(currentState.phase).toBe(GamePhase.PLAYING);
        expect(currentState.contract).not.toBeNull();
        expect(currentState.contract!.level).toBe(BidLevel.ONE);
        expect(currentState.contract!.suit).toBe(BidSuit.CLUBS);
        expect(currentState.contract!.declarer).toBe(Position.EAST);
        expect(currentState.dummy).toBe(Position.WEST); // Partner of declarer
      });

      it('should handle passed out auction', () => {
        // All pass
        const bids = [
          { player: Position.EAST, value: SpecialBid.PASS, timestamp: new Date() },
          { player: Position.SOUTH, value: SpecialBid.PASS, timestamp: new Date() },
          { player: Position.WEST, value: SpecialBid.PASS, timestamp: new Date() },
          { player: Position.NORTH, value: SpecialBid.PASS, timestamp: new Date() }
        ];
        
        let currentState = gameState;
        for (const bid of bids) {
          currentState = processBid(currentState, bid);
        }
        
        expect(currentState.phase).toBe(GamePhase.FINISHED);
        expect(currentState.contract).toBeNull();
      });
    });
  });

  describe('playing phase', () => {
    let gameState: any;

    beforeEach(() => {
      const players = createPlayers(Position.SOUTH);
      gameState = createNewGame('test-game', players, Position.NORTH);
      
      // Complete bidding to get to playing phase
      const bids = [
        { player: Position.EAST, value: { level: BidLevel.ONE, suit: BidSuit.CLUBS }, timestamp: new Date() },
        { player: Position.SOUTH, value: SpecialBid.PASS, timestamp: new Date() },
        { player: Position.WEST, value: SpecialBid.PASS, timestamp: new Date() },
        { player: Position.NORTH, value: SpecialBid.PASS, timestamp: new Date() }
      ];
      
      for (const bid of bids) {
        gameState = processBid(gameState, bid);
      }
    });

    describe('playCard', () => {
      it('should play a valid card', () => {
        const playerHand = getCurrentPlayerHand(gameState);
        const cardToPlay = playerHand[0];
        
        const newState = playCard(gameState, cardToPlay, gameState.currentPlayer);
        
        expect(newState.currentTrick).toHaveLength(1);
        expect(newState.currentTrick[0].card).toEqual(cardToPlay);
        expect(newState.currentTrick[0].player).toBe(gameState.currentPlayer);
        expect(newState.players[gameState.currentPlayer as Position].hand).toHaveLength(12);
      });

      it('should reject card from wrong player', () => {
        const playerHand = getCurrentPlayerHand(gameState);
        const cardToPlay = playerHand[0];
        const wrongPlayer = Position.NORTH; // Not current player
        
        expect(() => playCard(gameState, cardToPlay, wrongPlayer))
          .toThrow("It's not N's turn to play");
      });

      it('should reject card not in hand', () => {
        // Create a card that's definitely not in the current player's hand
        const playerHand = getCurrentPlayerHand(gameState);
        const allSuits = [Suit.CLUBS, Suit.DIAMONDS, Suit.HEARTS, Suit.SPADES];
        const allRanks = [Rank.TWO, Rank.THREE, Rank.FOUR, Rank.FIVE, Rank.SIX, Rank.SEVEN,
                         Rank.EIGHT, Rank.NINE, Rank.TEN, Rank.JACK, Rank.QUEEN, Rank.KING, Rank.ACE];
        
        let cardNotInHand = { rank: Rank.ACE, suit: Suit.SPADES };
        
        // Find a card that's not in the player's hand
        for (const suit of allSuits) {
          for (const rank of allRanks) {
            const testCard = { rank, suit };
            if (!playerHand.some(card => card.rank === testCard.rank && card.suit === testCard.suit)) {
              cardNotInHand = testCard;
              break;
            }
          }
        }
        
        expect(() => playCard(gameState, cardNotInHand, gameState.currentPlayer))
          .toThrow('Invalid card play');
      });

      it('should complete a trick and determine winner', () => {
        let currentState = gameState;
        
        // Play 4 cards to complete a trick
        for (let i = 0; i < 4; i++) {
          const playerHand = getCurrentPlayerHand(currentState);
          const validCards = getValidCards(currentState);
          const cardToPlay = validCards[0];
          currentState = playCard(currentState, cardToPlay, currentState.currentPlayer);
        }
        
        expect(currentState.tricks).toHaveLength(1);
        expect(currentState.currentTrick).toHaveLength(0);
        expect(currentState.tricks[0].cards).toHaveLength(4);
        expect(currentState.tricks[0].winner).toBeDefined();
      });
    });

    describe('getValidCards', () => {
      it('should return all cards when leading', () => {
        const playerHand = getCurrentPlayerHand(gameState);
        const validCards = getValidCards(gameState);
        
        expect(validCards).toHaveLength(playerHand.length);
      });

      it('should return empty array during bidding phase', () => {
        const players = createPlayers(Position.SOUTH);
        const biddingState = createNewGame('test-game', players);
        
        const validCards = getValidCards(biddingState);
        expect(validCards).toHaveLength(0);
      });
    });
  });

  describe('game state queries', () => {
    let gameState: any;

    beforeEach(() => {
      const players = createPlayers(Position.SOUTH);
      gameState = createNewGame('test-game', players, Position.NORTH);
      
      // Move to playing phase
      const bids = [
        { player: Position.EAST, value: { level: BidLevel.ONE, suit: BidSuit.CLUBS }, timestamp: new Date() },
        { player: Position.SOUTH, value: SpecialBid.PASS, timestamp: new Date() },
        { player: Position.WEST, value: SpecialBid.PASS, timestamp: new Date() },
        { player: Position.NORTH, value: SpecialBid.PASS, timestamp: new Date() }
      ];
      
      for (const bid of bids) {
        gameState = processBid(gameState, bid);
      }
    });

    describe('getCurrentPlayerHand', () => {
      it('should return current player hand', () => {
        const hand = getCurrentPlayerHand(gameState);
        expect(hand).toHaveLength(13);
        expect(hand).toEqual(gameState.players[gameState.currentPlayer as Position].hand);
      });
    });

    describe('getDummyHand', () => {
      it('should return dummy hand during playing phase', () => {
        const dummyHand = getDummyHand(gameState);
        expect(dummyHand).toHaveLength(13);
        expect(dummyHand).toEqual(gameState.players[gameState.dummy!].hand);
      });

      it('should return null when no dummy', () => {
        const players = createPlayers(Position.SOUTH);
        const biddingState = createNewGame('test-game', players);
        
        const dummyHand = getDummyHand(biddingState);
        expect(dummyHand).toBeNull();
      });
    });

    describe('canSeeCards', () => {
      it('should allow players to see their own cards', () => {
        expect(canSeeCards(gameState, Position.NORTH, Position.NORTH)).toBe(true);
      });

      it('should allow all players to see dummy cards during play', () => {
        expect(canSeeCards(gameState, Position.NORTH, gameState.dummy!)).toBe(true);
        expect(canSeeCards(gameState, Position.SOUTH, gameState.dummy!)).toBe(true);
      });

      it('should not allow players to see other players cards', () => {
        expect(canSeeCards(gameState, Position.NORTH, Position.SOUTH)).toBe(false);
      });
    });

    describe('isGameComplete', () => {
      it('should return false for active game', () => {
        expect(isGameComplete(gameState)).toBe(false);
      });

      it('should return true for finished game', () => {
        const finishedState = { ...gameState, phase: GamePhase.FINISHED };
        expect(isGameComplete(finishedState)).toBe(true);
      });
    });
  });

  describe('validation', () => {
    describe('validateGameState', () => {
      it('should validate correct game state', () => {
        const players = createPlayers(Position.SOUTH);
        const gameState = createNewGame('test-game', players);
        
        const validation = validateGameState(gameState);
        expect(validation.isValid).toBe(true);
      });

      it('should detect invalid card count', () => {
        const players = createPlayers(Position.SOUTH);
        const gameState = createNewGame('test-game', players);
        
        // Remove a card to create invalid state
        gameState.players[Position.NORTH].hand.pop();
        
        const validation = validateGameState(gameState);
        expect(validation.isValid).toBe(false);
        expect(validation.error).toBe('Invalid card count');
      });

      it('should detect phase inconsistencies', () => {
        const players = createPlayers(Position.SOUTH);
        const gameState = createNewGame('test-game', players);
        
        // Set contract during bidding phase (invalid)
        gameState.contract = {
          level: BidLevel.ONE,
          suit: BidSuit.CLUBS,
          declarer: Position.NORTH,
          doubled: 'none'
        };
        
        const validation = validateGameState(gameState);
        expect(validation.isValid).toBe(false);
        expect(validation.error).toBe('Contract should be null during bidding phase');
      });
    });
  });
});

/**
 * Card utility functions for sorting, grouping, and manipulating cards
 */

import { Card, Suit, Rank } from '../../types/bridge';

/**
 * Rank order for sorting (Ace high)
 */
const RANK_ORDER: Record<Rank, number> = {
  [Rank.TWO]: 2,
  [Rank.THREE]: 3,
  [Rank.FOUR]: 4,
  [Rank.FIVE]: 5,
  [Rank.SIX]: 6,
  [Rank.SEVEN]: 7,
  [Rank.EIGHT]: 8,
  [Rank.NINE]: 9,
  [Rank.TEN]: 10,
  [Rank.JACK]: 11,
  [Rank.QUEEN]: 12,
  [Rank.KING]: 13,
  [Rank.ACE]: 14
};

/**
 * Suit order for sorting (Spades, Hearts, Diamonds, Clubs)
 */
const SUIT_ORDER: Record<Suit, number> = {
  [Suit.SPADES]: 4,
  [Suit.HEARTS]: 3,
  [Suit.DIAMONDS]: 2,
  [Suit.CLUBS]: 1
};

/**
 * Sort cards by suit and rank
 */
export function sortCards(cards: Card[], sortBy: 'suit' | 'rank' = 'suit'): Card[] {
  return [...cards].sort((a, b) => {
    if (sortBy === 'suit') {
      // First by suit (descending), then by rank (descending)
      const suitDiff = SUIT_ORDER[b.suit] - SUIT_ORDER[a.suit];
      if (suitDiff !== 0) return suitDiff;
      return RANK_ORDER[b.rank] - RANK_ORDER[a.rank];
    } else {
      // First by rank (descending), then by suit (descending)
      const rankDiff = RANK_ORDER[b.rank] - RANK_ORDER[a.rank];
      if (rankDiff !== 0) return rankDiff;
      return SUIT_ORDER[b.suit] - SUIT_ORDER[a.suit];
    }
  });
}

/**
 * Group cards by suit
 */
export function groupCardsBySuit(cards: Card[]): Record<Suit, Card[]> {
  const groups: Record<Suit, Card[]> = {
    [Suit.SPADES]: [],
    [Suit.HEARTS]: [],
    [Suit.DIAMONDS]: [],
    [Suit.CLUBS]: []
  };

  cards.forEach(card => {
    groups[card.suit].push(card);
  });

  return groups;
}

/**
 * Group cards by rank
 */
export function groupCardsByRank(cards: Card[]): Record<Rank, Card[]> {
  const groups: Partial<Record<Rank, Card[]>> = {};

  cards.forEach(card => {
    if (!groups[card.rank]) {
      groups[card.rank] = [];
    }
    groups[card.rank]!.push(card);
  });

  return groups as Record<Rank, Card[]>;
}

/**
 * Get cards of a specific suit
 */
export function getCardsOfSuit(cards: Card[], suit: Suit): Card[] {
  return cards.filter(card => card.suit === suit);
}

/**
 * Get cards of a specific rank
 */
export function getCardsOfRank(cards: Card[], rank: Rank): Card[] {
  return cards.filter(card => card.rank === rank);
}

/**
 * Check if a card is higher than another card
 */
export function isCardHigher(card1: Card, card2: Card): boolean {
  return RANK_ORDER[card1.rank] > RANK_ORDER[card2.rank];
}

/**
 * Get the highest card from a list of cards
 */
export function getHighestCard(cards: Card[]): Card | null {
  if (cards.length === 0) return null;
  
  return cards.reduce((highest, current) => 
    isCardHigher(current, highest) ? current : highest
  );
}

/**
 * Get the lowest card from a list of cards
 */
export function getLowestCard(cards: Card[]): Card | null {
  if (cards.length === 0) return null;
  
  return cards.reduce((lowest, current) => 
    isCardHigher(lowest, current) ? current : lowest
  );
}

/**
 * Count cards by suit
 */
export function countCardsBySuit(cards: Card[]): Record<Suit, number> {
  const counts: Record<Suit, number> = {
    [Suit.SPADES]: 0,
    [Suit.HEARTS]: 0,
    [Suit.DIAMONDS]: 0,
    [Suit.CLUBS]: 0
  };

  cards.forEach(card => {
    counts[card.suit]++;
  });

  return counts;
}

/**
 * Get suit distribution (e.g., "4-4-3-2")
 */
export function getSuitDistribution(cards: Card[]): string {
  const counts = Object.values(countCardsBySuit(cards));
  counts.sort((a, b) => b - a); // Sort descending
  return counts.join('-');
}

/**
 * Check if hand is balanced (4-3-3-3, 4-4-3-2, or 5-3-3-2)
 */
export function isBalancedHand(cards: Card[]): boolean {
  const distribution = getSuitDistribution(cards);
  return ['4-3-3-3', '4-4-3-2', '5-3-3-2'].includes(distribution);
}

/**
 * Get honor cards (A, K, Q, J, 10)
 */
export function getHonorCards(cards: Card[]): Card[] {
  const honors = [Rank.ACE, Rank.KING, Rank.QUEEN, Rank.JACK, Rank.TEN];
  return cards.filter(card => honors.includes(card.rank));
}

/**
 * Count honor points (A=4, K=3, Q=2, J=1)
 */
export function countHonorPoints(cards: Card[]): number {
  const points: Record<Rank, number> = {
    [Rank.ACE]: 4,
    [Rank.KING]: 3,
    [Rank.QUEEN]: 2,
    [Rank.JACK]: 1,
    [Rank.TEN]: 0,
    [Rank.NINE]: 0,
    [Rank.EIGHT]: 0,
    [Rank.SEVEN]: 0,
    [Rank.SIX]: 0,
    [Rank.FIVE]: 0,
    [Rank.FOUR]: 0,
    [Rank.THREE]: 0,
    [Rank.TWO]: 0
  };

  return cards.reduce((total, card) => total + points[card.rank], 0);
}

/**
 * Check if cards contain a specific card
 */
export function containsCard(cards: Card[], targetCard: Card): boolean {
  return cards.some(card => 
    card.suit === targetCard.suit && card.rank === targetCard.rank
  );
}

/**
 * Remove a specific card from a list of cards
 */
export function removeCard(cards: Card[], targetCard: Card): Card[] {
  return cards.filter(card => 
    !(card.suit === targetCard.suit && card.rank === targetCard.rank)
  );
}

/**
 * Get longest suit in hand
 */
export function getLongestSuit(cards: Card[]): Suit | null {
  const counts = countCardsBySuit(cards);
  let longestSuit: Suit | null = null;
  let maxCount = 0;

  Object.entries(counts).forEach(([suit, count]) => {
    if (count > maxCount) {
      maxCount = count;
      longestSuit = suit as Suit;
    }
  });

  return longestSuit;
}

/**
 * Get shortest suit in hand
 */
export function getShortestSuit(cards: Card[]): Suit | null {
  const counts = countCardsBySuit(cards);
  let shortestSuit: Suit | null = null;
  let minCount = Infinity;

  Object.entries(counts).forEach(([suit, count]) => {
    if (count < minCount) {
      minCount = count;
      shortestSuit = suit as Suit;
    }
  });

  return shortestSuit;
}

/**
 * Check if hand has a void (no cards in a suit)
 */
export function hasVoid(cards: Card[]): boolean {
  const counts = countCardsBySuit(cards);
  return Object.values(counts).some(count => count === 0);
}

/**
 * Check if hand has a singleton (exactly one card in a suit)
 */
export function hasSingleton(cards: Card[]): boolean {
  const counts = countCardsBySuit(cards);
  return Object.values(counts).some(count => count === 1);
}

/**
 * Check if hand has a doubleton (exactly two cards in a suit)
 */
export function hasDoubleton(cards: Card[]): boolean {
  const counts = countCardsBySuit(cards);
  return Object.values(counts).some(count => count === 2);
}

/**
 * Format card for display
 */
export function formatCard(card: Card): string {
  const suitSymbols: Record<Suit, string> = {
    [Suit.SPADES]: '♠',
    [Suit.HEARTS]: '♥',
    [Suit.DIAMONDS]: '♦',
    [Suit.CLUBS]: '♣'
  };

  const rankSymbols: Record<Rank, string> = {
    [Rank.ACE]: 'A',
    [Rank.KING]: 'K',
    [Rank.QUEEN]: 'Q',
    [Rank.JACK]: 'J',
    [Rank.TEN]: '10',
    [Rank.NINE]: '9',
    [Rank.EIGHT]: '8',
    [Rank.SEVEN]: '7',
    [Rank.SIX]: '6',
    [Rank.FIVE]: '5',
    [Rank.FOUR]: '4',
    [Rank.THREE]: '3',
    [Rank.TWO]: '2'
  };

  return `${rankSymbols[card.rank]}${suitSymbols[card.suit]}`;
}

/**
 * Format multiple cards for display
 */
export function formatCards(cards: Card[]): string {
  return cards.map(formatCard).join(' ');
}


/**
 * Get numeric value of a rank for comparison
 */
export function getRankValue(rank: Rank): number {
  return RANK_ORDER[rank];
}

/**
 * Check if two cards are equal
 */
export function cardsEqual(card1: Card, card2: Card): boolean {
  return card1.suit === card2.suit && card1.rank === card2.rank;
}

/**
 * Check if hand contains a specific card
 */
export function handContainsCard(hand: Card[], targetCard: Card): boolean {
  return containsCard(hand, targetCard);
}

/**
 * Sort hand using the default bridge sorting
 */
export function sortHand(cards: Card[]): Card[] {
  return sortCards(cards, "suit");
}

/**
 * Create a standard 52-card deck
 */
export function createDeck(): Card[] {
  const deck: Card[] = [];
  const suits = [Suit.SPADES, Suit.HEARTS, Suit.DIAMONDS, Suit.CLUBS];
  const ranks = [
    Rank.TWO, Rank.THREE, Rank.FOUR, Rank.FIVE, Rank.SIX, Rank.SEVEN,
    Rank.EIGHT, Rank.NINE, Rank.TEN, Rank.JACK, Rank.QUEEN, Rank.KING, Rank.ACE
  ];

  suits.forEach(suit => {
    ranks.forEach(rank => {
      deck.push({ suit, rank });
    });
  });

  return deck;
}

/**
 * Shuffle a deck of cards using Fisher-Yates algorithm
 */
export function shuffleDeck(deck: Card[]): Card[] {
  const shuffled = [...deck];
  
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
  }
  
  return shuffled;
}

/**
 * Deal cards to players from a deck
 */
export function dealCards(deck: Card[], numPlayers: number = 4, cardsPerPlayer: number = 13): Card[][] {
  const hands: Card[][] = Array.from({ length: numPlayers }, () => []);
  
  for (let i = 0; i < cardsPerPlayer * numPlayers; i++) {
    const playerIndex = i % numPlayers;
    hands[playerIndex].push(deck[i]);
  }
  
  return hands;
}

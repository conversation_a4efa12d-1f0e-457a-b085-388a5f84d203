/**
 * Trick-taking logic and utilities for Bridge game
 * Handles trick validation, winner determination, and play sequence
 */

import {
  Card,
  Suit,
  Position,
  PlayedCard,
  Trick,
  Contract,
  Hand,
  ValidationResult,
  getNextPosition,
  TOTAL_TRICKS
} from '../../types/bridge';

import {
  cardsEqual,
  getRankValue,
  handContainsCard,
  getCardsOfSuit
} from './cardUtils';

// ============================================================================
// TRICK VALIDATION
// ============================================================================

/**
 * Validates that a card can be legally played in the current trick
 */
export function validateCardPlay(
  card: Card,
  playerHand: Hand,
  currentTrick: PlayedCard[],
  contract: Contract | null
): ValidationResult {
  // Check if player has the card
  if (!handContainsCard(playerHand, card)) {
    return {
      isValid: false,
      error: 'Card not in hand',
      details: `Player does not have ${card.rank}${card.suit}`
    };
  }

  // If this is the first card of the trick, any card is valid
  if (currentTrick.length === 0) {
    return { isValid: true };
  }

  const leadSuit = currentTrick[0].card.suit;
  const cardSuit = card.suit;

  // If following suit, the play is valid
  if (cardSuit === leadSuit) {
    return { isValid: true };
  }

  // If not following suit, check if player has cards of the lead suit
  const cardsOfLeadSuit = getCardsOfSuit(playerHand, leadSuit);
  if (cardsOfLeadSuit.length > 0) {
    return {
      isValid: false,
      error: 'Must follow suit',
      details: `Must play a ${leadSuit} if available`
    };
  }

  // Player is void in the lead suit, so any card is valid
  return { isValid: true };
}

/**
 * Determines the winner of a completed trick
 */
export function getTrickWinner(
  trick: PlayedCard[],
  contract: Contract | null
): Position {
  if (trick.length !== 4) {
    throw new Error('Trick must have exactly 4 cards to determine winner');
  }

  const leadSuit = trick[0].card.suit;
  const trumpSuit = getTrumpSuit(contract);

  let winningCard = trick[0];
  let winner = trick[0].player;

  for (let i = 1; i < trick.length; i++) {
    const currentCard = trick[i];

    if (isCardHigher(currentCard.card, winningCard.card, leadSuit, trumpSuit)) {
      winningCard = currentCard;
      winner = currentCard.player;
    }
  }

  return winner;
}

/**
 * Gets the trump suit from a contract, handling NT properly
 */
function getTrumpSuit(contract: Contract | null): Suit | null {
  if (!contract || contract.suit === 'NT') {
    return null;
  }

  // Convert BidSuit to Suit
  switch (contract.suit) {
    case 'C': return Suit.CLUBS;
    case 'D': return Suit.DIAMONDS;
    case 'H': return Suit.HEARTS;
    case 'S': return Suit.SPADES;
    default: return null;
  }
}

/**
 * Determines if card1 beats card2 in the context of a trick
 */
function isCardHigher(
  card1: Card,
  card2: Card,
  leadSuit: Suit,
  trumpSuit: Suit | null
): boolean {
  const card1IsTrump = trumpSuit !== null && card1.suit === trumpSuit;
  const card2IsTrump = trumpSuit !== null && card2.suit === trumpSuit;
  const card1FollowsSuit = card1.suit === leadSuit;
  const card2FollowsSuit = card2.suit === leadSuit;

  // Trump beats non-trump
  if (card1IsTrump && !card2IsTrump) {
    return true;
  }
  if (card2IsTrump && !card1IsTrump) {
    return false;
  }

  // Both trump: higher rank wins
  if (card1IsTrump && card2IsTrump) {
    return getRankValue(card1.rank) > getRankValue(card2.rank);
  }

  // Neither trump: only cards following suit can win
  if (!card1FollowsSuit && card2FollowsSuit) {
    return false;
  }
  if (card1FollowsSuit && !card2FollowsSuit) {
    return true;
  }

  // Both follow suit (or both don't): higher rank wins
  if (card1FollowsSuit && card2FollowsSuit) {
    return getRankValue(card1.rank) > getRankValue(card2.rank);
  }

  // Neither follows suit: first card wins (card2 was winning)
  return false;
}

// ============================================================================
// TRICK MANAGEMENT
// ============================================================================

/**
 * Creates a new trick with the given ID and lead player
 */
export function createTrick(id: number, leadPlayer: Position): Trick {
  return {
    id,
    cards: [],
    winner: leadPlayer, // Will be updated when trick is complete
    leadPlayer
  };
}

/**
 * Adds a card to a trick
 */
export function addCardToTrick(
  trick: Trick,
  card: Card,
  player: Position
): Trick {
  if (trick.cards.length >= 4) {
    throw new Error('Trick already has 4 cards');
  }

  const newTrick: Trick = {
    ...trick,
    cards: [...trick.cards, { card, player }]
  };

  // If trick is now complete, determine the winner
  if (newTrick.cards.length === 4) {
    // Note: This requires contract information to determine trump
    // The caller should call getTrickWinner separately with contract info
  }

  return newTrick;
}

/**
 * Checks if a trick is complete (has 4 cards)
 */
export function isTrickComplete(trick: Trick): boolean {
  return trick.cards.length === 4;
}

/**
 * Gets the next player to play in a trick
 */
export function getNextPlayerInTrick(
  trick: Trick,
  leadPlayer: Position
): Position {
  const cardsPlayed = trick.cards.length;
  
  if (cardsPlayed >= 4) {
    throw new Error('Trick is already complete');
  }

  let currentPlayer = leadPlayer;
  for (let i = 0; i < cardsPlayed; i++) {
    currentPlayer = getNextPosition(currentPlayer);
  }

  return currentPlayer;
}

// ============================================================================
// GAME FLOW UTILITIES
// ============================================================================

/**
 * Determines who leads the next trick
 */
export function getNextTrickLeader(
  completedTrick: Trick,
  contract: Contract | null
): Position {
  if (!isTrickComplete(completedTrick)) {
    throw new Error('Cannot determine next leader from incomplete trick');
  }

  return getTrickWinner(completedTrick.cards, contract);
}

/**
 * Checks if all tricks have been played
 */
export function areAllTricksComplete(tricks: Trick[]): boolean {
  return tricks.length === TOTAL_TRICKS && 
         tricks.every(trick => isTrickComplete(trick));
}

/**
 * Counts tricks won by each partnership
 */
export function countTricksWon(
  tricks: Trick[],
  contract: Contract | null
): { northSouth: number; eastWest: number } {
  let northSouth = 0;
  let eastWest = 0;

  for (const trick of tricks) {
    if (!isTrickComplete(trick)) {
      continue; // Skip incomplete tricks
    }

    const winner = trick.winner;
    
    if (winner === Position.NORTH || winner === Position.SOUTH) {
      northSouth++;
    } else {
      eastWest++;
    }
  }

  return { northSouth, eastWest };
}

/**
 * Determines the declaring partnership from a contract
 */
export function getDeclaringPartnership(contract: Contract): 'northSouth' | 'eastWest' {
  return (contract.declarer === Position.NORTH || contract.declarer === Position.SOUTH)
    ? 'northSouth'
    : 'eastWest';
}

/**
 * Calculates how many tricks the declaring side made vs their contract
 */
export function calculateContractResult(
  tricks: Trick[],
  contract: Contract
): {
  tricksNeeded: number;
  tricksMade: number;
  overtricks: number;
  undertricks: number;
  made: boolean;
} {
  const tricksWon = countTricksWon(tricks, contract);
  const declaringPartnership = getDeclaringPartnership(contract);
  const tricksMade = tricksWon[declaringPartnership];
  const tricksNeeded = 6 + contract.level; // Book (6) + bid level

  const made = tricksMade >= tricksNeeded;
  const overtricks = made ? tricksMade - tricksNeeded : 0;
  const undertricks = made ? 0 : tricksNeeded - tricksMade;

  return {
    tricksNeeded,
    tricksMade,
    overtricks,
    undertricks,
    made
  };
}

// ============================================================================
// DUMMY PLAY UTILITIES
// ============================================================================

/**
 * Determines the dummy position based on the contract
 */
export function getDummyPosition(contract: Contract): Position {
  // Dummy is the partner of the declarer
  switch (contract.declarer) {
    case Position.NORTH: return Position.SOUTH;
    case Position.SOUTH: return Position.NORTH;
    case Position.EAST: return Position.WEST;
    case Position.WEST: return Position.EAST;
  }
}

/**
 * Checks if a position is the dummy
 */
export function isDummy(position: Position, contract: Contract | null): boolean {
  if (!contract) return false;
  return position === getDummyPosition(contract);
}

/**
 * Checks if the declarer can play from dummy's hand
 */
export function canDeclarerPlayFromDummy(
  currentPlayer: Position,
  contract: Contract | null
): boolean {
  if (!contract) return false;
  
  const dummy = getDummyPosition(contract);
  return currentPlayer === dummy && contract.declarer !== dummy;
}

// ============================================================================
// VALIDATION UTILITIES
// ============================================================================

/**
 * Validates the sequence of cards played in a trick
 */
export function validateTrickSequence(
  trick: Trick,
  expectedLeadPlayer: Position
): ValidationResult {
  if (trick.leadPlayer !== expectedLeadPlayer) {
    return {
      isValid: false,
      error: 'Wrong lead player',
      details: `Expected ${expectedLeadPlayer} to lead, got ${trick.leadPlayer}`
    };
  }

  let expectedPlayer = trick.leadPlayer;
  for (let i = 0; i < trick.cards.length; i++) {
    const playedCard = trick.cards[i];
    
    if (playedCard.player !== expectedPlayer) {
      return {
        isValid: false,
        error: 'Wrong player sequence',
        details: `Expected ${expectedPlayer} to play card ${i + 1}, got ${playedCard.player}`
      };
    }
    
    expectedPlayer = getNextPosition(expectedPlayer);
  }

  return { isValid: true };
}

/**
 * Validates that all cards in tricks are unique
 */
export function validateUniqueCards(tricks: Trick[]): ValidationResult {
  const playedCards = new Set<string>();
  
  for (const trick of tricks) {
    for (const playedCard of trick.cards) {
      const cardString = `${playedCard.card.rank}${playedCard.card.suit}`;
      
      if (playedCards.has(cardString)) {
        return {
          isValid: false,
          error: 'Duplicate card played',
          details: `Card ${cardString} was played more than once`
        };
      }
      
      playedCards.add(cardString);
    }
  }

  return { isValid: true };
}

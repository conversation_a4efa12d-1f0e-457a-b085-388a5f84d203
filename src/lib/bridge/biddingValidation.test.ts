/**
 * Tests for comprehensive bidding validation
 * Verifies Bridge rules for bid ordering, doubles/redoubles, and contract determination
 */

import {
  createNewGame,
  createPlayers,
  processBid
} from './gameStateUtils';

import {
  isValidBid,
  isAuctionComplete,
  getFinalContract
} from './biddingUtils';

import {
  Position,
  GamePhase,
  BidLevel,
  BidSuit,
  SpecialBid
} from '../../types/bridge';

describe('Bidding Validation', () => {
  let gameState: any;

  beforeEach(() => {
    const players = createPlayers(Position.SOUTH);
    gameState = createNewGame('test-game', players, Position.NORTH);
  });

  describe('Bid Ordering Validation', () => {
    it('should accept higher level bids', () => {
      const auction = [
        { player: Position.EAST, value: { level: BidLevel.ONE, suit: BidSuit.CLUBS }, timestamp: new Date() }
      ];
      
      const higherBid = {
        player: Position.SOUTH,
        value: { level: BidLevel.TWO, suit: BidSuit.CLUBS },
        timestamp: new Date()
      };
      
      expect(isValidBid(auction, higherBid)).toBe(true);
    });

    it('should accept same level bid with higher suit', () => {
      const auction = [
        { player: Position.EAST, value: { level: BidLevel.ONE, suit: BidSuit.CLUBS }, timestamp: new Date() }
      ];
      
      const higherSuitBid = {
        player: Position.SOUTH,
        value: { level: BidLevel.ONE, suit: BidSuit.SPADES },
        timestamp: new Date()
      };
      
      expect(isValidBid(auction, higherSuitBid)).toBe(true);
    });

    it('should reject lower bids', () => {
      const auction = [
        { player: Position.EAST, value: { level: BidLevel.TWO, suit: BidSuit.HEARTS }, timestamp: new Date() }
      ];
      
      const lowerBid = {
        player: Position.SOUTH,
        value: { level: BidLevel.ONE, suit: BidSuit.SPADES },
        timestamp: new Date()
      };
      
      expect(isValidBid(auction, lowerBid)).toBe(false);
    });

    it('should reject same level bid with lower suit', () => {
      const auction = [
        { player: Position.EAST, value: { level: BidLevel.ONE, suit: BidSuit.HEARTS }, timestamp: new Date() }
      ];
      
      const lowerSuitBid = {
        player: Position.SOUTH,
        value: { level: BidLevel.ONE, suit: BidSuit.CLUBS },
        timestamp: new Date()
      };
      
      expect(isValidBid(auction, lowerSuitBid)).toBe(false);
    });
  });

  describe('Double/Redouble Logic', () => {
    it('should allow doubling opponent bids', () => {
      const auction = [
        { player: Position.EAST, value: { level: BidLevel.ONE, suit: BidSuit.CLUBS }, timestamp: new Date() }
      ];
      
      const doubleBid = {
        player: Position.SOUTH, // Opponent of East
        value: SpecialBid.DOUBLE,
        timestamp: new Date()
      };
      
      expect(isValidBid(auction, doubleBid)).toBe(true);
    });

    it('should reject doubling partner bids', () => {
      const auction = [
        { player: Position.NORTH, value: { level: BidLevel.ONE, suit: BidSuit.CLUBS }, timestamp: new Date() }
      ];
      
      const doubleBid = {
        player: Position.SOUTH, // Partner of North
        value: SpecialBid.DOUBLE,
        timestamp: new Date()
      };
      
      expect(isValidBid(auction, doubleBid)).toBe(false);
    });

    it('should allow redoubling after being doubled', () => {
      const auction = [
        { player: Position.NORTH, value: { level: BidLevel.ONE, suit: BidSuit.CLUBS }, timestamp: new Date() },
        { player: Position.EAST, value: SpecialBid.DOUBLE, timestamp: new Date() }
      ];
      
      const redoubleBid = {
        player: Position.SOUTH, // Partner of North who made the bid
        value: SpecialBid.REDOUBLE,
        timestamp: new Date()
      };
      
      expect(isValidBid(auction, redoubleBid)).toBe(true);
    });

    it('should reject redoubling without a double', () => {
      const auction = [
        { player: Position.NORTH, value: { level: BidLevel.ONE, suit: BidSuit.CLUBS }, timestamp: new Date() }
      ];
      
      const redoubleBid = {
        player: Position.SOUTH,
        value: SpecialBid.REDOUBLE,
        timestamp: new Date()
      };
      
      expect(isValidBid(auction, redoubleBid)).toBe(false);
    });
  });

  describe('Auction Completion', () => {
    it('should complete auction after 3 passes following a contract bid', () => {
      const auction = [
        { player: Position.EAST, value: { level: BidLevel.ONE, suit: BidSuit.CLUBS }, timestamp: new Date() },
        { player: Position.SOUTH, value: SpecialBid.PASS, timestamp: new Date() },
        { player: Position.WEST, value: SpecialBid.PASS, timestamp: new Date() },
        { player: Position.NORTH, value: SpecialBid.PASS, timestamp: new Date() }
      ];
      
      expect(isAuctionComplete(auction)).toBe(true);
    });

    it('should complete auction after 4 passes (all pass)', () => {
      const auction = [
        { player: Position.EAST, value: SpecialBid.PASS, timestamp: new Date() },
        { player: Position.SOUTH, value: SpecialBid.PASS, timestamp: new Date() },
        { player: Position.WEST, value: SpecialBid.PASS, timestamp: new Date() },
        { player: Position.NORTH, value: SpecialBid.PASS, timestamp: new Date() }
      ];
      
      expect(isAuctionComplete(auction)).toBe(true);
    });

    it('should not complete auction with only 2 passes after contract', () => {
      const auction = [
        { player: Position.EAST, value: { level: BidLevel.ONE, suit: BidSuit.CLUBS }, timestamp: new Date() },
        { player: Position.SOUTH, value: SpecialBid.PASS, timestamp: new Date() },
        { player: Position.WEST, value: SpecialBid.PASS, timestamp: new Date() }
      ];
      
      expect(isAuctionComplete(auction)).toBe(false);
    });
  });

  describe('Contract Determination', () => {
    it('should determine correct contract from completed auction', () => {
      const auction = [
        { player: Position.EAST, value: { level: BidLevel.ONE, suit: BidSuit.SPADES }, timestamp: new Date() },
        { player: Position.SOUTH, value: SpecialBid.PASS, timestamp: new Date() },
        { player: Position.WEST, value: SpecialBid.PASS, timestamp: new Date() },
        { player: Position.NORTH, value: SpecialBid.PASS, timestamp: new Date() }
      ];
      
      const contract = getFinalContract(auction);
      expect(contract).not.toBeNull();
      expect(contract!.level).toBe(BidLevel.ONE);
      expect(contract!.suit).toBe(BidSuit.SPADES);
      expect(contract!.declarer).toBe(Position.EAST);
      expect(contract!.doubled).toBe('none');
    });

    it('should find correct declarer (first in partnership to bid strain)', () => {
      const auction = [
        { player: Position.NORTH, value: { level: BidLevel.ONE, suit: BidSuit.HEARTS }, timestamp: new Date() },
        { player: Position.EAST, value: SpecialBid.PASS, timestamp: new Date() },
        { player: Position.SOUTH, value: { level: BidLevel.TWO, suit: BidSuit.HEARTS }, timestamp: new Date() },
        { player: Position.WEST, value: SpecialBid.PASS, timestamp: new Date() },
        { player: Position.NORTH, value: SpecialBid.PASS, timestamp: new Date() },
        { player: Position.EAST, value: SpecialBid.PASS, timestamp: new Date() }
      ];
      
      const contract = getFinalContract(auction);
      expect(contract).not.toBeNull();
      expect(contract!.declarer).toBe(Position.NORTH); // First to bid hearts in N-S partnership
    });

    it('should handle doubled contracts', () => {
      const auction = [
        { player: Position.EAST, value: { level: BidLevel.ONE, suit: BidSuit.CLUBS }, timestamp: new Date() },
        { player: Position.SOUTH, value: SpecialBid.DOUBLE, timestamp: new Date() },
        { player: Position.WEST, value: SpecialBid.PASS, timestamp: new Date() },
        { player: Position.NORTH, value: SpecialBid.PASS, timestamp: new Date() }
      ];
      
      const contract = getFinalContract(auction);
      expect(contract).not.toBeNull();
      expect(contract!.doubled).toBe('doubled');
    });

    it('should return null for all-pass auctions', () => {
      const auction = [
        { player: Position.EAST, value: SpecialBid.PASS, timestamp: new Date() },
        { player: Position.SOUTH, value: SpecialBid.PASS, timestamp: new Date() },
        { player: Position.WEST, value: SpecialBid.PASS, timestamp: new Date() },
        { player: Position.NORTH, value: SpecialBid.PASS, timestamp: new Date() }
      ];
      
      const contract = getFinalContract(auction);
      expect(contract).toBeNull();
    });
  });

  describe('Game State Integration', () => {
    it('should transition to playing phase with correct contract', () => {
      let currentState = gameState;
      
      const bids = [
        { player: Position.EAST, value: { level: BidLevel.ONE, suit: BidSuit.SPADES }, timestamp: new Date() },
        { player: Position.SOUTH, value: SpecialBid.PASS, timestamp: new Date() },
        { player: Position.WEST, value: SpecialBid.PASS, timestamp: new Date() },
        { player: Position.NORTH, value: SpecialBid.PASS, timestamp: new Date() }
      ];
      
      for (const bid of bids) {
        currentState = processBid(currentState, bid);
      }
      
      expect(currentState.phase).toBe(GamePhase.PLAYING);
      expect(currentState.contract).not.toBeNull();
      expect(currentState.contract!.level).toBe(BidLevel.ONE);
      expect(currentState.contract!.suit).toBe(BidSuit.SPADES);
      expect(currentState.contract!.declarer).toBe(Position.EAST);
      expect(currentState.dummy).toBe(Position.WEST); // Partner of declarer
    });

    it('should set correct opening leader (left of declarer)', () => {
      let currentState = gameState;
      
      const bids = [
        { player: Position.EAST, value: { level: BidLevel.ONE, suit: BidSuit.CLUBS }, timestamp: new Date() },
        { player: Position.SOUTH, value: SpecialBid.PASS, timestamp: new Date() },
        { player: Position.WEST, value: SpecialBid.PASS, timestamp: new Date() },
        { player: Position.NORTH, value: SpecialBid.PASS, timestamp: new Date() }
      ];
      
      for (const bid of bids) {
        currentState = processBid(currentState, bid);
      }
      
      // East is declarer, so South (left of East) should lead
      expect(currentState.currentPlayer).toBe(Position.SOUTH);
    });
  });
});

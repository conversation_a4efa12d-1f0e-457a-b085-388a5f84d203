/**
 * Game initialization tests - verify AI players get cards
 */

import { createNewGame, createPlayers } from './gameStateUtils';
import { Position, PlayerType } from '../../types/bridge';

describe('Game Initialization', () => {
  it('should create AI players with full hands of 13 cards each', () => {
    const players = createPlayers(Position.SOUTH, 'Human Player');
    const gameState = createNewGame('test-game', players, Position.NORTH);
    
    // Verify all players exist
    expect(gameState.players[Position.NORTH]).toBeDefined();
    expect(gameState.players[Position.EAST]).toBeDefined();
    expect(gameState.players[Position.SOUTH]).toBeDefined();
    expect(gameState.players[Position.WEST]).toBeDefined();
    
    // Verify AI players have cards
    expect(gameState.players[Position.NORTH].hand).toBeDefined();
    expect(gameState.players[Position.NORTH].hand.length).toBe(13);
    expect(gameState.players[Position.NORTH].type).toBe(PlayerType.AI);
    
    expect(gameState.players[Position.EAST].hand).toBeDefined();
    expect(gameState.players[Position.EAST].hand.length).toBe(13);
    expect(gameState.players[Position.EAST].type).toBe(PlayerType.AI);
    
    expect(gameState.players[Position.WEST].hand).toBeDefined();
    expect(gameState.players[Position.WEST].hand.length).toBe(13);
    expect(gameState.players[Position.WEST].type).toBe(PlayerType.AI);
    
    // Verify human player has cards
    expect(gameState.players[Position.SOUTH].hand).toBeDefined();
    expect(gameState.players[Position.SOUTH].hand.length).toBe(13);
    expect(gameState.players[Position.SOUTH].type).toBe(PlayerType.HUMAN);
    
    // Verify total cards dealt is 52
    const totalCards = Object.values(gameState.players)
      .reduce((sum, player) => sum + player.hand.length, 0);
    expect(totalCards).toBe(52);
  });

  it('should create unique cards for each player', () => {
    const players = createPlayers(Position.SOUTH, 'Human Player');
    const gameState = createNewGame('test-game', players, Position.NORTH);
    
    // Collect all cards from all players
    const allCards = Object.values(gameState.players)
      .flatMap(player => player.hand);
    
    // Verify we have 52 unique cards
    expect(allCards.length).toBe(52);
    
    // Check for duplicates by creating a set of card strings
    const cardStrings = allCards.map(card => `${card.rank}-${card.suit}`);
    const uniqueCardStrings = new Set(cardStrings);
    expect(uniqueCardStrings.size).toBe(52);
  });

  it('should assign correct player types', () => {
    const players = createPlayers(Position.SOUTH, 'Test Human');
    const gameState = createNewGame('test-game', players, Position.NORTH);
    
    expect(gameState.players[Position.NORTH].type).toBe(PlayerType.AI);
    expect(gameState.players[Position.EAST].type).toBe(PlayerType.AI);
    expect(gameState.players[Position.SOUTH].type).toBe(PlayerType.HUMAN);
    expect(gameState.players[Position.WEST].type).toBe(PlayerType.AI);
    
    expect(gameState.players[Position.SOUTH].name).toBe('Test Human');
  });
});

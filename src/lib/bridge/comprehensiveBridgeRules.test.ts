/**
 * Comprehensive Bridge Rules Test Suite
 * Tests all Bridge rules including edge cases and rule violations
 */

import {
  createNewGame,
  createPlayers,
  processBid,
  playCard,
  processScoring,
  validateGameState,
  getValidCards,
  isDummyVisible,
  shouldDeclarerControlDummy
} from './gameStateUtils';

import {
  isValidBid,
  isAuctionComplete,
  getFinalContract
} from './biddingUtils';

import {
  validateCardPlay,
  getTrickWinner
} from './trickUtils';

import {
  calculateContractScore,
  calculateDetailedScore
} from './scoringUtils';

import {
  Position,
  GamePhase,
  BidLevel,
  BidSuit,
  SpecialBid,
  Suit,
  Rank,
  Contract
} from '../../types/bridge';

describe('Comprehensive Bridge Rules Test Suite', () => {
  let gameState: any;

  beforeEach(() => {
    const players = createPlayers(Position.SOUTH);
    gameState = createNewGame('test-game', players, Position.NORTH);
  });

  describe('Card Play Rules - Edge Cases', () => {
    beforeEach(() => {
      // Set up playing phase
      const bids = [
        { player: Position.EAST, value: { level: BidLevel.ONE, suit: BidSuit.SPADES }, timestamp: new Date() },
        { player: Position.SOUTH, value: SpecialBid.PASS, timestamp: new Date() },
        { player: Position.WEST, value: SpecialBid.PASS, timestamp: new Date() },
        { player: Position.NORTH, value: SpecialBid.PASS, timestamp: new Date() }
      ];
      
      for (const bid of bids) {
        gameState = processBid(gameState, bid);
      }
    });

    it('should enforce suit following when player has cards in led suit', () => {
      // Set up specific hand
      gameState.players[Position.SOUTH].hand = [
        { suit: Suit.HEARTS, rank: Rank.ACE },
        { suit: Suit.HEARTS, rank: Rank.KING },
        { suit: Suit.CLUBS, rank: Rank.QUEEN }
      ];

      // Lead with hearts
      gameState.currentTrick = [
        { card: { suit: Suit.HEARTS, rank: Rank.TEN }, player: Position.NORTH }
      ];
      gameState.currentPlayer = Position.SOUTH;

      // Should only be able to play hearts
      const validCards = getValidCards(gameState);
      expect(validCards).toHaveLength(2);
      expect(validCards.every(card => card.suit === Suit.HEARTS)).toBe(true);

      // Should reject playing clubs
      expect(() => {
        playCard(gameState, { suit: Suit.CLUBS, rank: Rank.QUEEN }, Position.SOUTH);
      }).toThrow();
    });

    it('should allow any card when void in led suit', () => {
      // Set up void hand
      gameState.players[Position.SOUTH].hand = [
        { suit: Suit.CLUBS, rank: Rank.ACE },
        { suit: Suit.DIAMONDS, rank: Rank.KING },
        { suit: Suit.SPADES, rank: Rank.QUEEN }
      ];

      // Lead with hearts (player is void)
      gameState.currentTrick = [
        { card: { suit: Suit.HEARTS, rank: Rank.TEN }, player: Position.NORTH }
      ];
      gameState.currentPlayer = Position.SOUTH;

      // Should be able to play any card
      const validCards = getValidCards(gameState);
      expect(validCards).toHaveLength(3);

      // Should accept playing any suit
      expect(() => {
        playCard(gameState, { suit: Suit.CLUBS, rank: Rank.ACE }, Position.SOUTH);
      }).not.toThrow();
    });

    it('should handle trump correctly in suit contracts', () => {
      // Spades is trump
      const trick = [
        { card: { suit: Suit.HEARTS, rank: Rank.ACE }, player: Position.NORTH },
        { card: { suit: Suit.HEARTS, rank: Rank.KING }, player: Position.EAST },
        { card: { suit: Suit.SPADES, rank: Rank.TWO }, player: Position.SOUTH }, // Trump
        { card: { suit: Suit.HEARTS, rank: Rank.QUEEN }, player: Position.WEST }
      ];

      const winner = getTrickWinner(trick, gameState.contract);
      expect(winner).toBe(Position.SOUTH); // Trump wins
    });

    it('should handle no-trump contracts correctly', () => {
      // Change to no-trump
      gameState.contract.suit = BidSuit.NO_TRUMP;

      const trick = [
        { card: { suit: Suit.HEARTS, rank: Rank.KING }, player: Position.NORTH },
        { card: { suit: Suit.HEARTS, rank: Rank.ACE }, player: Position.EAST }, // Highest
        { card: { suit: Suit.SPADES, rank: Rank.ACE }, player: Position.SOUTH }, // Different suit
        { card: { suit: Suit.HEARTS, rank: Rank.QUEEN }, player: Position.WEST }
      ];

      const winner = getTrickWinner(trick, gameState.contract);
      expect(winner).toBe(Position.EAST); // Highest card of led suit
    });
  });

  describe('Bidding Rules - Complex Scenarios', () => {
    it('should handle competitive bidding sequences', () => {
      const bids = [
        { player: Position.EAST, value: { level: BidLevel.ONE, suit: BidSuit.CLUBS }, timestamp: new Date() },
        { player: Position.SOUTH, value: { level: BidLevel.ONE, suit: BidSuit.HEARTS }, timestamp: new Date() },
        { player: Position.WEST, value: { level: BidLevel.TWO, suit: BidSuit.CLUBS }, timestamp: new Date() },
        { player: Position.NORTH, value: { level: BidLevel.TWO, suit: BidSuit.HEARTS }, timestamp: new Date() },
        { player: Position.EAST, value: { level: BidLevel.THREE, suit: BidSuit.CLUBS }, timestamp: new Date() },
        { player: Position.SOUTH, value: SpecialBid.DOUBLE, timestamp: new Date() },
        { player: Position.WEST, value: SpecialBid.REDOUBLE, timestamp: new Date() },
        { player: Position.NORTH, value: SpecialBid.PASS, timestamp: new Date() },
        { player: Position.EAST, value: SpecialBid.PASS, timestamp: new Date() },
        { player: Position.SOUTH, value: SpecialBid.PASS, timestamp: new Date() }
      ];

      let currentState = gameState;
      for (const bid of bids) {
        expect(isValidBid(currentState.auction, bid)).toBe(true);
        currentState = processBid(currentState, bid);
      }

      expect(currentState.phase).toBe(GamePhase.PLAYING);
      expect(currentState.contract!.level).toBe(BidLevel.THREE);
      expect(currentState.contract!.suit).toBe(BidSuit.CLUBS);
      expect(currentState.contract!.doubled).toBe('redoubled');
    });

    it('should reject invalid bid sequences', () => {
      const auction = [
        { player: Position.EAST, value: { level: BidLevel.ONE, suit: BidSuit.SPADES }, timestamp: new Date() }
      ];

      // Lower bid should be rejected
      const lowerBid = {
        player: Position.SOUTH,
        value: { level: BidLevel.ONE, suit: BidSuit.CLUBS },
        timestamp: new Date()
      };
      expect(isValidBid(auction, lowerBid)).toBe(false);

      // Double own bid should be rejected
      const doubleBid = {
        player: Position.WEST, // Partner of East
        value: SpecialBid.DOUBLE,
        timestamp: new Date()
      };
      expect(isValidBid(auction, doubleBid)).toBe(false);
    });

    it('should find correct declarer in complex auctions', () => {
      const auction = [
        { player: Position.NORTH, value: { level: BidLevel.ONE, suit: BidSuit.HEARTS }, timestamp: new Date() },
        { player: Position.EAST, value: SpecialBid.PASS, timestamp: new Date() },
        { player: Position.SOUTH, value: { level: BidLevel.TWO, suit: BidSuit.HEARTS }, timestamp: new Date() },
        { player: Position.WEST, value: SpecialBid.PASS, timestamp: new Date() },
        { player: Position.NORTH, value: { level: BidLevel.FOUR, suit: BidSuit.HEARTS }, timestamp: new Date() },
        { player: Position.EAST, value: SpecialBid.PASS, timestamp: new Date() },
        { player: Position.SOUTH, value: SpecialBid.PASS, timestamp: new Date() },
        { player: Position.WEST, value: SpecialBid.PASS, timestamp: new Date() }
      ];

      const contract = getFinalContract(auction);
      expect(contract).not.toBeNull();
      expect(contract!.declarer).toBe(Position.NORTH); // First to bid hearts in N-S
    });
  });

  describe('Dummy Play Rules - Edge Cases', () => {
    beforeEach(() => {
      // Set up playing phase with East as declarer, West as dummy
      const bids = [
        { player: Position.EAST, value: { level: BidLevel.ONE, suit: BidSuit.SPADES }, timestamp: new Date() },
        { player: Position.SOUTH, value: SpecialBid.PASS, timestamp: new Date() },
        { player: Position.WEST, value: SpecialBid.PASS, timestamp: new Date() },
        { player: Position.NORTH, value: SpecialBid.PASS, timestamp: new Date() }
      ];
      
      for (const bid of bids) {
        gameState = processBid(gameState, bid);
      }
    });

    it('should hide dummy before opening lead', () => {
      expect(isDummyVisible(gameState)).toBe(false);
    });

    it('should expose dummy after opening lead', () => {
      gameState.players[Position.SOUTH].hand = [{ suit: Suit.HEARTS, rank: Rank.ACE }];
      
      const newGameState = playCard(gameState, { suit: Suit.HEARTS, rank: Rank.ACE }, Position.SOUTH);
      expect(isDummyVisible(newGameState)).toBe(true);
    });

    it('should require declarer control when dummy to play', () => {
      // Play to dummy's turn
      gameState.players[Position.SOUTH].hand = [{ suit: Suit.HEARTS, rank: Rank.ACE }];
      let currentState = playCard(gameState, { suit: Suit.HEARTS, rank: Rank.ACE }, Position.SOUTH);
      
      expect(currentState.currentPlayer).toBe(Position.WEST); // Dummy
      expect(shouldDeclarerControlDummy(currentState)).toBe(true);
    });

    it('should handle dummy winning tricks correctly', () => {
      // Set up so dummy wins
      gameState.players[Position.SOUTH].hand = [{ suit: Suit.HEARTS, rank: Rank.TWO }];
      gameState.players[Position.WEST].hand = [{ suit: Suit.HEARTS, rank: Rank.ACE }]; // Dummy wins
      gameState.players[Position.NORTH].hand = [{ suit: Suit.HEARTS, rank: Rank.THREE }];
      gameState.players[Position.EAST].hand = [{ suit: Suit.HEARTS, rank: Rank.FOUR }];

      let currentState = gameState;
      
      // Play complete trick
      const cards = [
        { suit: Suit.HEARTS, rank: Rank.TWO },
        { suit: Suit.HEARTS, rank: Rank.ACE },
        { suit: Suit.HEARTS, rank: Rank.THREE },
        { suit: Suit.HEARTS, rank: Rank.FOUR }
      ];

      for (let i = 0; i < 4; i++) {
        const currentPlayer = currentState.currentPlayer;
        currentState = playCard(currentState, cards[i], currentPlayer);
      }

      expect(currentState.tricks[0].winner).toBe(Position.WEST); // Dummy won
      expect(currentState.currentPlayer).toBe(Position.WEST); // Dummy leads next
      expect(shouldDeclarerControlDummy(currentState)).toBe(true);
    });
  });

  describe('Scoring Rules - Complex Cases', () => {
    it('should score doubled slam contracts correctly', () => {
      const contract: Contract = {
        level: BidLevel.SIX,
        suit: BidSuit.SPADES,
        declarer: Position.SOUTH,
        doubled: 'doubled'
      };

      // Made exactly, vulnerable
      const score = calculateContractScore(contract, 12, true);
      // 360 basic (180*2) + 500 game + 750 slam + 50 double = 1660
      expect(score).toBe(1660);
    });

    it('should score multiple undertricks in doubled contracts', () => {
      const contract: Contract = {
        level: BidLevel.SEVEN,
        suit: BidSuit.NO_TRUMP,
        declarer: Position.SOUTH,
        doubled: 'doubled'
      };

      // Down 3, vulnerable
      const penalty = calculateContractScore(contract, 10, true);
      // 200 + 300 + 300 = 800
      expect(penalty).toBe(-800);
    });

    it('should handle part-game contracts with overtricks', () => {
      const contract: Contract = {
        level: BidLevel.TWO,
        suit: BidSuit.CLUBS,
        declarer: Position.SOUTH,
        doubled: 'none'
      };

      // Made with 3 overtricks
      const score = calculateContractScore(contract, 11, false);
      // 40 basic + 60 overtricks + 50 part game = 150
      expect(score).toBe(150);
    });
  });

  describe('Game State Validation - Edge Cases', () => {
    it('should detect inconsistent game states', () => {
      // Invalid: contract during bidding
      gameState.phase = GamePhase.BIDDING;
      gameState.contract = {
        level: BidLevel.ONE,
        suit: BidSuit.CLUBS,
        declarer: Position.EAST,
        doubled: 'none'
      };

      const validation = validateGameState(gameState);
      expect(validation.isValid).toBe(false);
      expect(validation.errors).toContain('Contract should be null during bidding phase');
    });

    it('should validate card counts correctly', () => {
      // Remove cards from a player
      gameState.players[Position.NORTH].hand = [];

      const validation = validateGameState(gameState);
      expect(validation.isValid).toBe(false);
      expect(validation.errors.some(e => e.includes('cards, expected 13'))).toBe(true);
    });

    it('should validate phase transitions', () => {
      // Invalid: playing phase without contract
      gameState.phase = GamePhase.PLAYING;
      gameState.contract = null;

      const validation = validateGameState(gameState);
      expect(validation.isValid).toBe(false);
      expect(validation.errors).toContain('Contract should not be null during playing phase');
    });
  });

  describe('Rule Violations and Error Handling', () => {
    it('should reject playing cards out of turn', () => {
      // Set up playing phase
      const bids = [
        { player: Position.EAST, value: { level: BidLevel.ONE, suit: BidSuit.CLUBS }, timestamp: new Date() },
        { player: Position.SOUTH, value: SpecialBid.PASS, timestamp: new Date() },
        { player: Position.WEST, value: SpecialBid.PASS, timestamp: new Date() },
        { player: Position.NORTH, value: SpecialBid.PASS, timestamp: new Date() }
      ];
      
      for (const bid of bids) {
        gameState = processBid(gameState, bid);
      }

      // Try to play out of turn
      expect(() => {
        playCard(gameState, { suit: Suit.HEARTS, rank: Rank.ACE }, Position.NORTH);
      }).toThrow("It's not N's turn to play");
    });

    it('should reject bidding out of turn', () => {
      const wrongPlayerBid = {
        player: Position.NORTH, // Wrong player
        value: { level: BidLevel.ONE, suit: BidSuit.CLUBS },
        timestamp: new Date()
      };

      expect(() => {
        processBid(gameState, wrongPlayerBid);
      }).toThrow("It's not N's turn to bid");
    });

    it('should reject playing cards not in hand', () => {
      // Set up playing phase
      const bids = [
        { player: Position.EAST, value: { level: BidLevel.ONE, suit: BidSuit.CLUBS }, timestamp: new Date() },
        { player: Position.SOUTH, value: SpecialBid.PASS, timestamp: new Date() },
        { player: Position.WEST, value: SpecialBid.PASS, timestamp: new Date() },
        { player: Position.NORTH, value: SpecialBid.PASS, timestamp: new Date() }
      ];
      
      for (const bid of bids) {
        gameState = processBid(gameState, bid);
      }

      // Try to play card not in hand
      expect(() => {
        playCard(gameState, { suit: Suit.SPADES, rank: Rank.ACE }, Position.SOUTH);
      }).toThrow('Card not found in player hand');
    });

    it('should reject actions in wrong game phase', () => {
      // Try to play card during bidding
      expect(() => {
        playCard(gameState, { suit: Suit.HEARTS, rank: Rank.ACE }, Position.EAST);
      }).toThrow('Cannot play cards outside of playing phase');

      // Try to bid during playing
      gameState.phase = GamePhase.PLAYING;
      expect(() => {
        processBid(gameState, {
          player: Position.EAST,
          value: { level: BidLevel.ONE, suit: BidSuit.CLUBS },
          timestamp: new Date()
        });
      }).toThrow('Cannot bid outside of bidding phase');
    });
  });

  describe('Integration Tests - Complete Game Flows', () => {
    it('should handle complete rubber bridge game', () => {
      // Game 1: Simple contract
      let currentState = gameState;

      const bids1 = [
        { player: Position.EAST, value: { level: BidLevel.ONE, suit: BidSuit.CLUBS }, timestamp: new Date() },
        { player: Position.SOUTH, value: SpecialBid.PASS, timestamp: new Date() },
        { player: Position.WEST, value: SpecialBid.PASS, timestamp: new Date() },
        { player: Position.NORTH, value: SpecialBid.PASS, timestamp: new Date() }
      ];

      for (const bid of bids1) {
        currentState = processBid(currentState, bid);
      }

      expect(currentState.phase).toBe(GamePhase.PLAYING);
      expect(currentState.contract!.declarer).toBe(Position.EAST);
      expect(currentState.dummy).toBe(Position.WEST);
      expect(currentState.currentPlayer).toBe(Position.SOUTH); // Left of declarer leads
    });

    it('should handle all-pass deals correctly', () => {
      const allPassBids = [
        { player: Position.EAST, value: SpecialBid.PASS, timestamp: new Date() },
        { player: Position.SOUTH, value: SpecialBid.PASS, timestamp: new Date() },
        { player: Position.WEST, value: SpecialBid.PASS, timestamp: new Date() },
        { player: Position.NORTH, value: SpecialBid.PASS, timestamp: new Date() }
      ];

      let currentState = gameState;
      for (const bid of allPassBids) {
        currentState = processBid(currentState, bid);
      }

      expect(currentState.phase).toBe(GamePhase.FINISHED);
      expect(currentState.contract).toBeNull();
    });

    it('should maintain data consistency throughout complete game', () => {
      // Complete a full game and verify data consistency at each step
      let currentState = gameState;

      // Bidding phase
      expect(validateGameState(currentState).isValid).toBe(true);

      const bids = [
        { player: Position.EAST, value: { level: BidLevel.THREE, suit: BidSuit.NO_TRUMP }, timestamp: new Date() },
        { player: Position.SOUTH, value: SpecialBid.PASS, timestamp: new Date() },
        { player: Position.WEST, value: SpecialBid.PASS, timestamp: new Date() },
        { player: Position.NORTH, value: SpecialBid.PASS, timestamp: new Date() }
      ];

      for (const bid of bids) {
        currentState = processBid(currentState, bid);
        expect(validateGameState(currentState).isValid).toBe(true);
      }

      // Playing phase
      expect(currentState.phase).toBe(GamePhase.PLAYING);
      expect(validateGameState(currentState).isValid).toBe(true);

      // Set up hands for controlled play
      const positions = [Position.SOUTH, Position.WEST, Position.NORTH, Position.EAST];
      positions.forEach(pos => {
        const hand = [];
        for (let i = 0; i < 13; i++) {
          hand.push({ suit: Suit.HEARTS, rank: Rank.TWO });
        }
        currentState.players[pos].hand = hand;
      });

      // Play all tricks
      for (let trick = 0; trick < 13; trick++) {
        for (let card = 0; card < 4; card++) {
          const cardToPlay = { suit: Suit.HEARTS, rank: Rank.TWO };
          const currentPlayer = currentState.currentPlayer;
          currentState = playCard(currentState, cardToPlay, currentPlayer);

          // Validate state after each card
          expect(validateGameState(currentState).isValid).toBe(true);
        }
      }

      // Scoring phase
      expect(currentState.phase).toBe(GamePhase.SCORING);
      expect(validateGameState(currentState).isValid).toBe(true);

      // Complete scoring
      currentState = processScoring(currentState);
      expect(currentState.phase).toBe(GamePhase.COMPLETE);
      expect(validateGameState(currentState).isValid).toBe(true);
    });
  });

  describe('Performance and Stress Tests', () => {
    it('should handle rapid card play efficiently', () => {
      // Set up playing phase
      const bids = [
        { player: Position.EAST, value: { level: BidLevel.ONE, suit: BidSuit.CLUBS }, timestamp: new Date() },
        { player: Position.SOUTH, value: SpecialBid.PASS, timestamp: new Date() },
        { player: Position.WEST, value: SpecialBid.PASS, timestamp: new Date() },
        { player: Position.NORTH, value: SpecialBid.PASS, timestamp: new Date() }
      ];

      let currentState = gameState;
      for (const bid of bids) {
        currentState = processBid(currentState, bid);
      }

      // Set up hands
      const positions = [Position.SOUTH, Position.WEST, Position.NORTH, Position.EAST];
      positions.forEach(pos => {
        const hand = [];
        for (let i = 0; i < 13; i++) {
          hand.push({ suit: Suit.HEARTS, rank: Rank.TWO });
        }
        currentState.players[pos].hand = hand;
      });

      const startTime = Date.now();

      // Play all 52 cards
      for (let trick = 0; trick < 13; trick++) {
        for (let card = 0; card < 4; card++) {
          const cardToPlay = { suit: Suit.HEARTS, rank: Rank.TWO };
          const currentPlayer = currentState.currentPlayer;
          currentState = playCard(currentState, cardToPlay, currentPlayer);
        }
      }

      const endTime = Date.now();
      const duration = endTime - startTime;

      expect(duration).toBeLessThan(100); // Should complete in under 100ms
      expect(currentState.phase).toBe(GamePhase.SCORING);
    });

    it('should handle complex bidding sequences efficiently', () => {
      const startTime = Date.now();

      // Create a long competitive auction
      const bids = [
        { player: Position.EAST, value: { level: BidLevel.ONE, suit: BidSuit.CLUBS }, timestamp: new Date() },
        { player: Position.SOUTH, value: { level: BidLevel.ONE, suit: BidSuit.DIAMONDS }, timestamp: new Date() },
        { player: Position.WEST, value: { level: BidLevel.ONE, suit: BidSuit.HEARTS }, timestamp: new Date() },
        { player: Position.NORTH, value: { level: BidLevel.ONE, suit: BidSuit.SPADES }, timestamp: new Date() },
        { player: Position.EAST, value: { level: BidLevel.TWO, suit: BidSuit.CLUBS }, timestamp: new Date() },
        { player: Position.SOUTH, value: { level: BidLevel.TWO, suit: BidSuit.DIAMONDS }, timestamp: new Date() },
        { player: Position.WEST, value: { level: BidLevel.TWO, suit: BidSuit.HEARTS }, timestamp: new Date() },
        { player: Position.NORTH, value: { level: BidLevel.TWO, suit: BidSuit.SPADES }, timestamp: new Date() },
        { player: Position.EAST, value: SpecialBid.DOUBLE, timestamp: new Date() },
        { player: Position.SOUTH, value: SpecialBid.REDOUBLE, timestamp: new Date() },
        { player: Position.WEST, value: SpecialBid.PASS, timestamp: new Date() },
        { player: Position.NORTH, value: SpecialBid.PASS, timestamp: new Date() },
        { player: Position.EAST, value: SpecialBid.PASS, timestamp: new Date() }
      ];

      let currentState = gameState;
      for (const bid of bids) {
        currentState = processBid(currentState, bid);
      }

      const endTime = Date.now();
      const duration = endTime - startTime;

      expect(duration).toBeLessThan(50); // Should complete in under 50ms
      expect(currentState.phase).toBe(GamePhase.PLAYING);
      expect(currentState.contract!.doubled).toBe('redoubled');
    });
  });

  describe('Boundary Conditions', () => {
    it('should handle minimum and maximum bid levels', () => {
      // Minimum bid (1C)
      const minBid = {
        player: Position.EAST,
        value: { level: BidLevel.ONE, suit: BidSuit.CLUBS },
        timestamp: new Date()
      };
      expect(isValidBid([], minBid)).toBe(true);

      // Maximum bid (7NT)
      const maxBid = {
        player: Position.SOUTH,
        value: { level: BidLevel.SEVEN, suit: BidSuit.NO_TRUMP },
        timestamp: new Date()
      };
      expect(isValidBid([minBid], maxBid)).toBe(true);
    });

    it('should handle extreme scoring scenarios', () => {
      // Maximum possible positive score (7NTxx made with overtricks, vulnerable)
      const contract: Contract = {
        level: BidLevel.SEVEN,
        suit: BidSuit.NO_TRUMP,
        declarer: Position.SOUTH,
        doubled: 'redoubled'
      };

      const score = calculateContractScore(contract, 13, true);
      expect(score).toBeGreaterThan(2000); // Should be very high

      // Maximum possible penalty (7NTxx down 13, vulnerable)
      const penalty = calculateContractScore(contract, 0, true);
      expect(penalty).toBeLessThan(-5000); // Should be very negative
    });

    it('should handle edge cases in trick counting', () => {
      // Exactly 6 tricks (book) - should fail any contract
      const contract: Contract = {
        level: BidLevel.ONE,
        suit: BidSuit.CLUBS,
        declarer: Position.SOUTH,
        doubled: 'none'
      };

      const score = calculateContractScore(contract, 6, false);
      expect(score).toBe(-50); // Down 1

      // All 13 tricks in 1-level contract
      const maxScore = calculateContractScore(contract, 13, false);
      expect(maxScore).toBeGreaterThan(100); // Basic + part game + lots of overtricks
    });
  });
});

/**
 * Bidding utility functions for bridge game logic
 */

import { Auction, Bid, BidLevel, BidSuit, SpecialBid, Position } from '../../types/bridge';

/**
 * Gets the last contract bid from the auction (ignoring passes, doubles, redoubles)
 */
export function getLastContractBid(auction: Auction): Bid | null {
  for (let i = auction.length - 1; i >= 0; i--) {
    const bid = auction[i];
    if (typeof bid.value === 'object' && 'level' in bid.value && 'suit' in bid.value) {
      return bid;
    }
  }
  return null;
}

/**
 * Checks if a bid is valid given the current auction state
 */
export function isValidBid(auction: Auction, bid: Bid): boolean {
  const lastContractBid = getLastContractBid(auction);
  
  // Pass is always valid
  if (bid.value === SpecialBid.PASS) {
    return true;
  }
  
  // Double/Redouble logic
  if (bid.value === SpecialBid.DOUBLE) {
    return canDouble(auction, bid.player);
  }
  
  if (bid.value === SpecialBid.REDOUBLE) {
    return canRedouble(auction, bid.player);
  }
  
  // Contract bid validation
  if (typeof bid.value === 'object' && 'level' in bid.value && 'suit' in bid.value) {
    return isValidContractBid(lastContractBid, bid.value);
  }
  
  return false;
}

/**
 * Checks if a contract bid is higher than the last contract bid
 */
function isValidContractBid(
  lastBid: Bid | null, 
  newBid: { level: BidLevel; suit: BidSuit }
): boolean {
  if (!lastBid || typeof lastBid.value === 'string') {
    return true; // No previous contract bid, any bid is valid
  }
  
  const lastContract = lastBid.value;
  
  // Higher level is always valid
  if (newBid.level > lastContract.level) {
    return true;
  }
  
  // Same level, must be higher suit
  if (newBid.level === lastContract.level) {
    return getSuitRank(newBid.suit) > getSuitRank(lastContract.suit);
  }
  
  // Lower level is never valid
  return false;
}

/**
 * Gets the rank of a suit for bidding purposes
 */
function getSuitRank(suit: BidSuit): number {
  switch (suit) {
    case BidSuit.CLUBS: return 1;
    case BidSuit.DIAMONDS: return 2;
    case BidSuit.HEARTS: return 3;
    case BidSuit.SPADES: return 4;
    case BidSuit.NO_TRUMP: return 5;
    default: return 0;
  }
}

/**
 * Checks if a player can double the last bid
 */
function canDouble(auction: Auction, player: Position): boolean {
  const lastContractBid = getLastContractBid(auction);
  if (!lastContractBid) {
    return false; // No bid to double
  }

  // Check if the last bid was made by an opponent
  const isOpponentBid = isOpponent(player, lastContractBid.player);
  if (!isOpponentBid) {
    return false; // Can't double partner's bid
  }

  // Check if the bid hasn't already been doubled
  const lastSpecialBid = getLastSpecialBid(auction);
  if (lastSpecialBid === SpecialBid.DOUBLE || lastSpecialBid === SpecialBid.REDOUBLE) {
    return false; // Already doubled
  }

  return true;
}

/**
 * Checks if a player can redouble the last bid
 */
function canRedouble(auction: Auction, player: Position): boolean {
  const lastSpecialBid = getLastSpecialBid(auction);
  if (lastSpecialBid !== SpecialBid.DOUBLE) {
    return false; // No double to redouble
  }

  const lastContractBid = getLastContractBid(auction);
  if (!lastContractBid) {
    return false; // No contract bid
  }

  // Can only redouble if the original bid was made by player or partner
  const isOwnSideBid = !isOpponent(player, lastContractBid.player);
  return isOwnSideBid;
}

/**
 * Gets the last special bid (pass, double, redouble) from the auction
 */
function getLastSpecialBid(auction: Auction): SpecialBid | null {
  for (let i = auction.length - 1; i >= 0; i--) {
    const bid = auction[i];
    if (typeof bid.value === 'string') {
      return bid.value as SpecialBid;
    }
  }
  return null;
}

/**
 * Checks if two players are opponents (different partnerships)
 */
function isOpponent(player1: Position, player2: Position): boolean {
  // In bridge: North-South vs East-West
  const northSouth = [Position.NORTH, Position.SOUTH];
  const eastWest = [Position.EAST, Position.WEST];

  const player1Side = northSouth.includes(player1) ? 'NS' : 'EW';
  const player2Side = northSouth.includes(player2) ? 'NS' : 'EW';

  return player1Side !== player2Side;
}

/**
 * Checks if the auction is complete (3 consecutive passes after a bid)
 */
export function isAuctionComplete(auction: Auction): boolean {
  if (auction.length < 4) {
    return false; // Need at least 4 bids to end
  }
  
  // Check for 4 passes (all pass)
  if (auction.length === 4 && auction.every(bid => bid.value === SpecialBid.PASS)) {
    return true;
  }
  
  // Check for 3 consecutive passes after a contract bid
  const lastThreeBids = auction.slice(-3);
  const allPasses = lastThreeBids.every(bid => bid.value === SpecialBid.PASS);
  
  if (allPasses) {
    // Make sure there was at least one contract bid before the passes
    const hasContractBid = auction.slice(0, -3).some(bid => 
      typeof bid.value === 'object' && 'level' in bid.value
    );
    return hasContractBid;
  }
  
  return false;
}

/**
 * Gets the final contract from a completed auction
 */
export function getFinalContract(auction: Auction): {
  level: BidLevel;
  suit: BidSuit;
  declarer: Position;
  doubled: 'none' | 'doubled' | 'redoubled';
} | null {
  if (!isAuctionComplete(auction)) {
    return null;
  }

  const lastContractBid = getLastContractBid(auction);
  if (!lastContractBid || typeof lastContractBid.value === 'string') {
    return null; // All passed
  }

  // Find the correct declarer (first player in winning partnership to bid the strain)
  const declarer = findDeclarer(auction, lastContractBid.value);

  // Determine if doubled/redoubled
  let doubled: 'none' | 'doubled' | 'redoubled' = 'none';
  const lastSpecialBid = getLastSpecialBid(auction);
  if (lastSpecialBid === SpecialBid.DOUBLE) {
    doubled = 'doubled';
  } else if (lastSpecialBid === SpecialBid.REDOUBLE) {
    doubled = 'redoubled';
  }

  return {
    level: lastContractBid.value.level,
    suit: lastContractBid.value.suit,
    declarer,
    doubled
  };
}

/**
 * Finds the declarer for a contract (first player in partnership to bid the strain)
 */
function findDeclarer(auction: Auction, finalContract: { level: BidLevel; suit: BidSuit }): Position {
  const lastContractBid = getLastContractBid(auction);
  if (!lastContractBid) {
    throw new Error('No contract bid found');
  }

  // Get the partnership that made the final bid
  const winningPartnership = getPartnership(lastContractBid.player);

  // Find the first player in that partnership to bid the strain
  for (const bid of auction) {
    if (typeof bid.value !== 'string' &&
        bid.value.suit === finalContract.suit &&
        winningPartnership.includes(bid.player)) {
      return bid.player;
    }
  }

  // Fallback to the player who made the final bid
  return lastContractBid.player;
}

/**
 * Gets the partnership for a player
 */
function getPartnership(player: Position): Position[] {
  if (player === Position.NORTH || player === Position.SOUTH) {
    return [Position.NORTH, Position.SOUTH];
  } else {
    return [Position.EAST, Position.WEST];
  }
}

/**
 * Formats a bid for display
 */
export function formatBid(bid: Bid): string {
  if (typeof bid.value === "string") {
    switch (bid.value) {
      case SpecialBid.PASS:
        return "Pass";
      case SpecialBid.DOUBLE:
        return "X";
      case SpecialBid.REDOUBLE:
        return "XX";
      default:
        return bid.value;
    }
  }
  
  if (typeof bid.value === "object" && "level" in bid.value && "suit" in bid.value) {
    const suitSymbol = getSuitSymbol(bid.value.suit);
    return `${bid.value.level}${suitSymbol}`;
  }
  
  return "Unknown";
}

/**
 * Gets the symbol for a bid suit
 */
function getSuitSymbol(suit: BidSuit): string {
  switch (suit) {
    case BidSuit.CLUBS: return "♣";
    case BidSuit.DIAMONDS: return "♦";
    case BidSuit.HEARTS: return "♥";
    case BidSuit.SPADES: return "♠";
    case BidSuit.NO_TRUMP: return "NT";
    default: return "?";
  }
}

// Simple bid validation for UI components
export function isValidSimpleBid(bid: any): boolean {
  return true; // Simplified validation for now
}

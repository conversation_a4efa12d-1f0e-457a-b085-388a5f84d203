/**
 * Unit tests for AI Player implementation
 * Tests SAYC bidding system and card play logic
 */

import {
  AIPlayer,
  calculateHCP,
  calculateDistributionPoints,
  calculateTotalPoints,
  getSuitLengths,
  isSuitBiddable,
  getLongestSuits,
  isBalancedHand
} from './aiPlayer';

import {
  Card,
  Suit,
  Rank,
  Position,
  BidLevel,
  BidSuit,
  SpecialBid,
  GameState,
  GamePhase,
  PlayerType
} from '../../types/bridge';

import { createNewGame, createPlayers } from './gameStateUtils';

describe('aiPlayer', () => {
  // Test hands
  const balancedHand: Card[] = [
    { rank: Rank.ACE, suit: Suit.SPADES },
    { rank: Rank.KING, suit: Suit.SPADES },
    { rank: Rank.QUEEN, suit: Suit.SPADES },
    { rank: Rank.JACK, suit: Suit.HEARTS },
    { rank: Rank.TEN, suit: Suit.HEARTS },
    { rank: Rank.NINE, suit: Suit.HEARTS },
    { rank: Rank.EIGHT, suit: Suit.HEARTS },
    { rank: Rank.ACE, suit: Suit.DIAMONDS },
    { rank: Rank.KING, suit: Suit.DIAMONDS },
    { rank: Rank.QUEEN, suit: Suit.DIAMONDS },
    { rank: Rank.ACE, suit: Suit.CLUBS },
    { rank: Rank.KING, suit: Suit.CLUBS },
    { rank: Rank.QUEEN, suit: Suit.CLUBS }
  ];

  const unbalancedHand: Card[] = [
    { rank: Rank.ACE, suit: Suit.SPADES },
    { rank: Rank.KING, suit: Suit.SPADES },
    { rank: Rank.QUEEN, suit: Suit.SPADES },
    { rank: Rank.JACK, suit: Suit.SPADES },
    { rank: Rank.TEN, suit: Suit.SPADES },
    { rank: Rank.NINE, suit: Suit.SPADES },
    { rank: Rank.ACE, suit: Suit.HEARTS },
    { rank: Rank.KING, suit: Suit.HEARTS },
    { rank: Rank.QUEEN, suit: Suit.HEARTS },
    { rank: Rank.JACK, suit: Suit.HEARTS },
    { rank: Rank.TEN, suit: Suit.HEARTS },
    { rank: Rank.ACE, suit: Suit.DIAMONDS },
    { rank: Rank.TWO, suit: Suit.CLUBS }
  ];

  const weakHand: Card[] = [
    { rank: Rank.TWO, suit: Suit.SPADES },
    { rank: Rank.THREE, suit: Suit.SPADES },
    { rank: Rank.FOUR, suit: Suit.SPADES },
    { rank: Rank.FIVE, suit: Suit.HEARTS },
    { rank: Rank.SIX, suit: Suit.HEARTS },
    { rank: Rank.SEVEN, suit: Suit.HEARTS },
    { rank: Rank.EIGHT, suit: Suit.HEARTS },
    { rank: Rank.NINE, suit: Suit.DIAMONDS },
    { rank: Rank.TEN, suit: Suit.DIAMONDS },
    { rank: Rank.JACK, suit: Suit.DIAMONDS },
    { rank: Rank.TWO, suit: Suit.CLUBS },
    { rank: Rank.THREE, suit: Suit.CLUBS },
    { rank: Rank.FOUR, suit: Suit.CLUBS }
  ];

  describe('hand evaluation', () => {
    describe('calculateHCP', () => {
      it('should calculate high card points correctly', () => {
        const testHand = [
          { rank: Rank.ACE, suit: Suit.SPADES },    // 4 points
          { rank: Rank.KING, suit: Suit.HEARTS },   // 3 points
          { rank: Rank.QUEEN, suit: Suit.DIAMONDS }, // 2 points
          { rank: Rank.JACK, suit: Suit.CLUBS },    // 1 point
          { rank: Rank.TEN, suit: Suit.SPADES }     // 0 points
        ];
        
        expect(calculateHCP(testHand)).toBe(10);
      });

      it('should handle hands with no honors', () => {
        const noHonorsHand = [
          { rank: Rank.TWO, suit: Suit.SPADES },
          { rank: Rank.THREE, suit: Suit.HEARTS },
          { rank: Rank.FOUR, suit: Suit.DIAMONDS }
        ];
        
        expect(calculateHCP(noHonorsHand)).toBe(0);
      });

      it('should handle hands with all honors', () => {
        const allHonorsHand = [
          { rank: Rank.ACE, suit: Suit.SPADES },
          { rank: Rank.ACE, suit: Suit.HEARTS },
          { rank: Rank.ACE, suit: Suit.DIAMONDS },
          { rank: Rank.ACE, suit: Suit.CLUBS },
          { rank: Rank.KING, suit: Suit.SPADES },
          { rank: Rank.KING, suit: Suit.HEARTS },
          { rank: Rank.KING, suit: Suit.DIAMONDS },
          { rank: Rank.KING, suit: Suit.CLUBS },
          { rank: Rank.QUEEN, suit: Suit.SPADES },
          { rank: Rank.QUEEN, suit: Suit.HEARTS },
          { rank: Rank.QUEEN, suit: Suit.DIAMONDS },
          { rank: Rank.QUEEN, suit: Suit.CLUBS },
          { rank: Rank.JACK, suit: Suit.SPADES }
        ];
        
        expect(calculateHCP(allHonorsHand)).toBe(37); // 16+12+8+1
      });
    });

    describe('calculateDistributionPoints', () => {
      it('should calculate distribution points for voids and singletons', () => {
        const distributionalHand = [
          { rank: Rank.ACE, suit: Suit.SPADES },
          { rank: Rank.KING, suit: Suit.SPADES },
          { rank: Rank.QUEEN, suit: Suit.SPADES },
          { rank: Rank.JACK, suit: Suit.SPADES },
          { rank: Rank.TEN, suit: Suit.SPADES },
          { rank: Rank.NINE, suit: Suit.SPADES },
          { rank: Rank.EIGHT, suit: Suit.SPADES },
          { rank: Rank.ACE, suit: Suit.HEARTS },
          { rank: Rank.KING, suit: Suit.HEARTS },
          { rank: Rank.QUEEN, suit: Suit.HEARTS },
          { rank: Rank.JACK, suit: Suit.HEARTS },
          { rank: Rank.TEN, suit: Suit.HEARTS },
          { rank: Rank.ACE, suit: Suit.DIAMONDS } // Singleton
          // Void in clubs
        ];
        
        const points = calculateDistributionPoints(distributionalHand);
        expect(points).toBe(5); // 2 for singleton + 3 for void
      });

      it('should calculate zero points for balanced distribution', () => {
        expect(calculateDistributionPoints(balancedHand)).toBe(0);
      });
    });

    describe('getSuitLengths', () => {
      it('should correctly count suit lengths', () => {
        const lengths = getSuitLengths(unbalancedHand);
        expect(lengths[Suit.SPADES]).toBe(6);
        expect(lengths[Suit.HEARTS]).toBe(5);
        expect(lengths[Suit.DIAMONDS]).toBe(1);
        expect(lengths[Suit.CLUBS]).toBe(1);
      });
    });

    describe('isBalancedHand', () => {
      it('should identify balanced hands', () => {
        expect(isBalancedHand(balancedHand)).toBe(true);
      });

      it('should identify unbalanced hands', () => {
        expect(isBalancedHand(unbalancedHand)).toBe(false);
      });
    });

    describe('isSuitBiddable', () => {
      it('should identify biddable suits', () => {
        expect(isSuitBiddable(unbalancedHand, Suit.SPADES, 5)).toBe(true);
        expect(isSuitBiddable(unbalancedHand, Suit.HEARTS, 4)).toBe(true);
      });

      it('should reject non-biddable suits', () => {
        expect(isSuitBiddable(unbalancedHand, Suit.DIAMONDS, 4)).toBe(false);
        expect(isSuitBiddable(unbalancedHand, Suit.CLUBS, 4)).toBe(false);
      });
    });

    describe('getLongestSuits', () => {
      it('should return longest suits', () => {
        const longest = getLongestSuits(unbalancedHand);
        expect(longest).toContain(Suit.SPADES);
        expect(longest).toHaveLength(1);
      });

      it('should return multiple suits of equal length', () => {
        const equalLengthHand = [
          { rank: Rank.ACE, suit: Suit.SPADES },
          { rank: Rank.KING, suit: Suit.SPADES },
          { rank: Rank.QUEEN, suit: Suit.SPADES },
          { rank: Rank.JACK, suit: Suit.SPADES },
          { rank: Rank.ACE, suit: Suit.HEARTS },
          { rank: Rank.KING, suit: Suit.HEARTS },
          { rank: Rank.QUEEN, suit: Suit.HEARTS },
          { rank: Rank.JACK, suit: Suit.HEARTS },
          { rank: Rank.ACE, suit: Suit.DIAMONDS },
          { rank: Rank.KING, suit: Suit.DIAMONDS },
          { rank: Rank.ACE, suit: Suit.CLUBS },
          { rank: Rank.KING, suit: Suit.CLUBS },
          { rank: Rank.QUEEN, suit: Suit.CLUBS }
        ];
        
        const longest = getLongestSuits(equalLengthHand);
        expect(longest).toContain(Suit.SPADES);
        expect(longest).toContain(Suit.HEARTS);
        expect(longest).toHaveLength(2);
      });
    });
  });

  describe('AI bidding logic', () => {
    let aiPlayer: AIPlayer;

    beforeEach(() => {
      aiPlayer = new AIPlayer(Position.NORTH, 'intermediate');
    });

    describe('opening bids', () => {
      it('should pass with insufficient points', () => {
        const bid = aiPlayer.makeBid(weakHand, []);
        expect(bid).toBe(SpecialBid.PASS);
      });

      it('should open 1NT with balanced 15-17 HCP', () => {
        // Create a 15 HCP balanced hand
        const ntHand = [
          { rank: Rank.ACE, suit: Suit.SPADES },    // 4
          { rank: Rank.KING, suit: Suit.SPADES },   // 3
          { rank: Rank.QUEEN, suit: Suit.SPADES },  // 2
          { rank: Rank.JACK, suit: Suit.HEARTS },   // 1
          { rank: Rank.TEN, suit: Suit.HEARTS },    // 0
          { rank: Rank.NINE, suit: Suit.HEARTS },   // 0
          { rank: Rank.EIGHT, suit: Suit.HEARTS },  // 0
          { rank: Rank.ACE, suit: Suit.DIAMONDS },  // 4
          { rank: Rank.TWO, suit: Suit.DIAMONDS },  // 0
          { rank: Rank.THREE, suit: Suit.DIAMONDS }, // 0
          { rank: Rank.JACK, suit: Suit.CLUBS },    // 1
          { rank: Rank.TEN, suit: Suit.CLUBS },     // 0
          { rank: Rank.NINE, suit: Suit.CLUBS }     // 0
        ]; // Total: 15 HCP
        
        const bid = aiPlayer.makeBid(ntHand, []);
        expect(bid).toEqual({ level: BidLevel.ONE, suit: BidSuit.NO_TRUMP });
      });

      it('should open longest suit with unbalanced hand', () => {
        const bid = aiPlayer.makeBid(unbalancedHand, []);
        expect(typeof bid).toBe('object');
        if (typeof bid === 'object') {
          expect([BidLevel.ONE, BidLevel.TWO]).toContain(bid.level);
          expect([BidSuit.CLUBS, BidSuit.DIAMONDS, BidSuit.HEARTS, BidSuit.SPADES]).toContain(bid.suit);
        }
      });

      it('should open strong 2 clubs with 22+ HCP', () => {
        const strongHand = [
          { rank: Rank.ACE, suit: Suit.SPADES },
          { rank: Rank.KING, suit: Suit.SPADES },
          { rank: Rank.QUEEN, suit: Suit.SPADES },
          { rank: Rank.ACE, suit: Suit.HEARTS },
          { rank: Rank.KING, suit: Suit.HEARTS },
          { rank: Rank.QUEEN, suit: Suit.HEARTS },
          { rank: Rank.ACE, suit: Suit.DIAMONDS },
          { rank: Rank.KING, suit: Suit.DIAMONDS },
          { rank: Rank.QUEEN, suit: Suit.DIAMONDS },
          { rank: Rank.ACE, suit: Suit.CLUBS },
          { rank: Rank.KING, suit: Suit.CLUBS },
          { rank: Rank.QUEEN, suit: Suit.CLUBS },
          { rank: Rank.JACK, suit: Suit.CLUBS }
        ]; // 22 HCP
        
        const bid = aiPlayer.makeBid(strongHand, []);
        expect(bid).toEqual({ level: BidLevel.TWO, suit: BidSuit.CLUBS });
      });
    });

    describe('response bids', () => {
      it('should pass with insufficient points to respond', () => {
        const partnerOpening = [
          { player: Position.EAST, value: { level: BidLevel.ONE, suit: BidSuit.CLUBS }, timestamp: new Date() }
        ];
        
        const bid = aiPlayer.makeBid(weakHand, partnerOpening);
        expect(bid).toBe(SpecialBid.PASS);
      });

      it('should respond to 1NT opening', () => {
        const partnerOpening = [
          { player: Position.SOUTH, value: { level: BidLevel.ONE, suit: BidSuit.NO_TRUMP }, timestamp: new Date() }
        ];
        
        // Hand with 4-card major and 8+ HCP
        const responseHand = [
          { rank: Rank.ACE, suit: Suit.SPADES },
          { rank: Rank.KING, suit: Suit.SPADES },
          { rank: Rank.QUEEN, suit: Suit.SPADES },
          { rank: Rank.JACK, suit: Suit.SPADES },
          { rank: Rank.TWO, suit: Suit.HEARTS },
          { rank: Rank.THREE, suit: Suit.HEARTS },
          { rank: Rank.FOUR, suit: Suit.HEARTS },
          { rank: Rank.FIVE, suit: Suit.DIAMONDS },
          { rank: Rank.SIX, suit: Suit.DIAMONDS },
          { rank: Rank.SEVEN, suit: Suit.DIAMONDS },
          { rank: Rank.EIGHT, suit: Suit.CLUBS },
          { rank: Rank.NINE, suit: Suit.CLUBS },
          { rank: Rank.TEN, suit: Suit.CLUBS }
        ];
        
        const bid = aiPlayer.makeBid(responseHand, partnerOpening);
        expect(typeof bid).toBe('object');
        if (typeof bid === 'object') {
          expect(bid.level).toBe(BidLevel.TWO);
          expect(bid.suit).toBe(BidSuit.DIAMONDS); // Jacoby transfer for spades
        }
      });

      it('should support partner\'s major suit', () => {
        const partnerOpening = [
          { player: Position.SOUTH, value: { level: BidLevel.ONE, suit: BidSuit.HEARTS }, timestamp: new Date() }
        ];
        
        // Hand with 3+ hearts and 6+ HCP
        const supportHand = [
          { rank: Rank.KING, suit: Suit.SPADES },
          { rank: Rank.QUEEN, suit: Suit.SPADES },
          { rank: Rank.JACK, suit: Suit.HEARTS },
          { rank: Rank.TEN, suit: Suit.HEARTS },
          { rank: Rank.NINE, suit: Suit.HEARTS },
          { rank: Rank.TWO, suit: Suit.DIAMONDS },
          { rank: Rank.THREE, suit: Suit.DIAMONDS },
          { rank: Rank.FOUR, suit: Suit.DIAMONDS },
          { rank: Rank.FIVE, suit: Suit.DIAMONDS },
          { rank: Rank.SIX, suit: Suit.CLUBS },
          { rank: Rank.SEVEN, suit: Suit.CLUBS },
          { rank: Rank.EIGHT, suit: Suit.CLUBS },
          { rank: Rank.NINE, suit: Suit.CLUBS }
        ];
        
        const bid = aiPlayer.makeBid(supportHand, partnerOpening);
        expect(bid).toEqual({ level: BidLevel.TWO, suit: BidSuit.HEARTS });
      });
    });

    describe('competitive bidding', () => {
      it('should overcall with good suit and 8+ HCP', () => {
        const opponentOpening = [
          { player: Position.EAST, value: { level: BidLevel.ONE, suit: BidSuit.CLUBS }, timestamp: new Date() }
        ];
        
        // Hand with good spade suit and 10 HCP
        const overcallHand = [
          { rank: Rank.ACE, suit: Suit.SPADES },
          { rank: Rank.KING, suit: Suit.SPADES },
          { rank: Rank.QUEEN, suit: Suit.SPADES },
          { rank: Rank.JACK, suit: Suit.SPADES },
          { rank: Rank.TEN, suit: Suit.SPADES },
          { rank: Rank.TWO, suit: Suit.HEARTS },
          { rank: Rank.THREE, suit: Suit.HEARTS },
          { rank: Rank.FOUR, suit: Suit.HEARTS },
          { rank: Rank.FIVE, suit: Suit.DIAMONDS },
          { rank: Rank.SIX, suit: Suit.DIAMONDS },
          { rank: Rank.SEVEN, suit: Suit.CLUBS },
          { rank: Rank.EIGHT, suit: Suit.CLUBS },
          { rank: Rank.NINE, suit: Suit.CLUBS }
        ];
        
        const bid = aiPlayer.makeBid(overcallHand, opponentOpening);
        expect(bid).toEqual({ level: BidLevel.ONE, suit: BidSuit.SPADES });
      });

      it('should pass without good overcall', () => {
        const opponentOpening = [
          { player: Position.EAST, value: { level: BidLevel.ONE, suit: BidSuit.SPADES }, timestamp: new Date() }
        ];
        
        const bid = aiPlayer.makeBid(weakHand, opponentOpening);
        expect(bid).toBe(SpecialBid.PASS);
      });
    });
  });

  describe('AI card play logic', () => {
    let gameState: GameState;
    let aiPlayer: AIPlayer;

    beforeEach(() => {
      const players = createPlayers(Position.SOUTH);
      gameState = createNewGame('test-game', players, Position.NORTH);
      
      // Move to playing phase
      const bids = [
        { player: Position.EAST, value: { level: BidLevel.ONE, suit: BidSuit.CLUBS }, timestamp: new Date() },
        { player: Position.SOUTH, value: SpecialBid.PASS, timestamp: new Date() },
        { player: Position.WEST, value: SpecialBid.PASS, timestamp: new Date() },
        { player: Position.NORTH, value: SpecialBid.PASS, timestamp: new Date() }
      ];
      
      for (const bid of bids) {
        gameState = require('./gameStateUtils').processBid(gameState, bid);
      }
      
      aiPlayer = new AIPlayer(gameState.currentPlayer, 'intermediate');
    });

    describe('card play decisions', () => {
      it('should make a valid card play', () => {
        const card = aiPlayer.playCard(gameState);
        
        expect(card).toBeDefined();
        expect(card.rank).toBeDefined();
        expect(card.suit).toBeDefined();
        
        // Verify the card is in the player's hand
        const playerHand = gameState.players[gameState.currentPlayer].hand;
        const cardInHand = playerHand.some(handCard => 
          handCard.rank === card.rank && handCard.suit === card.suit
        );
        expect(cardInHand).toBe(true);
      });

      it('should not throw error when making card play', () => {
        expect(() => aiPlayer.playCard(gameState)).not.toThrow();
      });
    });

    describe('AI timing simulation', () => {
      it('should simulate thinking time', async () => {
        const startTime = Date.now();
        
        const result = await aiPlayer.makeDecisionWithTiming(() => {
          return aiPlayer.makeBid(balancedHand, []);
        });
        
        const endTime = Date.now();
        const elapsed = endTime - startTime;
        
        expect(elapsed).toBeGreaterThan(400); // Should take some time
        expect(elapsed).toBeLessThan(3000); // But not too long
        expect(result).toBeDefined();
      });
    });
  });

  describe('AI difficulty levels', () => {
    it('should create AI players with different difficulty levels', () => {
      const beginner = new AIPlayer(Position.NORTH, 'beginner');
      const intermediate = new AIPlayer(Position.NORTH, 'intermediate');
      const advanced = new AIPlayer(Position.NORTH, 'advanced');
      
      expect(beginner).toBeInstanceOf(AIPlayer);
      expect(intermediate).toBeInstanceOf(AIPlayer);
      expect(advanced).toBeInstanceOf(AIPlayer);
    });

    it('should have different thinking times for different difficulties', async () => {
      const advanced = new AIPlayer(Position.NORTH, 'advanced');
      const beginner = new AIPlayer(Position.NORTH, 'beginner');
      
      const advancedStart = Date.now();
      await advanced.makeDecisionWithTiming(() => 'test');
      const advancedTime = Date.now() - advancedStart;
      
      const beginnerStart = Date.now();
      await beginner.makeDecisionWithTiming(() => 'test');
      const beginnerTime = Date.now() - beginnerStart;
      
      // Beginner should generally take longer than advanced
      // (though there's randomness, so this might occasionally fail)
      expect(beginnerTime).toBeGreaterThan(advancedTime * 0.8);
    });
  });
});

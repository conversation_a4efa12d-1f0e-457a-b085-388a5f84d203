/**
 * Tests for dummy play rules implementation
 * Verifies Bridge rules for declarer controlling dummy, dummy visibility, and dummy restrictions
 */

import {
  createNewGame,
  createPlayers,
  processBid,
  playCard,
  isDummyVisible,
  shouldDeclarerControlDummy,
  getEffectivePlayer,
  getValidCardsForPlayer
} from './gameStateUtils';

import {
  Position,
  GamePhase,
  BidLevel,
  BidSuit,
  SpecialBid,
  Suit,
  Rank
} from '../../types/bridge';

describe('Dummy Play Rules', () => {
  let gameState: any;

  beforeEach(() => {
    const players = createPlayers(Position.SOUTH);
    gameState = createNewGame('test-game', players, Position.NORTH);
    
    // Complete bidding to get to playing phase with East as declarer
    const bids = [
      { player: Position.EAST, value: { level: BidLevel.ONE, suit: BidSuit.SPADES }, timestamp: new Date() },
      { player: Position.SOUTH, value: SpecialBid.PASS, timestamp: new Date() },
      { player: Position.WEST, value: SpecialBid.PASS, timestamp: new Date() },
      { player: Position.NORTH, value: SpecialBid.PASS, timestamp: new Date() }
    ];
    
    for (const bid of bids) {
      gameState = processBid(gameState, bid);
    }
    
    // Verify setup: East is declarer, West is dummy, South leads
    expect(gameState.contract.declarer).toBe(Position.EAST);
    expect(gameState.dummy).toBe(Position.WEST);
    expect(gameState.currentPlayer).toBe(Position.SOUTH); // Left of declarer leads
  });

  describe('Dummy Visibility Rules', () => {
    it('should not show dummy before opening lead', () => {
      // Before any cards are played
      expect(isDummyVisible(gameState)).toBe(false);
    });

    it('should show dummy after opening lead', () => {
      // Set up hands for testing
      gameState.players[Position.SOUTH].hand = [{ suit: Suit.HEARTS, rank: Rank.ACE }];
      
      // Play opening lead
      const openingLead = { suit: Suit.HEARTS, rank: Rank.ACE };
      const newGameState = playCard(gameState, openingLead, Position.SOUTH);
      
      // Dummy should now be visible
      expect(isDummyVisible(newGameState)).toBe(true);
    });

    it('should keep dummy visible throughout the hand', () => {
      // Set up hands for a complete trick
      const positions = [Position.SOUTH, Position.WEST, Position.NORTH, Position.EAST];
      positions.forEach(pos => {
        gameState.players[pos].hand = [{ suit: Suit.HEARTS, rank: Rank.ACE }];
      });
      
      let currentState = gameState;
      
      // Play a complete trick
      for (let i = 0; i < 4; i++) {
        const card = { suit: Suit.HEARTS, rank: Rank.ACE };
        const currentPlayer = currentState.currentPlayer;
        currentState = playCard(currentState, card, currentPlayer);
      }
      
      // Dummy should still be visible
      expect(isDummyVisible(currentState)).toBe(true);
    });
  });

  describe('Declarer Control of Dummy', () => {
    it('should identify when declarer should control dummy', () => {
      // Set up hands and play to dummy's turn
      const positions = [Position.SOUTH, Position.WEST, Position.NORTH, Position.EAST];
      positions.forEach(pos => {
        gameState.players[pos].hand = [{ suit: Suit.HEARTS, rank: Rank.ACE }];
      });
      
      let currentState = gameState;
      
      // Play opening lead (South)
      currentState = playCard(currentState, { suit: Suit.HEARTS, rank: Rank.ACE }, Position.SOUTH);
      
      // Now it's dummy's turn (West)
      expect(currentState.currentPlayer).toBe(Position.WEST);
      expect(shouldDeclarerControlDummy(currentState)).toBe(true);
    });

    it('should not require declarer control when not dummy turn', () => {
      // It's South's turn (not dummy)
      expect(shouldDeclarerControlDummy(gameState)).toBe(false);
      
      // Set up and play to North's turn
      gameState.players[Position.SOUTH].hand = [{ suit: Suit.HEARTS, rank: Rank.ACE }];
      gameState.players[Position.WEST].hand = [{ suit: Suit.HEARTS, rank: Rank.KING }];
      
      let currentState = gameState;
      currentState = playCard(currentState, { suit: Suit.HEARTS, rank: Rank.ACE }, Position.SOUTH);
      currentState = playCard(currentState, { suit: Suit.HEARTS, rank: Rank.KING }, Position.WEST);
      
      // Now it's North's turn (not dummy)
      expect(currentState.currentPlayer).toBe(Position.NORTH);
      expect(shouldDeclarerControlDummy(currentState)).toBe(false);
    });

    it('should return correct effective player', () => {
      // When it's not dummy's turn, effective player is current player
      expect(getEffectivePlayer(gameState)).toBe(Position.SOUTH);
      
      // Set up to dummy's turn
      gameState.players[Position.SOUTH].hand = [{ suit: Suit.HEARTS, rank: Rank.ACE }];
      let currentState = playCard(gameState, { suit: Suit.HEARTS, rank: Rank.ACE }, Position.SOUTH);
      
      // When it's dummy's turn, effective player should be declarer
      expect(currentState.currentPlayer).toBe(Position.WEST); // Dummy
      expect(getEffectivePlayer(currentState)).toBe(Position.EAST); // Declarer
    });
  });

  describe('Dummy Play Validation', () => {
    it('should allow declarer to play from dummy hand', () => {
      // Set up to dummy's turn
      gameState.players[Position.SOUTH].hand = [{ suit: Suit.HEARTS, rank: Rank.ACE }];
      gameState.players[Position.WEST].hand = [{ suit: Suit.HEARTS, rank: Rank.KING }];
      
      let currentState = gameState;
      currentState = playCard(currentState, { suit: Suit.HEARTS, rank: Rank.ACE }, Position.SOUTH);
      
      // Now it's dummy's turn - declarer should be able to play from dummy
      expect(() => {
        playCard(currentState, { suit: Suit.HEARTS, rank: Rank.KING }, Position.WEST);
      }).not.toThrow();
    });

    it('should get valid cards for dummy correctly', () => {
      // Set up dummy hand with specific cards
      gameState.players[Position.WEST].hand = [
        { suit: Suit.HEARTS, rank: Rank.ACE },
        { suit: Suit.HEARTS, rank: Rank.KING },
        { suit: Suit.CLUBS, rank: Rank.QUEEN }
      ];
      
      // Set up a trick where hearts are led
      gameState.currentTrick = [
        { card: { suit: Suit.HEARTS, rank: Rank.TEN }, player: Position.SOUTH }
      ];
      
      // Get valid cards for dummy
      const validCards = getValidCardsForPlayer(gameState, Position.WEST);
      
      // Should only be able to play hearts (must follow suit)
      expect(validCards).toHaveLength(2);
      expect(validCards.every(card => card.suit === Suit.HEARTS)).toBe(true);
    });

    it('should handle dummy void in led suit', () => {
      // Set up dummy hand with no hearts
      gameState.players[Position.WEST].hand = [
        { suit: Suit.CLUBS, rank: Rank.ACE },
        { suit: Suit.DIAMONDS, rank: Rank.KING },
        { suit: Suit.SPADES, rank: Rank.QUEEN }
      ];
      
      // Set up a trick where hearts are led (dummy is void)
      gameState.currentTrick = [
        { card: { suit: Suit.HEARTS, rank: Rank.TEN }, player: Position.SOUTH }
      ];
      
      // Get valid cards for dummy
      const validCards = getValidCardsForPlayer(gameState, Position.WEST);
      
      // Should be able to play any card (void in led suit)
      expect(validCards).toHaveLength(3);
    });
  });

  describe('Game Flow with Dummy', () => {
    it('should maintain correct turn order with dummy', () => {
      // Set up hands for all players
      const positions = [Position.SOUTH, Position.WEST, Position.NORTH, Position.EAST];
      positions.forEach(pos => {
        gameState.players[pos].hand = [
          { suit: Suit.HEARTS, rank: Rank.ACE },
          { suit: Suit.CLUBS, rank: Rank.KING }
        ];
      });
      
      let currentState = gameState;
      const playOrder: Position[] = [];
      
      // Play one complete trick and track the order
      for (let i = 0; i < 4; i++) {
        const currentPlayer = currentState.currentPlayer;
        playOrder.push(currentPlayer);
        
        const card = { suit: Suit.HEARTS, rank: Rank.ACE };
        currentState = playCard(currentState, card, currentPlayer);
      }
      
      // Should follow clockwise order: South -> West -> North -> East
      expect(playOrder).toEqual([Position.SOUTH, Position.WEST, Position.NORTH, Position.EAST]);
    });

    it('should handle multiple tricks with dummy correctly', () => {
      // Set up hands for multiple tricks
      const positions = [Position.SOUTH, Position.WEST, Position.NORTH, Position.EAST];
      positions.forEach(pos => {
        gameState.players[pos].hand = [
          { suit: Suit.HEARTS, rank: Rank.ACE },
          { suit: Suit.CLUBS, rank: Rank.KING }
        ];
      });
      
      let currentState = gameState;
      
      // Play first trick
      for (let i = 0; i < 4; i++) {
        const card = { suit: Suit.HEARTS, rank: Rank.ACE };
        const currentPlayer = currentState.currentPlayer;
        currentState = playCard(currentState, card, currentPlayer);
      }
      
      // First trick should be complete
      expect(currentState.tricks).toHaveLength(1);
      expect(currentState.currentTrick).toHaveLength(0);
      
      // Winner should lead next trick
      const trickWinner = currentState.tricks[0].winner;
      expect(currentState.currentPlayer).toBe(trickWinner);
      
      // Dummy should still be visible
      expect(isDummyVisible(currentState)).toBe(true);
    });
  });

  describe('Edge Cases', () => {
    it('should handle dummy play when dummy wins a trick', () => {
      // Set up so dummy wins the trick
      gameState.players[Position.SOUTH].hand = [{ suit: Suit.HEARTS, rank: Rank.TWO }];
      gameState.players[Position.WEST].hand = [{ suit: Suit.HEARTS, rank: Rank.ACE }]; // Dummy wins
      gameState.players[Position.NORTH].hand = [{ suit: Suit.HEARTS, rank: Rank.THREE }];
      gameState.players[Position.EAST].hand = [{ suit: Suit.HEARTS, rank: Rank.FOUR }];
      
      let currentState = gameState;
      
      // Play complete trick
      const cards = [
        { suit: Suit.HEARTS, rank: Rank.TWO },
        { suit: Suit.HEARTS, rank: Rank.ACE },
        { suit: Suit.HEARTS, rank: Rank.THREE },
        { suit: Suit.HEARTS, rank: Rank.FOUR }
      ];
      
      for (let i = 0; i < 4; i++) {
        const currentPlayer = currentState.currentPlayer;
        currentState = playCard(currentState, cards[i], currentPlayer);
      }
      
      // Dummy should have won and should lead next trick
      expect(currentState.tricks[0].winner).toBe(Position.WEST);
      expect(currentState.currentPlayer).toBe(Position.WEST);
      
      // Declarer should control dummy for next trick
      expect(shouldDeclarerControlDummy(currentState)).toBe(true);
    });
  });
});

/**
 * Unit tests for scoringUtils module
 * Tests Bridge scoring calculations including contracts, bonuses, and penalties
 */

import {
  calculateContractPoints,
  isGameContract,
  isSlamContract,
  isGrandSlamContract,
  isVulnerable,
  calculateScore,
  calculateRubberBonus,
  isRubberComplete
} from './scoringUtils';

import {
  Contract,
  BidLevel,
  BidSuit,
  Position,
  Trick
} from '../../types/bridge';

describe('scoringUtils', () => {
  // Test contracts
  const oneClubContract: Contract = {
    level: BidLevel.ONE,
    suit: BidSuit.CLUBS,
    declarer: Position.NORTH,
    doubled: 'none'
  };

  const threeNoTrumpContract: Contract = {
    level: BidLevel.THREE,
    suit: BidSuit.NO_TRUMP,
    declarer: Position.NORTH,
    doubled: 'none'
  };

  const fourSpadesContract: Contract = {
    level: BidLevel.FOUR,
    suit: BidSuit.SPADES,
    declarer: Position.NORTH,
    doubled: 'none'
  };

  const sixNoTrumpContract: Contract = {
    level: BidLevel.SIX,
    suit: BidSuit.NO_TRUMP,
    declarer: Position.NORTH,
    doubled: 'none'
  };

  const sevenNoTrumpContract: Contract = {
    level: BidLevel.SEVEN,
    suit: BidSuit.NO_TRUMP,
    declarer: Position.NORTH,
    doubled: 'none'
  };

  const doubledContract: Contract = {
    level: BidLevel.ONE,
    suit: BidSuit.CLUBS,
    declarer: Position.NORTH,
    doubled: 'doubled'
  };

  const redoubledContract: Contract = {
    level: BidLevel.ONE,
    suit: BidSuit.CLUBS,
    declarer: Position.NORTH,
    doubled: 'redoubled'
  };

  // Test vulnerabilities
  const notVulnerable = { northSouth: false, eastWest: false };
  const nsVulnerable = { northSouth: true, eastWest: false };
  const bothVulnerable = { northSouth: true, eastWest: true };

  describe('basic contract calculations', () => {
    describe('calculateContractPoints', () => {
      it('should calculate minor suit points correctly', () => {
        expect(calculateContractPoints(oneClubContract)).toBe(20);
        expect(calculateContractPoints({
          ...oneClubContract,
          level: BidLevel.FIVE,
          suit: BidSuit.DIAMONDS
        })).toBe(100);
      });

      it('should calculate major suit points correctly', () => {
        expect(calculateContractPoints({
          ...oneClubContract,
          suit: BidSuit.HEARTS
        })).toBe(30);
        expect(calculateContractPoints(fourSpadesContract)).toBe(120);
      });

      it('should calculate no trump points correctly', () => {
        expect(calculateContractPoints({
          ...oneClubContract,
          suit: BidSuit.NO_TRUMP
        })).toBe(40);
        expect(calculateContractPoints(threeNoTrumpContract)).toBe(100);
        expect(calculateContractPoints(sevenNoTrumpContract)).toBe(220);
      });
    });

    describe('contract classification', () => {
      it('should identify game contracts', () => {
        expect(isGameContract(oneClubContract)).toBe(false);
        expect(isGameContract(threeNoTrumpContract)).toBe(true);
        expect(isGameContract(fourSpadesContract)).toBe(true);
        expect(isGameContract({
          ...oneClubContract,
          level: BidLevel.FIVE,
          suit: BidSuit.CLUBS
        })).toBe(true);
      });

      it('should identify slam contracts', () => {
        expect(isSlamContract(oneClubContract)).toBe(false);
        expect(isSlamContract(fourSpadesContract)).toBe(false);
        expect(isSlamContract(sixNoTrumpContract)).toBe(true);
        expect(isSlamContract(sevenNoTrumpContract)).toBe(true);
      });

      it('should identify grand slam contracts', () => {
        expect(isGrandSlamContract(oneClubContract)).toBe(false);
        expect(isGrandSlamContract(sixNoTrumpContract)).toBe(false);
        expect(isGrandSlamContract(sevenNoTrumpContract)).toBe(true);
      });
    });

    describe('vulnerability', () => {
      it('should correctly identify vulnerable partnerships', () => {
        expect(isVulnerable('northSouth', notVulnerable)).toBe(false);
        expect(isVulnerable('eastWest', notVulnerable)).toBe(false);
        expect(isVulnerable('northSouth', nsVulnerable)).toBe(true);
        expect(isVulnerable('eastWest', nsVulnerable)).toBe(false);
        expect(isVulnerable('northSouth', bothVulnerable)).toBe(true);
        expect(isVulnerable('eastWest', bothVulnerable)).toBe(true);
      });
    });
  });

  describe('complete scoring', () => {
    // Helper function to create tricks with specific winners
    const createTricks = (northSouthTricks: number): Trick[] => {
      const tricks: Trick[] = [];
      for (let i = 0; i < 13; i++) {
        tricks.push({
          id: i + 1,
          leadPlayer: Position.NORTH,
          winner: i < northSouthTricks ? Position.NORTH : Position.EAST,
          cards: [
            { card: { rank: 'A' as any, suit: 'S' as any }, player: Position.NORTH },
            { card: { rank: 'K' as any, suit: 'S' as any }, player: Position.EAST },
            { card: { rank: 'Q' as any, suit: 'H' as any }, player: Position.SOUTH },
            { card: { rank: 'J' as any, suit: 'C' as any }, player: Position.WEST }
          ]
        });
      }
      return tricks;
    };

    describe('made contracts', () => {
      it('should score part game correctly', () => {
        const tricks = createTricks(7); // Made exactly
        const score = calculateScore(oneClubContract, tricks, notVulnerable);
        
        expect(score.northSouth.contractPoints).toBe(20);
        expect(score.northSouth.bonuses.game).toBe(50); // Part game bonus
        expect(score.northSouth.bonuses.slam).toBe(0);
        expect(score.northSouth.totalScore).toBe(70);
        expect(score.eastWest.totalScore).toBe(0);
      });

      it('should score game contract correctly', () => {
        const tricks = createTricks(9); // Made exactly
        const score = calculateScore(threeNoTrumpContract, tricks, notVulnerable);
        
        expect(score.northSouth.contractPoints).toBe(100);
        expect(score.northSouth.bonuses.game).toBe(300); // Game bonus
        expect(score.northSouth.bonuses.slam).toBe(0);
        expect(score.northSouth.totalScore).toBe(400);
        expect(score.eastWest.totalScore).toBe(0);
      });

      it('should score small slam correctly', () => {
        const tricks = createTricks(12); // Made exactly
        const score = calculateScore(sixNoTrumpContract, tricks, notVulnerable);
        
        expect(score.northSouth.contractPoints).toBe(190);
        expect(score.northSouth.bonuses.game).toBe(300); // Game bonus
        expect(score.northSouth.bonuses.slam).toBe(500); // Small slam bonus
        expect(score.northSouth.totalScore).toBe(990);
        expect(score.eastWest.totalScore).toBe(0);
      });

      it('should score grand slam correctly', () => {
        const tricks = createTricks(13); // Made exactly
        const score = calculateScore(sevenNoTrumpContract, tricks, notVulnerable);
        
        expect(score.northSouth.contractPoints).toBe(220);
        expect(score.northSouth.bonuses.game).toBe(300); // Game bonus
        expect(score.northSouth.bonuses.slam).toBe(1000); // Grand slam bonus
        expect(score.northSouth.totalScore).toBe(1520);
        expect(score.eastWest.totalScore).toBe(0);
      });

      it('should score overtricks correctly', () => {
        const tricks = createTricks(8); // Made 1 overtrick
        const score = calculateScore(oneClubContract, tricks, notVulnerable);
        
        expect(score.northSouth.contractPoints).toBe(20);
        expect(score.northSouth.overtricks).toBe(20); // 1 overtrick in minor suit
        expect(score.northSouth.bonuses.game).toBe(50);
        expect(score.northSouth.totalScore).toBe(90);
      });

      it('should score doubled contracts correctly', () => {
        const tricks = createTricks(7); // Made exactly
        const score = calculateScore(doubledContract, tricks, notVulnerable);
        
        expect(score.northSouth.contractPoints).toBe(40); // Doubled
        expect(score.northSouth.bonuses.insult).toBe(50); // Double bonus
        expect(score.northSouth.bonuses.game).toBe(50); // Part game bonus
        expect(score.northSouth.totalScore).toBe(140);
      });

      it('should score redoubled contracts correctly', () => {
        const tricks = createTricks(7); // Made exactly
        const score = calculateScore(redoubledContract, tricks, notVulnerable);
        
        expect(score.northSouth.contractPoints).toBe(80); // Redoubled (4x)
        expect(score.northSouth.bonuses.insult).toBe(100); // Redouble bonus
        expect(score.northSouth.bonuses.game).toBe(50); // Part game bonus
        expect(score.northSouth.totalScore).toBe(230);
      });
    });

    describe('failed contracts', () => {
      it('should score undertricks correctly (not vulnerable)', () => {
        const tricks = createTricks(6); // Down 1
        const score = calculateScore(oneClubContract, tricks, notVulnerable);
        
        expect(score.northSouth.totalScore).toBe(0);
        expect(score.eastWest.penalties).toBe(50); // 1 undertrick not vulnerable
        expect(score.eastWest.totalScore).toBe(50);
      });

      it('should score undertricks correctly (vulnerable)', () => {
        const tricks = createTricks(6); // Down 1
        const score = calculateScore(oneClubContract, tricks, nsVulnerable);
        
        expect(score.northSouth.totalScore).toBe(0);
        expect(score.eastWest.penalties).toBe(100); // 1 undertrick vulnerable
        expect(score.eastWest.totalScore).toBe(100);
      });

      it('should score doubled undertricks correctly', () => {
        const tricks = createTricks(6); // Down 1
        const score = calculateScore(doubledContract, tricks, notVulnerable);
        
        expect(score.northSouth.totalScore).toBe(0);
        expect(score.eastWest.penalties).toBe(100); // 1 doubled undertrick not vulnerable
        expect(score.eastWest.totalScore).toBe(100);
      });

      it('should score multiple undertricks correctly', () => {
        const tricks = createTricks(4); // Down 3
        const score = calculateScore(oneClubContract, tricks, notVulnerable);
        
        expect(score.northSouth.totalScore).toBe(0);
        expect(score.eastWest.penalties).toBe(150); // 3 undertricks not vulnerable (50+50+50)
        expect(score.eastWest.totalScore).toBe(150);
      });
    });
  });

  describe('rubber scoring', () => {
    describe('calculateRubberBonus', () => {
      it('should calculate 2-0 rubber bonus correctly', () => {
        const bonus = calculateRubberBonus({ northSouth: 2, eastWest: 0 });
        expect(bonus.northSouth).toBe(700);
        expect(bonus.eastWest).toBe(0);
      });

      it('should calculate 2-1 rubber bonus correctly', () => {
        const bonus = calculateRubberBonus({ northSouth: 2, eastWest: 1 });
        expect(bonus.northSouth).toBe(500);
        expect(bonus.eastWest).toBe(0);
      });

      it('should calculate rubber bonus for east-west', () => {
        const bonus = calculateRubberBonus({ northSouth: 0, eastWest: 2 });
        expect(bonus.northSouth).toBe(0);
        expect(bonus.eastWest).toBe(700);
      });

      it('should return no bonus for incomplete rubber', () => {
        const bonus = calculateRubberBonus({ northSouth: 1, eastWest: 1 });
        expect(bonus.northSouth).toBe(0);
        expect(bonus.eastWest).toBe(0);
      });
    });

    describe('isRubberComplete', () => {
      it('should identify complete rubbers', () => {
        expect(isRubberComplete({ northSouth: 2, eastWest: 0 })).toBe(true);
        expect(isRubberComplete({ northSouth: 2, eastWest: 1 })).toBe(true);
        expect(isRubberComplete({ northSouth: 0, eastWest: 2 })).toBe(true);
        expect(isRubberComplete({ northSouth: 1, eastWest: 2 })).toBe(true);
      });

      it('should identify incomplete rubbers', () => {
        expect(isRubberComplete({ northSouth: 0, eastWest: 0 })).toBe(false);
        expect(isRubberComplete({ northSouth: 1, eastWest: 0 })).toBe(false);
        expect(isRubberComplete({ northSouth: 1, eastWest: 1 })).toBe(false);
        expect(isRubberComplete({ northSouth: 0, eastWest: 1 })).toBe(false);
      });
    });
  });

  // Performance tests
  describe('performance tests', () => {
    it('should calculate scores quickly', () => {
      const tricks = createTricks(9);
      const start = Date.now();
      
      for (let i = 0; i < 1000; i++) {
        calculateScore(threeNoTrumpContract, tricks, notVulnerable);
      }
      
      const elapsed = Date.now() - start;
      expect(elapsed).toBeLessThan(100); // Should complete in under 100ms
    });
  });

  // Helper function for creating tricks
  function createTricks(northSouthTricks: number): Trick[] {
    const tricks: Trick[] = [];
    for (let i = 0; i < 13; i++) {
      tricks.push({
        id: i + 1,
        leadPlayer: Position.NORTH,
        winner: i < northSouthTricks ? Position.NORTH : Position.EAST,
        cards: [
          { card: { rank: 'A' as any, suit: 'S' as any }, player: Position.NORTH },
          { card: { rank: 'K' as any, suit: 'S' as any }, player: Position.EAST },
          { card: { rank: 'Q' as any, suit: 'H' as any }, player: Position.SOUTH },
          { card: { rank: 'J' as any, suit: 'C' as any }, player: Position.WEST }
        ]
      });
    }
    return tricks;
  }
});

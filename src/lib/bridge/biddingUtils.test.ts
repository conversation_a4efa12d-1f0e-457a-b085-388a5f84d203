/**
 * Unit tests for biddingUtils module
 * Tests bid validation, auction logic, and contract determination
 */

import {
  getBidSuitValue,
  getBidValue,
  compareBids,
  isBidHigher,
  validateBid,
  getLastContractBid,
  isAuctionComplete,
  isAuctionPassedOut,
  getContract,
  formatBid,
  getNextBidder
} from './biddingUtils';

import {
  Bid,
  BidValue,
  BidLevel,
  BidSuit,
  SpecialBid,
  Position,
  Auction
} from '../../types/bridge';

describe('biddingUtils', () => {
  describe('bid comparison', () => {
    describe('getBidSuitValue', () => {
      it('should return correct suit values', () => {
        expect(getBidSuitValue(BidSuit.CLUBS)).toBe(1);
        expect(getBidSuitValue(BidSuit.DIAMONDS)).toBe(2);
        expect(getBidSuitValue(BidSuit.HEARTS)).toBe(3);
        expect(getBidSuitValue(BidSuit.SPADES)).toBe(4);
        expect(getBidSuitValue(BidSuit.NO_TRUMP)).toBe(5);
      });
    });

    describe('getBidValue', () => {
      it('should return correct bid values', () => {
        expect(getBidValue({ level: BidLevel.ONE, suit: BidSuit.CLUBS })).toBe(1);
        expect(getBidValue({ level: BidLevel.ONE, suit: BidSuit.NO_TRUMP })).toBe(5);
        expect(getBidValue({ level: BidLevel.SEVEN, suit: BidSuit.NO_TRUMP })).toBe(35);
      });

      it('should return 0 for special bids', () => {
        expect(getBidValue(SpecialBid.PASS)).toBe(0);
        expect(getBidValue(SpecialBid.DOUBLE)).toBe(0);
        expect(getBidValue(SpecialBid.REDOUBLE)).toBe(0);
      });
    });

    describe('compareBids', () => {
      it('should correctly compare contract bids', () => {
        const oneClub = { level: BidLevel.ONE, suit: BidSuit.CLUBS };
        const oneDiamond = { level: BidLevel.ONE, suit: BidSuit.DIAMONDS };
        const twoClubs = { level: BidLevel.TWO, suit: BidSuit.CLUBS };
        
        expect(compareBids(oneDiamond, oneClub)).toBeGreaterThan(0);
        expect(compareBids(oneClub, oneDiamond)).toBeLessThan(0);
        expect(compareBids(twoClubs, oneClub)).toBeGreaterThan(0);
        expect(compareBids(oneClub, oneClub)).toBe(0);
      });
    });

    describe('isBidHigher', () => {
      it('should correctly identify higher bids', () => {
        const oneClub = { level: BidLevel.ONE, suit: BidSuit.CLUBS };
        const oneDiamond = { level: BidLevel.ONE, suit: BidSuit.DIAMONDS };
        
        expect(isBidHigher(oneDiamond, oneClub)).toBe(true);
        expect(isBidHigher(oneClub, oneDiamond)).toBe(false);
      });
    });
  });

  describe('bid validation', () => {
    const createBid = (player: Position, value: BidValue): Bid => ({
      player,
      value,
      timestamp: new Date()
    });

    describe('validateBid', () => {
      it('should allow pass at any time', () => {
        const auction: Auction = [];
        const result = validateBid(SpecialBid.PASS, auction, Position.NORTH);
        expect(result.isValid).toBe(true);
      });

      it('should allow first contract bid', () => {
        const auction: Auction = [];
        const bid = { level: BidLevel.ONE, suit: BidSuit.CLUBS };
        const result = validateBid(bid, auction, Position.NORTH);
        expect(result.isValid).toBe(true);
      });

      it('should require higher bids', () => {
        const auction: Auction = [
          createBid(Position.NORTH, { level: BidLevel.ONE, suit: BidSuit.HEARTS })
        ];
        
        const lowerBid = { level: BidLevel.ONE, suit: BidSuit.CLUBS };
        const result = validateBid(lowerBid, auction, Position.EAST);
        expect(result.isValid).toBe(false);
        expect(result.error).toBe('Bid too low');
      });

      it('should allow higher bids', () => {
        const auction: Auction = [
          createBid(Position.NORTH, { level: BidLevel.ONE, suit: BidSuit.CLUBS })
        ];
        
        const higherBid = { level: BidLevel.ONE, suit: BidSuit.HEARTS };
        const result = validateBid(higherBid, auction, Position.EAST);
        expect(result.isValid).toBe(true);
      });

      it('should reject invalid bid levels', () => {
        const auction: Auction = [];
        const invalidBid = { level: 8 as BidLevel, suit: BidSuit.CLUBS };
        const result = validateBid(invalidBid, auction, Position.NORTH);
        expect(result.isValid).toBe(false);
        expect(result.error).toBe('Invalid bid level');
      });
    });

    describe('double validation', () => {
      it('should allow double of opponent bid', () => {
        const auction: Auction = [
          createBid(Position.NORTH, { level: BidLevel.ONE, suit: BidSuit.CLUBS })
        ];
        
        const result = validateBid(SpecialBid.DOUBLE, auction, Position.EAST);
        expect(result.isValid).toBe(true);
      });

      it('should not allow double of own partnership bid', () => {
        const auction: Auction = [
          createBid(Position.NORTH, { level: BidLevel.ONE, suit: BidSuit.CLUBS })
        ];
        
        const result = validateBid(SpecialBid.DOUBLE, auction, Position.SOUTH);
        expect(result.isValid).toBe(false);
        expect(result.error).toBe('Cannot double own partnership');
      });

      it('should not allow double without a bid', () => {
        const auction: Auction = [];
        const result = validateBid(SpecialBid.DOUBLE, auction, Position.NORTH);
        expect(result.isValid).toBe(false);
        expect(result.error).toBe('Cannot double without a bid');
      });

      it('should not allow double of special bid', () => {
        const auction: Auction = [
          createBid(Position.NORTH, SpecialBid.PASS)
        ];
        
        const result = validateBid(SpecialBid.DOUBLE, auction, Position.EAST);
        expect(result.isValid).toBe(false);
        expect(result.error).toBe('Cannot double special bid');
      });
    });

    describe('redouble validation', () => {
      it('should allow redouble after double', () => {
        const auction: Auction = [
          createBid(Position.NORTH, { level: BidLevel.ONE, suit: BidSuit.CLUBS }),
          createBid(Position.EAST, SpecialBid.DOUBLE)
        ];
        
        const result = validateBid(SpecialBid.REDOUBLE, auction, Position.NORTH);
        expect(result.isValid).toBe(true);
      });

      it('should not allow redouble without double', () => {
        const auction: Auction = [
          createBid(Position.NORTH, { level: BidLevel.ONE, suit: BidSuit.CLUBS })
        ];
        
        const result = validateBid(SpecialBid.REDOUBLE, auction, Position.NORTH);
        expect(result.isValid).toBe(false);
        expect(result.error).toBe('Cannot redouble without double');
      });

      it('should not allow redouble of opponent bid', () => {
        const auction: Auction = [
          createBid(Position.NORTH, { level: BidLevel.ONE, suit: BidSuit.CLUBS }),
          createBid(Position.EAST, SpecialBid.DOUBLE)
        ];
        
        const result = validateBid(SpecialBid.REDOUBLE, auction, Position.WEST);
        expect(result.isValid).toBe(false);
        expect(result.error).toBe('Cannot redouble opponent bid');
      });
    });
  });

  describe('auction analysis', () => {
    const createBid = (player: Position, value: BidValue): Bid => ({
      player,
      value,
      timestamp: new Date()
    });

    describe('getLastContractBid', () => {
      it('should return null for empty auction', () => {
        expect(getLastContractBid([])).toBeNull();
      });

      it('should return null for auction with only special bids', () => {
        const auction: Auction = [
          createBid(Position.NORTH, SpecialBid.PASS),
          createBid(Position.EAST, SpecialBid.PASS)
        ];
        expect(getLastContractBid(auction)).toBeNull();
      });

      it('should return last contract bid', () => {
        const contractBid = createBid(Position.NORTH, { level: BidLevel.ONE, suit: BidSuit.CLUBS });
        const auction: Auction = [
          contractBid,
          createBid(Position.EAST, SpecialBid.PASS),
          createBid(Position.SOUTH, SpecialBid.DOUBLE)
        ];
        
        expect(getLastContractBid(auction)).toEqual(contractBid);
      });
    });

    describe('isAuctionComplete', () => {
      it('should return false for empty auction', () => {
        expect(isAuctionComplete([])).toBe(false);
      });

      it('should return false for auction with less than 4 bids', () => {
        const auction: Auction = [
          createBid(Position.NORTH, { level: BidLevel.ONE, suit: BidSuit.CLUBS }),
          createBid(Position.EAST, SpecialBid.PASS)
        ];
        expect(isAuctionComplete(auction)).toBe(false);
      });

      it('should return true for completed auction', () => {
        const auction: Auction = [
          createBid(Position.NORTH, { level: BidLevel.ONE, suit: BidSuit.CLUBS }),
          createBid(Position.EAST, SpecialBid.PASS),
          createBid(Position.SOUTH, SpecialBid.PASS),
          createBid(Position.WEST, SpecialBid.PASS),
        ];
        expect(isAuctionComplete(auction)).toBe(true);
      });

      it('should return false if last three bids are not all passes', () => {
        const auction: Auction = [
          createBid(Position.NORTH, { level: BidLevel.ONE, suit: BidSuit.CLUBS }),
          createBid(Position.EAST, SpecialBid.PASS),
          createBid(Position.SOUTH, SpecialBid.PASS),
          createBid(Position.WEST, { level: BidLevel.TWO, suit: BidSuit.CLUBS })
        ];
        expect(isAuctionComplete(auction)).toBe(false);
      });
    });

    describe('isAuctionPassedOut', () => {
      it('should return true for four passes', () => {
        const auction: Auction = [
          createBid(Position.NORTH, SpecialBid.PASS),
          createBid(Position.EAST, SpecialBid.PASS),
          createBid(Position.SOUTH, SpecialBid.PASS),
          createBid(Position.WEST, SpecialBid.PASS),
        ];
        expect(isAuctionPassedOut(auction)).toBe(true);
      });

      it('should return false for auction with contract bid', () => {
        const auction: Auction = [
          createBid(Position.NORTH, { level: BidLevel.ONE, suit: BidSuit.CLUBS }),
          createBid(Position.EAST, SpecialBid.PASS),
          createBid(Position.SOUTH, SpecialBid.PASS),
          createBid(Position.WEST, SpecialBid.PASS),
        ];
        expect(isAuctionPassedOut(auction)).toBe(false);
      });
    });

    describe('getContract', () => {
      it('should return null for incomplete auction', () => {
        const auction: Auction = [
          createBid(Position.NORTH, { level: BidLevel.ONE, suit: BidSuit.CLUBS })
        ];
        expect(getContract(auction)).toBeNull();
      });

      it('should return null for passed out auction', () => {
        const auction: Auction = [
          createBid(Position.NORTH, SpecialBid.PASS),
          createBid(Position.EAST, SpecialBid.PASS),
          createBid(Position.SOUTH, SpecialBid.PASS),
          createBid(Position.WEST, SpecialBid.PASS),
        ];
        expect(getContract(auction)).toBeNull();
      });

      it('should return correct contract for completed auction', () => {
        const auction: Auction = [
          createBid(Position.NORTH, { level: BidLevel.ONE, suit: BidSuit.CLUBS }),
          createBid(Position.EAST, SpecialBid.PASS),
          createBid(Position.SOUTH, SpecialBid.PASS),
          createBid(Position.WEST, SpecialBid.PASS),
        ];
        
        const contract = getContract(auction);
        expect(contract).not.toBeNull();
        expect(contract!.level).toBe(BidLevel.ONE);
        expect(contract!.suit).toBe(BidSuit.CLUBS);
        expect(contract!.declarer).toBe(Position.NORTH);
        expect(contract!.doubled).toBe('none');
      });

      it('should handle doubled contracts', () => {
        const auction: Auction = [
          createBid(Position.NORTH, { level: BidLevel.ONE, suit: BidSuit.CLUBS }),
          createBid(Position.EAST, SpecialBid.DOUBLE),
          createBid(Position.SOUTH, SpecialBid.PASS),
          createBid(Position.WEST, SpecialBid.PASS),
        ];
        
        const contract = getContract(auction);
        expect(contract!.doubled).toBe('doubled');
      });

      it('should handle redoubled contracts', () => {
        const auction: Auction = [
          createBid(Position.NORTH, { level: BidLevel.ONE, suit: BidSuit.CLUBS }),
          createBid(Position.EAST, SpecialBid.DOUBLE),
          createBid(Position.SOUTH, SpecialBid.REDOUBLE),
          createBid(Position.WEST, SpecialBid.PASS),
          createBid(Position.NORTH, SpecialBid.PASS)
        ];
        
        const contract = getContract(auction);
        expect(contract).not.toBeNull();
        expect(contract!.doubled).toBe('redoubled');
      });
    });
  });

  describe('utility functions', () => {
    describe('formatBid', () => {
      it('should format contract bids correctly', () => {
        expect(formatBid({ level: BidLevel.ONE, suit: BidSuit.CLUBS })).toBe('1C');
        expect(formatBid({ level: BidLevel.SEVEN, suit: BidSuit.NO_TRUMP })).toBe('7NT');
      });

      it('should format special bids correctly', () => {
        expect(formatBid(SpecialBid.PASS)).toBe('Pass');
        expect(formatBid(SpecialBid.DOUBLE)).toBe('Double');
        expect(formatBid(SpecialBid.REDOUBLE)).toBe('Redouble');
      });
    });

    describe('getNextBidder', () => {
      it('should return next position in clockwise order', () => {
        expect(getNextBidder(Position.NORTH)).toBe(Position.EAST);
        expect(getNextBidder(Position.EAST)).toBe(Position.SOUTH);
        expect(getNextBidder(Position.SOUTH)).toBe(Position.WEST);
        expect(getNextBidder(Position.WEST)).toBe(Position.NORTH);
      });
    });
  });
});

/**
 * Bridge scoring utility functions
 * Implements standard bridge scoring rules for contracts, bonuses, and penalties
 */

import { Contract, BidSuit, BidLevel, GameScore, ScoreResult, Trick } from '../../types/bridge';

/**
 * Calculates the score for a completed contract
 */
export function calculateContractScore(
  contract: Contract,
  tricksTaken: number,
  vulnerable: boolean
): number {
  const { level, suit, doubled } = contract;
  const tricksNeeded = 6 + level;
  const overtricks = Math.max(0, tricksTaken - tricksNeeded);
  const undertricks = Math.max(0, tricksNeeded - tricksTaken);

  if (undertricks > 0) {
    // Contract failed - calculate penalty
    return -calculatePenalty(undertricks, doubled, vulnerable);
  }

  // Contract made - calculate score
  let score = 0;

  // Basic contract score
  const basicScore = calculateBasicScore(level, suit);
  
  // Apply doubling to basic score
  let contractScore = basicScore;
  if (doubled === 'doubled') {
    contractScore *= 2;
  } else if (doubled === 'redoubled') {
    contractScore *= 4;
  }
  
  score += contractScore;

  // Game bonus
  if (contractScore >= 100) {
    score += vulnerable ? 500 : 300; // Game bonus
  } else {
    score += 50; // Part game bonus
  }

  // Slam bonuses
  if (level === 6) { // Small slam
    score += vulnerable ? 750 : 500;
  } else if (level === 7) { // Grand slam
    score += vulnerable ? 1500 : 1000;
  }

  // Double/redouble bonus
  if (doubled === 'doubled') {
    score += 50;
  } else if (doubled === 'redoubled') {
    score += 100;
  }

  // Overtrick bonuses
  if (overtricks > 0) {
    score += calculateOvertrickBonus(overtricks, doubled, vulnerable);
  }

  return score;
}

/**
 * Calculates detailed score breakdown for a contract
 */
export function calculateDetailedScore(
  contract: Contract,
  tricksTaken: number,
  vulnerable: boolean,
  honors?: string[]
): GameScore {
  const { level, suit, doubled, declarer } = contract;
  const tricksNeeded = 6 + level;
  const overtricks = Math.max(0, tricksTaken - tricksNeeded);
  const undertricks = Math.max(0, tricksNeeded - tricksTaken);
  const made = undertricks === 0;

  // Initialize score results
  const declaringResult: ScoreResult = {
    contractPoints: 0,
    overtricks: overtricks,
    undertricks: undertricks,
    bonuses: {
      game: 0,
      slam: 0,
      rubber: 0,
      honors: 0,
      insult: 0
    },
    penalties: 0,
    totalScore: 0
  };

  const defendingResult: ScoreResult = {
    contractPoints: 0,
    overtricks: 0,
    undertricks: 0,
    bonuses: {
      game: 0,
      slam: 0,
      rubber: 0,
      honors: 0,
      insult: 0
    },
    penalties: 0,
    totalScore: 0
  };

  if (made) {
    // Contract made - declaring side scores
    const basicScore = calculateBasicScore(level, suit);

    // Apply doubling to basic score
    let contractScore = basicScore;
    if (doubled === 'doubled') {
      contractScore *= 2;
    } else if (doubled === 'redoubled') {
      contractScore *= 4;
    }

    declaringResult.contractPoints = contractScore;

    // Game bonus
    if (contractScore >= 100) {
      declaringResult.bonuses.game = vulnerable ? 500 : 300;
    } else {
      declaringResult.bonuses.game = 50; // Part game bonus
    }

    // Slam bonuses
    if (level === BidLevel.SIX) { // Small slam
      declaringResult.bonuses.slam = vulnerable ? 750 : 500;
    } else if (level === BidLevel.SEVEN) { // Grand slam
      declaringResult.bonuses.slam = vulnerable ? 1500 : 1000;
    }

    // Double/redouble bonus
    if (doubled === 'doubled') {
      declaringResult.bonuses.insult = 50;
    } else if (doubled === 'redoubled') {
      declaringResult.bonuses.insult = 100;
    }

    // Overtrick bonuses
    if (overtricks > 0) {
      const overtrickBonus = calculateOvertrickBonus(overtricks, doubled, vulnerable);
      declaringResult.contractPoints += overtrickBonus;
    }

    // Honor bonuses
    if (honors) {
      declaringResult.bonuses.honors = calculateHonorBonus(honors, suit);
    }

    declaringResult.totalScore = declaringResult.contractPoints +
                                declaringResult.bonuses.game +
                                declaringResult.bonuses.slam +
                                declaringResult.bonuses.rubber +
                                declaringResult.bonuses.honors +
                                declaringResult.bonuses.insult;
  } else {
    // Contract failed - defending side scores penalties
    const penalty = calculatePenalty(undertricks, doubled, vulnerable);
    defendingResult.penalties = penalty;
    defendingResult.totalScore = penalty;
  }

  // Determine which partnership is declaring
  const isNorthSouthDeclaring = declarer === 'N' || declarer === 'S';

  return {
    northSouth: isNorthSouthDeclaring ? declaringResult : defendingResult,
    eastWest: isNorthSouthDeclaring ? defendingResult : declaringResult
  };
}

/**
 * Calculates basic contract score before bonuses
 */
function calculateBasicScore(level: number, suit: BidSuit): number {
  switch (suit) {
    case BidSuit.CLUBS:
    case BidSuit.DIAMONDS:
      return level * 20; // Minor suits: 20 per level
    case BidSuit.HEARTS:
    case BidSuit.SPADES:
      return level * 30; // Major suits: 30 per level
    case BidSuit.NO_TRUMP:
      return 40 + (level - 1) * 30; // NT: 40 for first, 30 for each additional
    default:
      return 0;
  }
}

/**
 * Calculates penalty for failed contracts
 */
function calculatePenalty(
  undertricks: number,
  doubled: 'none' | 'doubled' | 'redoubled',
  vulnerable: boolean
): number {
  let penalty = 0;

  if (doubled === 'none') {
    // Undoubled penalties
    penalty = undertricks * (vulnerable ? 100 : 50);
  } else {
    // Doubled/redoubled penalties
    for (let i = 1; i <= undertricks; i++) {
      if (i === 1) {
        penalty += vulnerable ? 200 : 100;
      } else if (i <= 3) {
        penalty += vulnerable ? 300 : 200;
      } else {
        penalty += 300;
      }
    }
    
    if (doubled === 'redoubled') {
      penalty *= 2;
    }
  }

  return penalty;
}

/**
 * Calculates bonus for overtricks
 */
function calculateOvertrickBonus(
  overtricks: number,
  doubled: 'none' | 'doubled' | 'redoubled',
  vulnerable: boolean
): number {
  if (doubled === 'none') {
    // Undoubled overtricks
    return overtricks * (vulnerable ? 30 : 20);
  } else {
    // Doubled/redoubled overtricks
    let bonus = overtricks * (vulnerable ? 200 : 100);
    if (doubled === 'redoubled') {
      bonus *= 2;
    }
    return bonus;
  }
}

/**
 * Checks if a partnership is vulnerable
 */
export function isVulnerable(vulnerabilityStatus: boolean): boolean {
  return vulnerabilityStatus;
}

/**
 * Calculates honor bonuses (if honors are held)
 */
export function calculateHonorBonus(
  honors: string[],
  trumpSuit: BidSuit | null
): number {
  // This is a simplified implementation
  // In a full implementation, you'd check for:
  // - 4 honors in trump suit: 100 points
  // - 5 honors in trump suit: 150 points
  // - 4 aces in NT: 150 points
  
  if (!trumpSuit || trumpSuit === BidSuit.NO_TRUMP) {
    // Check for 4 aces in NT
    const aces = honors.filter(h => h.includes('A')).length;
    if (aces === 4) return 150;
  } else {
    // Check for honors in trump suit
    const trumpHonors = honors.filter(h => 
      h.includes(trumpSuit) && ['A', 'K', 'Q', 'J', '10'].some(rank => h.includes(rank))
    ).length;
    
    if (trumpHonors === 5) return 150;
    if (trumpHonors === 4) return 100;
  }
  
  return 0;
}

/**
 * Determines if a game has been won (100+ points)
 */
export function isGameWon(score: number): boolean {
  return score >= 100;
}

/**
 * Determines if a rubber has been won (first to win 2 games)
 */
export function isRubberWon(gamesWon: number): boolean {
  return gamesWon >= 2;
}

/**
 * Calculates rubber bonus
 */
export function calculateRubberBonus(
  winnerGames: number,
  loserGames: number
): number {
  if (winnerGames >= 2) {
    if (loserGames === 0) {
      return 700; // Won 2-0
    } else {
      return 500; // Won 2-1
    }
  }
  return 0;
}

/**
 * Formats score for display
 */
export function formatScore(score: number): string {
  if (score === 0) return '0';
  if (score > 0) return `+${score}`;
  return score.toString();
}

/**
 * Calculates total match points for tournament scoring
 */
export function calculateMatchPoints(
  ourScore: number,
  theirScore: number,
  totalBoards: number
): number {
  const difference = ourScore - theirScore;
  if (difference > 0) {
    return 2; // Win
  } else if (difference === 0) {
    return 1; // Tie
  } else {
    return 0; // Loss
  }
}

/**
 * Determines vulnerability for a given board number (tournament play)
 */
export function getVulnerabilityForBoard(boardNumber: number): {
  northSouth: boolean;
  eastWest: boolean;
} {
  const cycle = boardNumber % 16;
  
  // Standard vulnerability cycle
  const vulnerabilities = [
    { northSouth: false, eastWest: false }, // Board 1
    { northSouth: true, eastWest: false },  // Board 2
    { northSouth: false, eastWest: true },  // Board 3
    { northSouth: true, eastWest: true },   // Board 4
    { northSouth: true, eastWest: false },  // Board 5
    { northSouth: false, eastWest: true },  // Board 6
    { northSouth: true, eastWest: true },   // Board 7
    { northSouth: false, eastWest: false }, // Board 8
    { northSouth: false, eastWest: true },  // Board 9
    { northSouth: true, eastWest: true },   // Board 10
    { northSouth: false, eastWest: false }, // Board 11
    { northSouth: true, eastWest: false },  // Board 12
    { northSouth: true, eastWest: true },   // Board 13
    { northSouth: false, eastWest: false }, // Board 14
    { northSouth: true, eastWest: false },  // Board 15
    { northSouth: false, eastWest: true }   // Board 16
  ];
  
  return vulnerabilities[cycle] || { northSouth: false, eastWest: false };
}

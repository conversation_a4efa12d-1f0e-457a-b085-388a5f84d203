/**
 * Tests for game phase transitions
 * Verifies correct transitions between all game phases with proper state management
 */

import {
  createNewGame,
  createPlayers,
  processBid,
  playCard,
  processScoring,
  canTransitionPhase,
  getNextPhase,
  autoTransitionPhase,
  validatePhaseConsistency
} from './gameStateUtils';

import {
  Position,
  GamePhase,
  BidLevel,
  BidSuit,
  SpecialBid,
  Suit,
  Rank
} from '../../types/bridge';

describe('Game Phase Transitions', () => {
  let gameState: any;

  beforeEach(() => {
    const players = createPlayers(Position.SOUTH);
    gameState = createNewGame('test-game', players, Position.NORTH);
  });

  describe('Phase Transition Validation', () => {
    it('should validate correct phase transitions', () => {
      // From BIDDING
      expect(canTransitionPhase(gameState, GamePhase.PLAYING)).toBe(true);
      expect(canTransitionPhase(gameState, GamePhase.FINISHED)).toBe(true);
      expect(canTransitionPhase(gameState, GamePhase.SCORING)).toBe(false);
      expect(canTransitionPhase(gameState, GamePhase.COMPLETE)).toBe(false);

      // Transition to PLAYING
      gameState.phase = GamePhase.PLAYING;
      expect(canTransitionPhase(gameState, GamePhase.SCORING)).toBe(true);
      expect(canTransitionPhase(gameState, GamePhase.BIDDING)).toBe(false);
      expect(canTransitionPhase(gameState, GamePhase.FINISHED)).toBe(false);

      // Transition to SCORING
      gameState.phase = GamePhase.SCORING;
      expect(canTransitionPhase(gameState, GamePhase.COMPLETE)).toBe(true);
      expect(canTransitionPhase(gameState, GamePhase.BIDDING)).toBe(true); // Next hand
      expect(canTransitionPhase(gameState, GamePhase.PLAYING)).toBe(false);

      // Terminal phase
      gameState.phase = GamePhase.COMPLETE;
      expect(canTransitionPhase(gameState, GamePhase.BIDDING)).toBe(false);
      expect(canTransitionPhase(gameState, GamePhase.PLAYING)).toBe(false);
    });

    it('should get correct next phase', () => {
      expect(getNextPhase(gameState)).toBe(null); // BIDDING depends on auction

      gameState.phase = GamePhase.PLAYING;
      expect(getNextPhase(gameState)).toBe(GamePhase.SCORING);

      gameState.phase = GamePhase.SCORING;
      expect(getNextPhase(gameState)).toBe(GamePhase.COMPLETE);

      gameState.phase = GamePhase.FINISHED;
      expect(getNextPhase(gameState)).toBe(GamePhase.COMPLETE);

      gameState.phase = GamePhase.COMPLETE;
      expect(getNextPhase(gameState)).toBe(null); // Terminal
    });
  });

  describe('Bidding to Playing Transition', () => {
    it('should transition correctly after successful auction', () => {
      expect(gameState.phase).toBe(GamePhase.BIDDING);

      const bids = [
        { player: Position.EAST, value: { level: BidLevel.ONE, suit: BidSuit.SPADES }, timestamp: new Date() },
        { player: Position.SOUTH, value: SpecialBid.PASS, timestamp: new Date() },
        { player: Position.WEST, value: SpecialBid.PASS, timestamp: new Date() },
        { player: Position.NORTH, value: SpecialBid.PASS, timestamp: new Date() }
      ];

      let currentState = gameState;
      for (const bid of bids) {
        currentState = processBid(currentState, bid);
      }

      expect(currentState.phase).toBe(GamePhase.PLAYING);
      expect(currentState.contract).not.toBeNull();
      expect(currentState.dummy).not.toBeNull();
      expect(currentState.currentPlayer).toBe(Position.SOUTH); // Left of declarer
    });

    it('should transition to finished for all-pass auction', () => {
      const bids = [
        { player: Position.EAST, value: SpecialBid.PASS, timestamp: new Date() },
        { player: Position.SOUTH, value: SpecialBid.PASS, timestamp: new Date() },
        { player: Position.WEST, value: SpecialBid.PASS, timestamp: new Date() },
        { player: Position.NORTH, value: SpecialBid.PASS, timestamp: new Date() }
      ];

      let currentState = gameState;
      for (const bid of bids) {
        currentState = processBid(currentState, bid);
      }

      expect(currentState.phase).toBe(GamePhase.FINISHED);
      expect(currentState.contract).toBeNull();
    });
  });

  describe('Playing to Scoring Transition', () => {
    it('should transition to scoring after 13 tricks', () => {
      // Set up playing phase
      const bids = [
        { player: Position.EAST, value: { level: BidLevel.ONE, suit: BidSuit.CLUBS }, timestamp: new Date() },
        { player: Position.SOUTH, value: SpecialBid.PASS, timestamp: new Date() },
        { player: Position.WEST, value: SpecialBid.PASS, timestamp: new Date() },
        { player: Position.NORTH, value: SpecialBid.PASS, timestamp: new Date() }
      ];

      let currentState = gameState;
      for (const bid of bids) {
        currentState = processBid(currentState, bid);
      }

      expect(currentState.phase).toBe(GamePhase.PLAYING);

      // Set up hands for quick completion
      const positions = [Position.SOUTH, Position.WEST, Position.NORTH, Position.EAST];
      positions.forEach(pos => {
        const hand = [];
        for (let i = 0; i < 13; i++) {
          hand.push({ suit: Suit.HEARTS, rank: Rank.TWO });
        }
        currentState.players[pos].hand = hand;
      });

      // Play all 13 tricks
      for (let trick = 0; trick < 13; trick++) {
        for (let card = 0; card < 4; card++) {
          const cardToPlay = { suit: Suit.HEARTS, rank: Rank.TWO };
          const currentPlayer = currentState.currentPlayer;
          currentState = playCard(currentState, cardToPlay, currentPlayer);
        }
      }

      expect(currentState.phase).toBe(GamePhase.SCORING);
      expect(currentState.tricks).toHaveLength(13);
      expect(currentState.currentTrick).toHaveLength(0);
    });

    it('should not transition to scoring before all tricks complete', () => {
      // Set up playing phase
      const bids = [
        { player: Position.EAST, value: { level: BidLevel.ONE, suit: BidSuit.CLUBS }, timestamp: new Date() },
        { player: Position.SOUTH, value: SpecialBid.PASS, timestamp: new Date() },
        { player: Position.WEST, value: SpecialBid.PASS, timestamp: new Date() },
        { player: Position.NORTH, value: SpecialBid.PASS, timestamp: new Date() }
      ];

      let currentState = gameState;
      for (const bid of bids) {
        currentState = processBid(currentState, bid);
      }

      // Set up hands
      const positions = [Position.SOUTH, Position.WEST, Position.NORTH, Position.EAST];
      positions.forEach(pos => {
        currentState.players[pos].hand = [{ suit: Suit.HEARTS, rank: Rank.ACE }];
      });

      // Play only one trick
      for (let card = 0; card < 4; card++) {
        const cardToPlay = { suit: Suit.HEARTS, rank: Rank.ACE };
        const currentPlayer = currentState.currentPlayer;
        currentState = playCard(currentState, cardToPlay, currentPlayer);
      }

      expect(currentState.phase).toBe(GamePhase.PLAYING); // Still playing
      expect(currentState.tricks).toHaveLength(1);
    });
  });

  describe('Scoring to Complete Transition', () => {
    it('should process scoring and transition to complete', () => {
      // Set up scoring phase
      gameState.phase = GamePhase.SCORING;
      gameState.contract = {
        level: BidLevel.ONE,
        suit: BidSuit.CLUBS,
        declarer: Position.EAST,
        doubled: 'none'
      };
      
      // Set up 13 completed tricks
      gameState.tricks = [];
      for (let i = 0; i < 13; i++) {
        gameState.tricks.push({
          id: i + 1,
          cards: [
            { card: { suit: Suit.HEARTS, rank: Rank.ACE }, player: Position.NORTH },
            { card: { suit: Suit.HEARTS, rank: Rank.KING }, player: Position.EAST },
            { card: { suit: Suit.HEARTS, rank: Rank.QUEEN }, player: Position.SOUTH },
            { card: { suit: Suit.HEARTS, rank: Rank.JACK }, player: Position.WEST }
          ],
          winner: Position.NORTH,
          leadPlayer: Position.NORTH
        });
      }

      const newGameState = processScoring(gameState);
      expect(newGameState.phase).toBe(GamePhase.COMPLETE);
    });

    it('should reject scoring with incomplete tricks', () => {
      gameState.phase = GamePhase.SCORING;
      gameState.tricks = []; // No tricks completed

      expect(() => {
        processScoring(gameState);
      }).toThrow('Cannot score incomplete hand');
    });

    it('should reject scoring outside scoring phase', () => {
      gameState.phase = GamePhase.PLAYING;

      expect(() => {
        processScoring(gameState);
      }).toThrow('Cannot process scoring outside of scoring phase');
    });
  });

  describe('Auto Phase Transitions', () => {
    it('should auto-transition from scoring to complete', () => {
      gameState.phase = GamePhase.SCORING;
      gameState.contract = {
        level: BidLevel.ONE,
        suit: BidSuit.CLUBS,
        declarer: Position.EAST,
        doubled: 'none'
      };
      
      // Set up 13 completed tricks
      gameState.tricks = new Array(13).fill(null).map((_, i) => ({
        id: i + 1,
        cards: [],
        winner: Position.NORTH,
        leadPlayer: Position.NORTH
      }));

      const newGameState = autoTransitionPhase(gameState);
      expect(newGameState.phase).toBe(GamePhase.COMPLETE);
    });

    it('should auto-transition from finished to complete', () => {
      gameState.phase = GamePhase.FINISHED;
      gameState.contract = null; // All passed

      const newGameState = autoTransitionPhase(gameState);
      expect(newGameState.phase).toBe(GamePhase.COMPLETE);
    });

    it('should not auto-transition other phases', () => {
      gameState.phase = GamePhase.BIDDING;
      const newGameState = autoTransitionPhase(gameState);
      expect(newGameState.phase).toBe(GamePhase.BIDDING); // No change
    });
  });

  describe('Phase Consistency Validation', () => {
    it('should validate bidding phase consistency', () => {
      gameState.phase = GamePhase.BIDDING;
      gameState.contract = null;
      gameState.dummy = null;
      gameState.tricks = [];

      const validation = validatePhaseConsistency(gameState);
      expect(validation.isValid).toBe(true);
      expect(validation.errors).toHaveLength(0);
    });

    it('should detect bidding phase inconsistencies', () => {
      gameState.phase = GamePhase.BIDDING;
      gameState.contract = { level: BidLevel.ONE, suit: BidSuit.CLUBS, declarer: Position.EAST, doubled: 'none' };
      gameState.dummy = Position.WEST;

      const validation = validatePhaseConsistency(gameState);
      expect(validation.isValid).toBe(false);
      expect(validation.errors).toContain('Contract should be null during bidding phase');
      expect(validation.errors).toContain('Dummy should be null during bidding phase');
    });

    it('should validate playing phase consistency', () => {
      gameState.phase = GamePhase.PLAYING;
      gameState.contract = { level: BidLevel.ONE, suit: BidSuit.CLUBS, declarer: Position.EAST, doubled: 'none' };
      gameState.dummy = Position.WEST;
      gameState.tricks = [];

      const validation = validatePhaseConsistency(gameState);
      expect(validation.isValid).toBe(true);
    });

    it('should detect playing phase inconsistencies', () => {
      gameState.phase = GamePhase.PLAYING;
      gameState.contract = null;
      gameState.dummy = null;

      const validation = validatePhaseConsistency(gameState);
      expect(validation.isValid).toBe(false);
      expect(validation.errors).toContain('Contract should not be null during playing phase');
      expect(validation.errors).toContain('Dummy should not be null during playing phase');
    });

    it('should validate scoring phase consistency', () => {
      gameState.phase = GamePhase.SCORING;
      gameState.contract = { level: BidLevel.ONE, suit: BidSuit.CLUBS, declarer: Position.EAST, doubled: 'none' };
      gameState.tricks = new Array(13).fill(null);
      gameState.currentTrick = [];

      const validation = validatePhaseConsistency(gameState);
      expect(validation.isValid).toBe(true);
    });

    it('should detect scoring phase inconsistencies', () => {
      gameState.phase = GamePhase.SCORING;
      gameState.contract = null;
      gameState.tricks = []; // Not 13 tricks
      gameState.currentTrick = [{ card: { suit: Suit.HEARTS, rank: Rank.ACE }, player: Position.NORTH }];

      const validation = validatePhaseConsistency(gameState);
      expect(validation.isValid).toBe(false);
      expect(validation.errors).toContain('Contract should not be null during scoring phase');
      expect(validation.errors).toContain('All 13 tricks should be complete during scoring phase');
      expect(validation.errors).toContain('Current trick should be empty during scoring phase');
    });
  });
});

/**
 * Tests for data model consistency and immutable state updates
 * Verifies that the single source of truth for cards works correctly
 */

import {
  createNewGame,
  createPlayers,
  processBid,
  playCard,
  validateGameState,
  getCurrentPlayerHand,
  getDummyHand
} from './gameStateUtils';

import {
  Position,
  PlayerType,
  GamePhase,
  BidLevel,
  BidSuit,
  SpecialBid,
  Suit,
  Rank,
  Card
} from '../../types/bridge';

describe('Data Model Consistency', () => {
  describe('Single Source of Truth', () => {
    it('should store cards only in players[].hand, not in separate hands field', () => {
      const players = createPlayers(Position.SOUTH);
      const gameState = createNewGame('test-game', players, Position.NORTH);
      
      // Verify gameState.hands field doesn't exist
      expect((gameState as any).hands).toBeUndefined();
      
      // Verify cards are stored in players[].hand
      expect(gameState.players[Position.NORTH].hand).toHaveLength(13);
      expect(gameState.players[Position.EAST].hand).toHaveLength(13);
      expect(gameState.players[Position.SOUTH].hand).toHaveLength(13);
      expect(gameState.players[Position.WEST].hand).toHaveLength(13);
      
      // Verify total cards is 52
      const totalCards = Object.values(gameState.players)
        .reduce((sum, player) => sum + player.hand.length, 0);
      expect(totalCards).toBe(52);
    });

    it('should maintain card consistency during game state mutations', () => {
      const players = createPlayers(Position.SOUTH);
      let gameState = createNewGame('test-game', players, Position.NORTH);
      
      // Complete bidding to get to playing phase
      const bids = [
        { player: Position.EAST, value: { level: BidLevel.ONE, suit: BidSuit.CLUBS }, timestamp: new Date() },
        { player: Position.SOUTH, value: SpecialBid.PASS, timestamp: new Date() },
        { player: Position.WEST, value: SpecialBid.PASS, timestamp: new Date() },
        { player: Position.NORTH, value: SpecialBid.PASS, timestamp: new Date() }
      ];
      
      for (const bid of bids) {
        gameState = processBid(gameState, bid);
      }
      
      // Verify we're in playing phase
      expect(gameState.phase).toBe(GamePhase.PLAYING);
      
      // Play a card
      const currentPlayerHand = getCurrentPlayerHand(gameState);
      const cardToPlay = currentPlayerHand[0];
      const originalHandSize = currentPlayerHand.length;
      
      const newGameState = playCard(gameState, cardToPlay, gameState.currentPlayer);
      
      // Verify card was removed from hand
      expect(newGameState.players[gameState.currentPlayer].hand).toHaveLength(originalHandSize - 1);
      
      // Verify card is not in the new hand
      const newHand = newGameState.players[gameState.currentPlayer].hand;
      expect(newHand.some(card => card.rank === cardToPlay.rank && card.suit === cardToPlay.suit)).toBe(false);
      
      // Verify card was added to current trick
      expect(newGameState.currentTrick).toHaveLength(1);
      expect(newGameState.currentTrick[0].card).toEqual(cardToPlay);
    });
  });

  describe('Immutable State Updates', () => {
    it('should not mutate original game state when playing cards', () => {
      const players = createPlayers(Position.SOUTH);
      let gameState = createNewGame('test-game', players, Position.NORTH);
      
      // Complete bidding
      const bids = [
        { player: Position.EAST, value: { level: BidLevel.ONE, suit: BidSuit.CLUBS }, timestamp: new Date() },
        { player: Position.SOUTH, value: SpecialBid.PASS, timestamp: new Date() },
        { player: Position.WEST, value: SpecialBid.PASS, timestamp: new Date() },
        { player: Position.NORTH, value: SpecialBid.PASS, timestamp: new Date() }
      ];
      
      for (const bid of bids) {
        gameState = processBid(gameState, bid);
      }
      
      // Store original state references
      const originalHand = gameState.players[gameState.currentPlayer].hand;
      const originalHandLength = originalHand.length;
      const originalTrickLength = gameState.currentTrick.length;
      
      // Play a card
      const cardToPlay = originalHand[0];
      const newGameState = playCard(gameState, cardToPlay, gameState.currentPlayer);
      
      // Verify original state was not mutated
      expect(gameState.players[gameState.currentPlayer].hand).toHaveLength(originalHandLength);
      expect(gameState.currentTrick).toHaveLength(originalTrickLength);
      expect(gameState.players[gameState.currentPlayer].hand).toContain(cardToPlay);
      
      // Verify new state has changes
      expect(newGameState.players[gameState.currentPlayer].hand).toHaveLength(originalHandLength - 1);
      expect(newGameState.currentTrick).toHaveLength(originalTrickLength + 1);
      expect(newGameState.players[gameState.currentPlayer].hand).not.toContain(cardToPlay);
    });

    it('should not mutate original game state when processing bids', () => {
      const players = createPlayers(Position.SOUTH);
      const gameState = createNewGame('test-game', players, Position.NORTH);
      
      // Store original state references
      const originalAuctionLength = gameState.auction.length;
      const originalCurrentPlayer = gameState.currentPlayer;
      
      // Make a bid
      const bid = {
        player: Position.EAST,
        value: { level: BidLevel.ONE, suit: BidSuit.CLUBS },
        timestamp: new Date()
      };
      
      const newGameState = processBid(gameState, bid);
      
      // Verify original state was not mutated
      expect(gameState.auction).toHaveLength(originalAuctionLength);
      expect(gameState.currentPlayer).toBe(originalCurrentPlayer);
      
      // Verify new state has changes
      expect(newGameState.auction).toHaveLength(originalAuctionLength + 1);
      expect(newGameState.currentPlayer).not.toBe(originalCurrentPlayer);
    });
  });

  describe('Data Access Consistency', () => {
    it('should provide consistent access to player hands through utility functions', () => {
      const players = createPlayers(Position.SOUTH);
      const gameState = createNewGame('test-game', players, Position.NORTH);
      
      // Test getCurrentPlayerHand
      const currentHand = getCurrentPlayerHand(gameState);
      const directHand = gameState.players[gameState.currentPlayer].hand;
      expect(currentHand).toEqual(directHand);
      expect(currentHand).toBe(directHand); // Same reference
      
      // Move to playing phase to test getDummyHand
      let playingState = gameState;
      const bids = [
        { player: Position.EAST, value: { level: BidLevel.ONE, suit: BidSuit.CLUBS }, timestamp: new Date() },
        { player: Position.SOUTH, value: SpecialBid.PASS, timestamp: new Date() },
        { player: Position.WEST, value: SpecialBid.PASS, timestamp: new Date() },
        { player: Position.NORTH, value: SpecialBid.PASS, timestamp: new Date() }
      ];
      
      for (const bid of bids) {
        playingState = processBid(playingState, bid);
      }
      
      // Test getDummyHand
      const dummyHand = getDummyHand(playingState);
      const directDummyHand = playingState.players[playingState.dummy!].hand;
      expect(dummyHand).toEqual(directDummyHand);
      expect(dummyHand).toBe(directDummyHand); // Same reference
    });
  });

  describe('Validation Consistency', () => {
    it('should validate game state correctly with single data model', () => {
      const players = createPlayers(Position.SOUTH);
      const gameState = createNewGame('test-game', players, Position.NORTH);
      
      // Valid state should pass validation
      const validation = validateGameState(gameState);
      expect(validation.isValid).toBe(true);
      expect(validation.errors).toHaveLength(0);
      
      // Manually corrupt the state to test validation
      const corruptedState = {
        ...gameState,
        players: {
          ...gameState.players,
          [Position.NORTH]: {
            ...gameState.players[Position.NORTH],
            hand: [] // Remove all cards
          }
        }
      };
      
      const corruptedValidation = validateGameState(corruptedState);
      expect(corruptedValidation.isValid).toBe(false);
      expect(corruptedValidation.errors.length).toBeGreaterThan(0);
    });
  });
});

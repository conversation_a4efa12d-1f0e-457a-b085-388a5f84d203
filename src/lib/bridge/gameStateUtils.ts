/**
 * Game state utility functions for managing bridge game state
 */

import {
  <PERSON>S<PERSON>,
  Player,
  Position,
  PlayerType,
  GamePhase,
  BidLevel,
  BidSuit,
  Card,
  Bid,
  Hand,
  Contract,
  PlayedCard,
  Trick
} from '../../types/bridge';
import { createDeck, shuffleDeck, dealCards } from './cardUtils';
import { validateCardPlay, getTrick<PERSON>inner, calculateContractResult } from './trickUtils';
import { isValidBid, isAuctionComplete, getFinalContract } from './biddingUtils';
import { calculateContractScore, calculateDetailedScore } from './scoringUtils';

/**
 * Gets the next player in clockwise order
 */
function getNextPlayer(position: Position): Position {
  const positions = [Position.NORTH, Position.EAST, Position.SOUTH, Position.WEST];
  const currentIndex = positions.indexOf(position);
  return positions[(currentIndex + 1) % 4];
}

/**
 * Deep clones a game state to ensure immutability
 */
function deepCloneGameState(gameState: GameState): GameState {
  return {
    ...gameState,
    players: {
      [Position.NORTH]: {
        ...gameState.players[Position.NORTH],
        hand: [...gameState.players[Position.NORTH].hand]
      },
      [Position.EAST]: {
        ...gameState.players[Position.EAST],
        hand: [...gameState.players[Position.EAST].hand]
      },
      [Position.SOUTH]: {
        ...gameState.players[Position.SOUTH],
        hand: [...gameState.players[Position.SOUTH].hand]
      },
      [Position.WEST]: {
        ...gameState.players[Position.WEST],
        hand: [...gameState.players[Position.WEST].hand]
      }
    },
    auction: [...gameState.auction],
    tricks: gameState.tricks.map(trick => ({
      ...trick,
      cards: [...trick.cards]
    })),
    currentTrick: [...gameState.currentTrick],
    vulnerabilities: { ...gameState.vulnerabilities },
    score: { ...gameState.score },
    rubberScore: { ...gameState.rubberScore }
  };
}

/**
 * Creates a new game state
 */
export function createNewGame(
  gameId: string,
  players: Record<Position, Pick<Player, 'name' | 'type'>>,
  dealer: Position
): GameState {
  const deck = shuffleDeck(createDeck());
  const hands = dealCards(deck);

  const gameState: GameState = {
    id: gameId,
    phase: GamePhase.BIDDING,
    dealer,
    currentPlayer: getNextPlayer(dealer), // Next player after dealer starts bidding
    players: {
      [Position.NORTH]: {
        id: 'north',
        name: players[Position.NORTH].name,
        position: Position.NORTH,
        type: players[Position.NORTH].type,
        hand: hands[0]
      },
      [Position.EAST]: {
        id: 'east',
        name: players[Position.EAST].name,
        position: Position.EAST,
        type: players[Position.EAST].type,
        hand: hands[1]
      },
      [Position.SOUTH]: {
        id: 'south',
        name: players[Position.SOUTH].name,
        position: Position.SOUTH,
        type: players[Position.SOUTH].type,
        hand: hands[2]
      },
      [Position.WEST]: {
        id: 'west',
        name: players[Position.WEST].name,
        position: Position.WEST,
        type: players[Position.WEST].type,
        hand: hands[3]
      }
    },
    auction: [],
    contract: null,
    tricks: [],
    currentTrick: [],
    dummy: null,
    vulnerabilities: { northSouth: false, eastWest: false },
    score: { northSouth: 0, eastWest: 0 },
    gameNumber: 1,
    rubberScore: { northSouth: 0, eastWest: 0 }
  };
  
  return gameState;
}

/**
 * Creates player objects for a new game
 */
export function createPlayers(
  humanPosition: Position,
  humanName: string
): Record<Position, Pick<Player, 'name' | 'type'>> {
  const players: Record<Position, Pick<Player, 'name' | 'type'>> = {
    [Position.NORTH]: { name: 'AI North', type: PlayerType.AI },
    [Position.SOUTH]: { name: 'AI South', type: PlayerType.AI },
    [Position.EAST]: { name: 'AI East', type: PlayerType.AI },
    [Position.WEST]: { name: 'AI West', type: PlayerType.AI }
  };
  
  players[humanPosition] = { name: humanName, type: PlayerType.HUMAN };
  
  return players;
}

/**
 * Determines if a player can see another player's cards
 */
export function canSeeCards(
  gameState: GameState,
  viewerPosition: Position,
  targetPosition: Position
): boolean {
  // Can always see your own cards
  if (viewerPosition === targetPosition) {
    return true;
  }
  
  // During playing phase, all players can see dummy's cards
  if (gameState.phase === GamePhase.PLAYING && gameState.dummy === targetPosition) {
    return true;
  }
  
  // Otherwise can't see other players' cards
  return false;
}

/**
 * Gets the current player's hand
 */
export function getCurrentPlayerHand(gameState: GameState): Card[] {
  return gameState.players[gameState.currentPlayer].hand;
}

/**
 * Gets the dummy's hand (if dummy is revealed)
 */
export function getDummyHand(gameState: GameState): Card[] | null {
  if (!gameState.dummy || gameState.phase !== GamePhase.PLAYING) {
    return null;
  }
  return gameState.players[gameState.dummy].hand;
}

/**
 * Checks if the game is complete
 */
export function isGameComplete(gameState: GameState): boolean {
  return gameState.phase === GamePhase.FINISHED || gameState.phase === GamePhase.COMPLETE;
}

/**
 * Validates the game state for consistency and correctness
 */
export function validateGameState(gameState: GameState): { isValid: boolean; errors: string[]; error?: string } {
  const errors: string[] = [];

  // Check that all players exist
  const requiredPositions = [Position.NORTH, Position.EAST, Position.SOUTH, Position.WEST];
  for (const position of requiredPositions) {
    if (!gameState.players[position]) {
      errors.push(`Missing player at position ${position}`);
    }
  }

  // Check that all players have the correct number of cards
  const totalCards = Object.values(gameState.players).reduce((sum, player) => sum + player.hand.length, 0);
  const expectedTotalCards = gameState.phase === GamePhase.BIDDING ? 52 :
                            gameState.phase === GamePhase.PLAYING ? 52 - (gameState.tricks.length * 4 + gameState.currentTrick.length) : 52;

  if (totalCards !== expectedTotalCards) {
    errors.push(`Invalid total cards: ${totalCards}, expected ${expectedTotalCards}`);
  }

  // Check that each player has correct number of cards during bidding
  if (gameState.phase === GamePhase.BIDDING) {
    for (const [position, player] of Object.entries(gameState.players)) {
      if (player.hand.length !== 13) {
        errors.push(`Player ${position} has ${player.hand.length} cards, expected 13`);
      }
    }
  }

  // Check phase consistency
  if (gameState.phase === GamePhase.BIDDING && gameState.contract !== null) {
    errors.push('Contract should be null during bidding phase');
  }

  if (gameState.phase === GamePhase.PLAYING && gameState.contract === null) {
    errors.push('Contract should not be null during playing phase');
  }

  // Check current player is valid
  if (!requiredPositions.includes(gameState.currentPlayer)) {
    errors.push(`Invalid current player: ${gameState.currentPlayer}`);
  }

  // Check current trick doesn't exceed 4 cards
  if (gameState.currentTrick.length > 4) {
    errors.push(`Current trick has ${gameState.currentTrick.length} cards, maximum is 4`);
  }

  const isValid = errors.length === 0;
  const result: { isValid: boolean; errors: string[]; error?: string } = {
    isValid,
    errors
  };

  // Add error field for backward compatibility
  if (!isValid) {
    result.error = errors[0];
  }

  return result;
}

/**
 * Gets valid cards that can be played in the current situation using Bridge rules
 */
export function getValidCards(gameState: GameState): Card[] {
  if (gameState.phase !== GamePhase.PLAYING) {
    return [];
  }

  if (!gameState.contract) {
    return [];
  }

  // Get the hand to play from (current player's hand, or dummy if declarer is controlling)
  const effectivePlayer = gameState.currentPlayer;
  const hand = gameState.players[effectivePlayer].hand;

  // If no cards played yet in trick, any card is valid (leading)
  if (gameState.currentTrick.length === 0) {
    return [...hand];
  }

  // Filter cards based on Bridge suit-following rules
  const validCards: Card[] = [];

  for (const card of hand) {
    const validation = validateCardPlay(card, hand, gameState.currentTrick, gameState.contract);
    if (validation.isValid) {
      validCards.push(card);
    }
  }

  return validCards;
}

/**
 * Gets valid cards for a specific player (used for dummy play)
 */
export function getValidCardsForPlayer(gameState: GameState, player: Position): Card[] {
  if (gameState.phase !== GamePhase.PLAYING) {
    return [];
  }

  if (!gameState.contract) {
    return [];
  }

  const hand = gameState.players[player].hand;

  // If no cards played yet in trick, any card is valid (leading)
  if (gameState.currentTrick.length === 0) {
    return [...hand];
  }

  // Filter cards based on Bridge suit-following rules
  const validCards: Card[] = [];

  for (const card of hand) {
    const validation = validateCardPlay(card, hand, gameState.currentTrick, gameState.contract);
    if (validation.isValid) {
      validCards.push(card);
    }
  }

  return validCards;
}

/**
 * Processes a card play with comprehensive Bridge rule validation including dummy play
 */
export function playCard(
  gameState: GameState,
  card: Card,
  player: Position
): GameState {
  // Validate game is in playing phase
  if (gameState.phase !== GamePhase.PLAYING) {
    throw new Error('Cannot play cards outside of playing phase');
  }

  // Validate contract exists
  if (!gameState.contract) {
    throw new Error('Cannot play cards without a contract');
  }

  // Validate dummy play rules
  const dummyValidation = validateDummyPlay(gameState, player);
  if (!dummyValidation.isValid) {
    throw new Error(dummyValidation.error || 'Invalid dummy play');
  }

  // Validate it's the correct player's turn
  if (player !== gameState.currentPlayer) {
    throw new Error(`It's not ${player}'s turn to play`);
  }

  // Get player's hand
  const playerHand = gameState.players[player].hand;

  // Validate card play using comprehensive Bridge rules
  const validation = validateCardPlay(card, playerHand, gameState.currentTrick, gameState.contract);
  if (!validation.isValid) {
    throw new Error(validation.error || 'Invalid card play');
  }

  // Find and remove card from player's hand
  const cardIndex = playerHand.findIndex(c => c && c.rank === card.rank && c.suit === card.suit);
  if (cardIndex < 0) {
    throw new Error('Card not found in player hand');
  }

  // Create deep clone of game state
  const newGameState = deepCloneGameState(gameState);

  // Remove card from player's hand (immutably)
  newGameState.players[player].hand = playerHand.filter((_, index) => index !== cardIndex);

  // Add card to current trick
  newGameState.currentTrick = [
    ...gameState.currentTrick,
    { card, player }
  ];

  // Expose dummy after opening lead (first card of first trick)
  if (gameState.tricks.length === 0 && gameState.currentTrick.length === 1) {
    // Opening lead has been made - dummy is now exposed
    // This is handled by the UI components checking gameState.dummy
  }

  // Check if trick is complete (4 cards played)
  if (newGameState.currentTrick.length === 4) {
    // Don't auto-complete trick - let UI handle the winner display first
    // The trick will be completed by the UI after showing the winner
    return newGameState;
  } else {
    // Move to next player (with dummy play logic)
    newGameState.currentPlayer = getNextPlayerWithDummyLogic(gameState, gameState.contract);
    return newGameState;
  }
}

/**
 * Validates dummy play rules
 */
function validateDummyPlay(gameState: GameState, player: Position): { isValid: boolean; error?: string } {
  if (!gameState.contract || !gameState.dummy) {
    return { isValid: true }; // No dummy restrictions if no contract/dummy
  }

  const isDummyPosition = player === gameState.dummy;
  const isDeclarerPosition = player === gameState.contract.declarer;

  // Dummy cannot play independently - only declarer can play from dummy
  if (isDummyPosition && gameState.currentPlayer === gameState.dummy) {
    // This should be handled by the UI - declarer should be making the play
    // But we allow it programmatically as the declarer is controlling dummy
    return { isValid: true };
  }

  // Declarer can play from their own hand or dummy's hand
  if (isDeclarerPosition) {
    return { isValid: true };
  }

  // Other players can only play from their own hands
  if (!isDummyPosition && !isDeclarerPosition) {
    return { isValid: true };
  }

  return { isValid: true };
}

/**
 * Gets the next player with dummy play logic
 */
function getNextPlayerWithDummyLogic(gameState: GameState, contract: Contract | null): Position {
  const currentPlayer = gameState.currentPlayer;
  const nextPlayer = getNextPlayer(currentPlayer);

  // In Bridge, play always proceeds clockwise regardless of dummy
  // The declarer will control dummy when it's dummy's turn
  return nextPlayer;
}

/**
 * Checks if dummy should be visible (after opening lead)
 */
export function isDummyVisible(gameState: GameState): boolean {
  if (!gameState.dummy || gameState.phase !== GamePhase.PLAYING) {
    return false;
  }

  // Dummy is visible after the opening lead (first card of first trick)
  return gameState.tricks.length > 0 || gameState.currentTrick.length > 0;
}

/**
 * Checks if the current player should control dummy
 */
export function shouldDeclarerControlDummy(gameState: GameState): boolean {
  if (!gameState.contract || !gameState.dummy) {
    return false;
  }

  // Declarer controls dummy when it's dummy's turn to play
  return gameState.currentPlayer === gameState.dummy;
}

/**
 * Gets the effective player for card selection (declarer when controlling dummy)
 */
export function getEffectivePlayer(gameState: GameState): Position {
  if (shouldDeclarerControlDummy(gameState)) {
    return gameState.contract!.declarer;
  }
  return gameState.currentPlayer;
}

/**
 * Completes a trick when 4 cards have been played
 */
export function completeTrick(gameState: GameState): GameState {
  if (gameState.currentTrick.length !== 4) {
    throw new Error('Cannot complete trick with less than 4 cards');
  }

  if (!gameState.contract) {
    throw new Error('Cannot complete trick without a contract');
  }

  // Determine trick winner
  const trickWinner = getTrickWinner(gameState.currentTrick, gameState.contract);

  // Create the completed trick
  const completedTrick: Trick = {
    id: gameState.tricks.length + 1,
    cards: [...gameState.currentTrick],
    winner: trickWinner,
    leadPlayer: gameState.currentTrick[0].player
  };

  // Create new game state
  const newGameState = deepCloneGameState(gameState);

  // Add completed trick to tricks array
  newGameState.tricks = [...gameState.tricks, completedTrick];

  // Clear current trick
  newGameState.currentTrick = [];

  // Winner of trick leads next trick
  newGameState.currentPlayer = trickWinner;

  // Check if all 13 tricks have been played
  if (newGameState.tricks.length === 13) {
    // Hand is complete - move to scoring phase
    newGameState.phase = GamePhase.SCORING;
  }

  return newGameState;
}

/**
 * Processes a bid with comprehensive Bridge bidding validation
 */
export function processBid(gameState: GameState, bid: Bid): GameState {
  // Validate game is in bidding phase
  if (gameState.phase !== GamePhase.BIDDING) {
    throw new Error('Cannot bid outside of bidding phase');
  }

  // Validate it's the correct player's turn
  if (bid.player !== gameState.currentPlayer) {
    throw new Error(`It's not ${bid.player}'s turn to bid`);
  }

  // Validate the bid using comprehensive Bridge rules
  if (!isValidBid(gameState.auction, bid)) {
    throw new Error('Invalid bid according to Bridge rules');
  }

  // Create deep clone of game state
  const newGameState = deepCloneGameState(gameState);

  // Add bid to auction
  newGameState.auction = [...gameState.auction, bid];

  // Check if auction is complete
  if (isAuctionComplete(newGameState.auction)) {
    // Get final contract
    const finalContract = getFinalContract(newGameState.auction);

    if (finalContract) {
      // Contract established - move to playing phase
      newGameState.contract = {
        level: finalContract.level,
        suit: finalContract.suit,
        declarer: finalContract.declarer,
        doubled: finalContract.doubled
      };
      newGameState.phase = GamePhase.PLAYING;

      // Set dummy (partner of declarer)
      const positions = [Position.NORTH, Position.EAST, Position.SOUTH, Position.WEST];
      const declarerIndex = positions.indexOf(finalContract.declarer);
      newGameState.dummy = positions[(declarerIndex + 2) % 4];

      // Set opening leader (left of declarer)
      newGameState.currentPlayer = positions[(declarerIndex + 1) % 4];
    } else {
      // All passed out - game ends
      newGameState.phase = GamePhase.FINISHED;
    }
  } else {
    // Continue bidding - move to next player
    newGameState.currentPlayer = getNextPlayer(gameState.currentPlayer);
  }

  return newGameState;
}

/**
 * Processes the scoring phase and transitions to complete
 */
export function processScoring(gameState: GameState): GameState {
  // Validate game is in scoring phase
  if (gameState.phase !== GamePhase.SCORING) {
    throw new Error('Cannot process scoring outside of scoring phase');
  }

  // Validate all tricks have been played
  if (gameState.tricks.length !== 13) {
    throw new Error('Cannot score incomplete hand');
  }

  // Validate contract exists
  if (!gameState.contract) {
    throw new Error('Cannot score without a contract');
  }

  // Create deep clone of game state
  const newGameState = deepCloneGameState(gameState);

  // Calculate contract result
  const contractResult = calculateContractResult(gameState.tricks, gameState.contract);

  // Determine vulnerability for declaring side
  const declaringPartnership = getDeclaringPartnership(gameState.contract);
  const isDeclaringVulnerable = declaringPartnership === 'NS' ?
    gameState.vulnerabilities.northSouth :
    gameState.vulnerabilities.eastWest;

  // Calculate score
  const score = calculateContractScore(
    gameState.contract,
    contractResult.tricksMade,
    isDeclaringVulnerable
  );

  // Apply score to the correct partnership
  if (declaringPartnership === 'NS') {
    newGameState.score.northSouth += Math.max(0, score);
    newGameState.score.eastWest += Math.max(0, -score);
  } else {
    newGameState.score.eastWest += Math.max(0, score);
    newGameState.score.northSouth += Math.max(0, -score);
  }

  // Transition to complete phase
  newGameState.phase = GamePhase.COMPLETE;

  return newGameState;
}

/**
 * Gets detailed scoring information for a completed hand
 */
export function getDetailedScoring(gameState: GameState): any {
  if (!gameState.contract || gameState.tricks.length !== 13) {
    return null;
  }

  const contractResult = calculateContractResult(gameState.tricks, gameState.contract);
  const declaringPartnership = getDeclaringPartnership(gameState.contract);
  const isDeclaringVulnerable = declaringPartnership === 'NS' ?
    gameState.vulnerabilities.northSouth :
    gameState.vulnerabilities.eastWest;

  return calculateDetailedScore(
    gameState.contract,
    contractResult.tricksMade,
    isDeclaringVulnerable
  );
}

/**
 * Gets the declaring partnership from a contract
 */
function getDeclaringPartnership(contract: Contract): 'NS' | 'EW' {
  if (contract.declarer === Position.NORTH || contract.declarer === Position.SOUTH) {
    return 'NS';
  } else {
    return 'EW';
  }
}

/**
 * Completes scoring for the game (legacy function)
 */
export function completeScoring(gameState: GameState): GameState {
  return processScoring(gameState);
}

/**
 * Checks if the game can transition to the next phase
 */
export function canTransitionPhase(gameState: GameState, targetPhase: GamePhase): boolean {
  switch (gameState.phase) {
    case GamePhase.WAITING_FOR_PLAYERS:
      return targetPhase === GamePhase.DEALING;

    case GamePhase.DEALING:
      return targetPhase === GamePhase.BIDDING;

    case GamePhase.BIDDING:
      return targetPhase === GamePhase.PLAYING || targetPhase === GamePhase.FINISHED;

    case GamePhase.PLAYING:
      return targetPhase === GamePhase.SCORING;

    case GamePhase.SCORING:
      return targetPhase === GamePhase.COMPLETE || targetPhase === GamePhase.BIDDING; // Next hand

    case GamePhase.FINISHED:
      return targetPhase === GamePhase.COMPLETE;

    case GamePhase.COMPLETE:
      return false; // Terminal phase

    default:
      return false;
  }
}

/**
 * Gets the next expected phase for a game state
 */
export function getNextPhase(gameState: GameState): GamePhase | null {
  switch (gameState.phase) {
    case GamePhase.WAITING_FOR_PLAYERS:
      return GamePhase.DEALING;

    case GamePhase.DEALING:
      return GamePhase.BIDDING;

    case GamePhase.BIDDING:
      // Depends on auction outcome
      return null; // Will be determined by auction completion

    case GamePhase.PLAYING:
      return GamePhase.SCORING;

    case GamePhase.SCORING:
      return GamePhase.COMPLETE;

    case GamePhase.FINISHED:
      return GamePhase.COMPLETE;

    case GamePhase.COMPLETE:
      return null; // Terminal phase

    default:
      return null;
  }
}

/**
 * Automatically transitions game phase if conditions are met
 */
export function autoTransitionPhase(gameState: GameState): GameState {
  let newGameState = gameState;

  switch (gameState.phase) {
    case GamePhase.SCORING:
      // Auto-transition to complete after scoring is processed
      if (gameState.tricks.length === 13) {
        newGameState = processScoring(gameState);
      }
      break;

    case GamePhase.FINISHED:
      // Auto-transition to complete for passed-out hands
      newGameState = deepCloneGameState(gameState);
      newGameState.phase = GamePhase.COMPLETE;
      break;

    // Other phases require explicit actions, no auto-transition
    default:
      break;
  }

  return newGameState;
}

/**
 * Validates that a game state is in a consistent phase
 */
export function validatePhaseConsistency(gameState: GameState): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  switch (gameState.phase) {
    case GamePhase.BIDDING:
      if (gameState.contract !== null) {
        errors.push('Contract should be null during bidding phase');
      }
      if (gameState.dummy !== null) {
        errors.push('Dummy should be null during bidding phase');
      }
      if (gameState.tricks.length > 0) {
        errors.push('No tricks should exist during bidding phase');
      }
      break;

    case GamePhase.PLAYING:
      if (gameState.contract === null) {
        errors.push('Contract should not be null during playing phase');
      }
      if (gameState.dummy === null) {
        errors.push('Dummy should not be null during playing phase');
      }
      if (gameState.tricks.length > 13) {
        errors.push('Too many tricks for playing phase');
      }
      break;

    case GamePhase.SCORING:
      if (gameState.contract === null) {
        errors.push('Contract should not be null during scoring phase');
      }
      if (gameState.tricks.length !== 13) {
        errors.push('All 13 tricks should be complete during scoring phase');
      }
      if (gameState.currentTrick.length > 0) {
        errors.push('Current trick should be empty during scoring phase');
      }
      break;

    case GamePhase.FINISHED:
      if (gameState.contract !== null) {
        errors.push('Contract should be null for passed-out hands');
      }
      break;

    case GamePhase.COMPLETE:
      // Complete phase can have various states depending on how game ended
      break;

    default:
      break;
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

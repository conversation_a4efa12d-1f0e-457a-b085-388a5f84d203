/**
 * Unit tests for GameSocket WebSocket client
 */

import GameSocket, { GameMessageType, GameSocketConfig } from './gameSocket';
import { Suit, Rank } from '../../types/bridge';

// Mock WebSocket
class MockWebSocket {
  public readyState: number = WebSocket.CONNECTING;
  public onopen: ((event: Event) => void) | null = null;
  public onmessage: ((event: MessageEvent) => void) | null = null;
  public onclose: ((event: CloseEvent) => void) | null = null;
  public onerror: ((event: Event) => void) | null = null;
  
  private sentMessages: string[] = [];
  private static shouldFailConnection: boolean = false;

  constructor(public url: string) {
    // Simulate connection immediately for tests
    process.nextTick(() => {
      if (MockWebSocket.shouldFailConnection) {
        this.readyState = WebSocket.CLOSED;
        this.onerror?.(new Event('error'));
      } else {
        this.readyState = WebSocket.OPEN;
        this.onopen?.(new Event('open'));
      }
    });
  }

  public send(data: string): void {
    if (this.readyState === WebSocket.OPEN) {
      this.sentMessages.push(data);
    }
  }

  public close(code?: number, reason?: string): void {
    this.readyState = WebSocket.CLOSED;
    this.onclose?.(new CloseEvent('close', { code, reason }));
  }

  public simulateMessage(data: any): void {
    const event = new MessageEvent('message', { 
      data: JSON.stringify(data) 
    });
    this.onmessage?.(event);
  }

  public simulateError(): void {
    this.onerror?.(new Event('error'));
  }

  public getSentMessages(): any[] {
    return this.sentMessages.map(msg => JSON.parse(msg));
  }

  public getLastSentMessage(): any {
    const messages = this.getSentMessages();
    return messages.length > 0 ? messages[messages.length - 1] : null;
  }

  public clearSentMessages(): void {
    this.sentMessages = [];
  }

  // Test helper to simulate connection failure
  public static setConnectionFailure(shouldFail: boolean): void {
    MockWebSocket.shouldFailConnection = shouldFail;
  }
}

// Mock global WebSocket
const originalWebSocket = (global as any).WebSocket;
(global as any).WebSocket = MockWebSocket;

describe('GameSocket', () => {
  let gameSocket: GameSocket;
  let mockConfig: GameSocketConfig;
  let mockCallbacks: any;

  beforeEach(() => {
    mockConfig = {
      url: 'ws://localhost:8080/ws',
      gameId: 'test-game-123',
      playerId: 'player-456',
      playerName: 'Test Player',
      reconnectAttempts: 3,
      reconnectDelay: 1000,
      heartbeatInterval: 30000
    };

    mockCallbacks = {
      onConnect: jest.fn(),
      onDisconnect: jest.fn(),
      onError: jest.fn(),
      onGameStateUpdate: jest.fn(),
      onPlayerJoined: jest.fn(),
      onPlayerLeft: jest.fn(),
      onBidMade: jest.fn(),
      onCardPlayed: jest.fn()
    };

    gameSocket = new GameSocket(mockConfig, mockCallbacks);
    MockWebSocket.setConnectionFailure(false);
  });

  afterEach(() => {
    gameSocket.disconnect();
    jest.clearAllMocks();
  });

  afterAll(() => {
    // Restore original WebSocket
    (global as any).WebSocket = originalWebSocket;
  });

  describe('Connection Management', () => {
    it('should connect successfully', async () => {
      await gameSocket.connect();
      
      expect(gameSocket.isConnected()).toBe(true);
      expect(mockCallbacks.onConnect).toHaveBeenCalled();
    });

    it('should handle connection failure', async () => {
      // Mock connection failure
      MockWebSocket.setConnectionFailure(true);
      
      await expect(gameSocket.connect()).rejects.toThrow();
    });

    it('should disconnect cleanly', async () => {
      await gameSocket.connect();
      gameSocket.disconnect();
      
      expect(gameSocket.isConnected()).toBe(false);
    });

    it('should not connect if already connecting', async () => {
      const connectPromise1 = gameSocket.connect();
      const connectPromise2 = gameSocket.connect();
      
      await Promise.all([connectPromise1, connectPromise2]);
      
      // Should only connect once
      expect(mockCallbacks.onConnect).toHaveBeenCalledTimes(1);
    });
  });

  describe('Message Handling', () => {
    beforeEach(async () => {
      await gameSocket.connect();
    });

    it('should handle game state update messages', () => {
      const mockSocket = (gameSocket as any).socket as MockWebSocket;
      mockSocket.simulateMessage({
        type: GameMessageType.GAME_STATE_UPDATE,
        gameId: 'test-game-123',
        playerId: 'server',
        timestamp: Date.now(),
        data: { phase: 'bidding', currentPlayer: 'north' }
      });

      expect(mockCallbacks.onGameStateUpdate).toHaveBeenCalled();
    });

    it('should handle player joined messages', () => {
      const mockSocket = (gameSocket as any).socket as MockWebSocket;
      mockSocket.simulateMessage({
        type: GameMessageType.PLAYER_JOINED,
        gameId: 'test-game-123',
        playerId: 'server',
        timestamp: Date.now(),
        data: { 
          playerId: 'new-player',
          playerName: 'New Player',
          position: 'south'
        }
      });

      expect(mockCallbacks.onPlayerJoined).toHaveBeenCalledWith({
        playerId: 'new-player',
        playerName: 'New Player',
        position: 'south'
      });
    });

    it('should handle bid made messages', () => {
      const mockBid = {
        player: 'north',
        level: 1,
        suit: Suit.HEARTS,
        isPass: false,
        isDouble: false,
        isRedouble: false
      };

      const mockSocket = (gameSocket as any).socket as MockWebSocket;
      mockSocket.simulateMessage({
        type: GameMessageType.BID_MADE,
        gameId: 'test-game-123',
        playerId: 'server',
        timestamp: Date.now(),
        data: { bid: mockBid }
      });

      expect(mockCallbacks.onBidMade).toHaveBeenCalledWith(mockBid);
    });

    it('should handle card played messages', () => {
      const mockCard = { suit: Suit.SPADES, rank: Rank.ACE };

      const mockSocket = (gameSocket as any).socket as MockWebSocket;
      mockSocket.simulateMessage({
        type: GameMessageType.CARD_PLAYED,
        gameId: 'test-game-123',
        playerId: 'server',
        timestamp: Date.now(),
        data: { 
          card: mockCard,
          player: 'north'
        }
      });

      expect(mockCallbacks.onCardPlayed).toHaveBeenCalledWith({
        card: mockCard,
        player: 'north'
      });
    });

    it('should handle error messages', () => {
      const mockSocket = (gameSocket as any).socket as MockWebSocket;
      mockSocket.simulateMessage({
        type: GameMessageType.ERROR,
        gameId: 'test-game-123',
        playerId: 'server',
        timestamp: Date.now(),
        data: { 
          message: 'Invalid move',
          code: 'INVALID_MOVE'
        }
      });

      expect(mockCallbacks.onError).toHaveBeenCalledWith(
        expect.objectContaining({ message: 'Invalid move' })
      );
    });

    it('should respond to heartbeat messages', () => {
      const mockSocket = (gameSocket as any).socket as MockWebSocket;
      mockSocket.simulateMessage({
        type: GameMessageType.HEARTBEAT,
        gameId: 'test-game-123',
        playerId: 'server',
        timestamp: Date.now(),
        data: {}
      });

      const sentMessages = mockSocket.getSentMessages();
      const heartbeatResponse = sentMessages.find(msg => msg.type === GameMessageType.HEARTBEAT);
      expect(heartbeatResponse).toBeDefined();
    });
  });

  describe('Sending Messages', () => {
    beforeEach(async () => {
      await gameSocket.connect();
    });

    it('should send bid messages', () => {
      const mockBid = {
        player: 'north',
        level: 1,
        suit: Suit.HEARTS,
        isPass: false,
        isDouble: false,
        isRedouble: false
      };

      gameSocket.sendBid(mockBid);

      const mockSocket = (gameSocket as any).socket as MockWebSocket;
      const sentMessages = mockSocket.getSentMessages();
      const bidMessage = sentMessages.find(msg => msg.type === GameMessageType.BID_MADE);
      
      expect(bidMessage).toBeDefined();
      expect(bidMessage.data.bid).toEqual(mockBid);
    });

    it('should send card play messages', () => {
      const mockCard = { suit: Suit.SPADES, rank: Rank.ACE };

      gameSocket.sendCardPlay(mockCard);

      const mockSocket = (gameSocket as any).socket as MockWebSocket;
      const sentMessages = mockSocket.getSentMessages();
      const cardMessage = sentMessages.find(msg => msg.type === GameMessageType.CARD_PLAYED);
      
      expect(cardMessage).toBeDefined();
      expect(cardMessage.data.card).toEqual(mockCard);
    });

    it('should request game state', () => {
      gameSocket.requestGameState();

      const mockSocket = (gameSocket as any).socket as MockWebSocket;
      const sentMessages = mockSocket.getSentMessages();
      const stateRequest = sentMessages.find(msg => msg.type === GameMessageType.GAME_STATE_UPDATE);
      
      expect(stateRequest).toBeDefined();
    });

    it('should queue messages when disconnected', () => {
      gameSocket.disconnect();
      
      const mockBid = {
        player: 'north',
        level: 1,
        suit: Suit.HEARTS,
        isPass: false,
        isDouble: false,
        isRedouble: false
      };

      // This should not throw since the message gets queued
      expect(() => gameSocket.sendBid(mockBid)).not.toThrow();
    });
  });

  describe('Error Handling', () => {
    beforeEach(async () => {
      await gameSocket.connect();
    });

    it('should handle malformed messages gracefully', () => {
      const mockSocket = (gameSocket as any).socket as MockWebSocket;
      const event = new MessageEvent('message', { data: 'invalid json' });
      mockSocket.onmessage?.(event);

      expect(mockCallbacks.onError).toHaveBeenCalledWith(
        expect.objectContaining({ message: 'Invalid message format' })
      );
    });

    it('should handle WebSocket errors', () => {
      const mockSocket = (gameSocket as any).socket as MockWebSocket;
      mockSocket.simulateError();

      expect(mockCallbacks.onError).toHaveBeenCalled();
    });
  });
});

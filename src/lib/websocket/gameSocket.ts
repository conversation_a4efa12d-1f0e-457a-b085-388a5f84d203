/**
 * WebSocket client for real-time multiplayer Bridge game communication
 * Handles connection management, message routing, and game state synchronization
 */

import { GameState, Position, Card, Bid, GamePhase } from '../../types/bridge';

export interface GameMessage {
  type: GameMessageType;
  gameId: string;
  playerId: string;
  timestamp: number;
  data: any;
}

export enum GameMessageType {
  // Connection management
  PLAYER_JOINED = 'player_joined',
  PLAYER_LEFT = 'player_left',
  GAME_STARTED = 'game_started',
  GAME_ENDED = 'game_ended',
  
  // Game actions
  BID_MADE = 'bid_made',
  CARD_PLAYED = 'card_played',
  TRICK_COMPLETED = 'trick_completed',
  HAND_COMPLETED = 'hand_completed',
  
  // State synchronization
  GAME_STATE_UPDATE = 'game_state_update',
  PLAYER_STATE_UPDATE = 'player_state_update',
  
  // System messages
  ERROR = 'error',
  HEARTBEAT = 'heartbeat',
  RECONNECT = 'reconnect'
}

export interface GameSocketConfig {
  url: string;
  gameId: string;
  playerId: string;
  playerName: string;
  reconnectAttempts?: number;
  reconnectDelay?: number;
  heartbeatInterval?: number;
}

export interface GameSocketCallbacks {
  onConnect?: () => void;
  onDisconnect?: (reason: string) => void;
  onError?: (error: Error) => void;
  onGameStateUpdate?: (gameState: GameState) => void;
  onPlayerJoined?: (playerId: string, playerName: string, position: Position) => void;
  onPlayerLeft?: (playerId: string, position: Position) => void;
  onBidMade?: (position: Position, bid: Bid) => void;
  onCardPlayed?: (position: Position, card: Card) => void;
  onTrickCompleted?: (winner: Position, trick: Card[]) => void;
  onGameStarted?: () => void;
  onGameEnded?: (result: any) => void;
  onMessage?: (message: GameMessage) => void;
}

export class GameSocket {
  private socket: WebSocket | null = null;
  private config: GameSocketConfig;
  private callbacks: GameSocketCallbacks;
  private reconnectAttempts = 0;
  private heartbeatTimer: NodeJS.Timeout | null = null;
  private isConnecting = false;
  private messageQueue: GameMessage[] = [];

  constructor(config: GameSocketConfig, callbacks: GameSocketCallbacks = {}) {
    this.config = {
      reconnectAttempts: 5,
      reconnectDelay: 1000,
      heartbeatInterval: 30000,
      ...config
    };
    this.callbacks = callbacks;
  }

  /**
   * Connect to the WebSocket server
   */
  public async connect(): Promise<void> {
    if (this.isConnecting || this.isConnected()) {
      return;
    }

    this.isConnecting = true;

    try {
      const wsUrl = `${this.config.url}?gameId=${this.config.gameId}&playerId=${this.config.playerId}&playerName=${encodeURIComponent(this.config.playerName)}`;
      
      this.socket = new WebSocket(wsUrl);
      
      this.socket.onopen = this.handleOpen.bind(this);
      this.socket.onmessage = this.handleMessage.bind(this);
      this.socket.onclose = this.handleClose.bind(this);
      this.socket.onerror = this.handleError.bind(this);

      // Wait for connection to open
      await new Promise<void>((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('Connection timeout'));
        }, 10000);

        this.socket!.onopen = () => {
          clearTimeout(timeout);
          this.handleOpen();
          resolve();
        };

        this.socket!.onerror = (error) => {
          clearTimeout(timeout);
          reject(new Error('Connection failed'));
        };
      });

    } catch (error) {
      this.isConnecting = false;
      throw error;
    }
  }

  /**
   * Disconnect from the WebSocket server
   */
  public disconnect(): void {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
      this.heartbeatTimer = null;
    }

    if (this.socket) {
      this.socket.close(1000, 'Client disconnect');
      this.socket = null;
    }

    this.isConnecting = false;
    this.reconnectAttempts = 0;
  }

  /**
   * Check if socket is connected
   */
  public isConnected(): boolean {
    return this.socket?.readyState === WebSocket.OPEN;
  }

  /**
   * Send a message to the server
   */
  public sendMessage(type: GameMessageType, data: any): void {
    const message: GameMessage = {
      type,
      gameId: this.config.gameId,
      playerId: this.config.playerId,
      timestamp: Date.now(),
      data
    };

    if (this.isConnected()) {
      this.socket!.send(JSON.stringify(message));
    } else {
      // Queue message for when connection is restored
      this.messageQueue.push(message);
    }
  }

  /**
   * Send a bid
   */
  public sendBid(bid: Bid): void {
    this.sendMessage(GameMessageType.BID_MADE, { bid });
  }

  /**
   * Send a card play
   */
  public sendCardPlay(card: Card): void {
    this.sendMessage(GameMessageType.CARD_PLAYED, { card });
  }

  /**
   * Request current game state
   */
  public requestGameState(): void {
    this.sendMessage(GameMessageType.GAME_STATE_UPDATE, { request: true });
  }

  /**
   * Handle WebSocket open event
   */
  private handleOpen(): void {
    this.isConnecting = false;
    this.reconnectAttempts = 0;

    // Send queued messages
    while (this.messageQueue.length > 0) {
      const message = this.messageQueue.shift()!;
      this.socket!.send(JSON.stringify(message));
    }

    // Start heartbeat
    this.startHeartbeat();

    this.callbacks.onConnect?.();
  }

  /**
   * Handle WebSocket message event
   */
  private handleMessage(event: MessageEvent): void {
    try {
      const message: GameMessage = JSON.parse(event.data);
      
      // Handle specific message types
      switch (message.type) {
        case GameMessageType.GAME_STATE_UPDATE:
          this.callbacks.onGameStateUpdate?.(message.data.gameState);
          break;
          
        case GameMessageType.PLAYER_JOINED:
          this.callbacks.onPlayerJoined?.(
            message.data.playerId,
            message.data.playerName,
            message.data.position
          );
          break;
          
        case GameMessageType.PLAYER_LEFT:
          this.callbacks.onPlayerLeft?.(
            message.data.playerId,
            message.data.position
          );
          break;
          
        case GameMessageType.BID_MADE:
          this.callbacks.onBidMade?.(
            message.data.position,
            message.data.bid
          );
          break;
          
        case GameMessageType.CARD_PLAYED:
          this.callbacks.onCardPlayed?.(
            message.data.position,
            message.data.card
          );
          break;
          
        case GameMessageType.TRICK_COMPLETED:
          this.callbacks.onTrickCompleted?.(
            message.data.winner,
            message.data.trick
          );
          break;
          
        case GameMessageType.GAME_STARTED:
          this.callbacks.onGameStarted?.();
          break;
          
        case GameMessageType.GAME_ENDED:
          this.callbacks.onGameEnded?.(message.data.result);
          break;
          
        case GameMessageType.ERROR:
          this.callbacks.onError?.(new Error(message.data.error));
          break;
          
        case GameMessageType.HEARTBEAT:
          // Respond to heartbeat
          this.sendMessage(GameMessageType.HEARTBEAT, { response: true });
          break;
      }

      // Call general message callback
      this.callbacks.onMessage?.(message);

    } catch (error) {
      console.error('Failed to parse WebSocket message:', error);
      this.callbacks.onError?.(new Error('Invalid message format'));
    }
  }

  /**
   * Handle WebSocket close event
   */
  private handleClose(event: CloseEvent): void {
    this.socket = null;
    this.isConnecting = false;

    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
      this.heartbeatTimer = null;
    }

    // Attempt reconnection if not a clean close
    if (event.code !== 1000 && this.reconnectAttempts < this.config.reconnectAttempts!) {
      this.attemptReconnect();
    }

    this.callbacks.onDisconnect?.(event.reason || 'Connection closed');
  }

  /**
   * Handle WebSocket error event
   */
  private handleError(event: Event): void {
    console.error('WebSocket error:', event);
    this.callbacks.onError?.(new Error('WebSocket connection error'));
  }

  /**
   * Attempt to reconnect to the server
   */
  private async attemptReconnect(): Promise<void> {
    this.reconnectAttempts++;
    
    const delay = this.config.reconnectDelay! * Math.pow(2, this.reconnectAttempts - 1);
    
    setTimeout(async () => {
      try {
        await this.connect();
        this.sendMessage(GameMessageType.RECONNECT, { 
          previousAttempts: this.reconnectAttempts 
        });
      } catch (error) {
        console.error(`Reconnection attempt ${this.reconnectAttempts} failed:`, error);
        
        if (this.reconnectAttempts >= this.config.reconnectAttempts!) {
          this.callbacks.onError?.(new Error('Max reconnection attempts reached'));
        }
      }
    }, delay);
  }

  /**
   * Start heartbeat to keep connection alive
   */
  private startHeartbeat(): void {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
    }

    this.heartbeatTimer = setInterval(() => {
      if (this.isConnected()) {
        this.sendMessage(GameMessageType.HEARTBEAT, { ping: true });
      }
    }, this.config.heartbeatInterval!);
  }
}

export default GameSocket;

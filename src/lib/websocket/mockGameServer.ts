/**
 * Mock WebSocket server for development and testing
 * Simulates a real Bridge game server with AI players
 */

import { GameState, Position, Card, Bid, GamePhase, Player, PlayerType } from '../../types/bridge';
import { GameMessage, GameMessageType } from './gameSocket';

export interface MockServerConfig {
  port?: number;
  aiDelay?: number;
  enableLogging?: boolean;
}

export class MockGameServer {
  private games: Map<string, MockGame> = new Map();
  private connections: Map<string, MockConnection> = new Map();
  private config: MockServerConfig;

  constructor(config: MockServerConfig = {}) {
    this.config = {
      port: 8080,
      aiDelay: 1000,
      enableLogging: true,
      ...config
    };
  }

  /**
   * Handle new WebSocket connection
   */
  public handleConnection(ws: any, gameId: string, playerId: string, playerName: string): void {
    const connectionId = `${gameId}-${playerId}`;
    
    const connection = new MockConnection(ws, id: gameId, playerId, playerName);
    this.connections.set(connectionId, connection);

    // Get or create game
    let game = this.games.get(gameId);
    if (!game) {
      game = new MockGame(id: gameId, this.config);
      this.games.set(id: gameId, game);
    }

    // Add player to game
    game.addPlayer(connection);

    // Set up message handling
    ws.on('message', (data: string) => {
      try {
        const message: GameMessage = JSON.parse(data);
        this.handleMessage(connection, message);
      } catch (error) {
        this.log(`Error parsing message from ${playerId}:`, error);
        connection.sendError('Invalid message format');
      }
    });

    ws.on('close', () => {
      this.handleDisconnection(connectionId);
    });

    this.log(`Player ${playerName} (${playerId}) connected to game ${gameId}`);
  }

  /**
   * Handle incoming message from client
   */
  private handleMessage(connection: MockConnection, message: GameMessage): void {
    const game = this.games.get(message.gameId);
    if (!game) {
      connection.sendError('Game not found');
      return;
    }

    switch (message.type) {
      case GameMessageType.BID_MADE:
        game.handleBid(connection, message.data.bid);
        break;
        
      case GameMessageType.CARD_PLAYED:
        game.handleCardPlay(connection, message.data.card);
        break;
        
      case GameMessageType.GAME_STATE_UPDATE:
        if (message.data.request) {
          game.sendGameState(connection);
        }
        break;
        
      case GameMessageType.HEARTBEAT:
        connection.send({
          type: GameMessageType.HEARTBEAT,
          gameId: message.id: gameId,
          playerId: 'server',
          timestamp: Date.now(),
          data: { pong: true }
        });
        break;
        
      default:
        this.log(`Unhandled message type: ${message.type}`);
    }
  }

  /**
   * Handle client disconnection
   */
  private handleDisconnection(connectionId: string): void {
    const connection = this.connections.get(connectionId);
    if (!connection) return;

    const game = this.games.get(connection.gameId);
    if (game) {
      game.removePlayer(connection);
      
      // Clean up empty games
      if (game.isEmpty()) {
        this.games.delete(connection.gameId);
      }
    }

    this.connections.delete(connectionId);
    this.log(`Player ${connection.playerName} disconnected`);
  }

  private log(...args: any[]): void {
    if (this.config.enableLogging) {
      console.log('[MockGameServer]', ...args);
    }
  }
}

class MockConnection {
  public ws: any;
  public gameId: string;
  public playerId: string;
  public playerName: string;
  public position?: Position;

  constructor(ws: any, gameId: string, playerId: string, playerName: string) {
    this.ws = ws;
    this.gameId = gameId;
    this.playerId = playerId;
    this.playerName = playerName;
  }

  public send(message: GameMessage): void {
    if (this.ws.readyState === 1) { // WebSocket.OPEN
      this.ws.send(JSON.stringify(message));
    }
  }

  public sendError(error: string): void {
    this.send({
      type: GameMessageType.ERROR,
      gameId: this.id,
      playerId: 'server',
      timestamp: Date.now(),
      data: { error }
    });
  }
}

class MockGame {
  private gameId: string;
  private players: Map<Position, MockConnection> = new Map();
  private gameState: GameState;
  private config: MockServerConfig;
  private aiTimers: Map<Position, NodeJS.Timeout> = new Map();

  constructor(gameId: string, config: MockServerConfig) {
    this.gameId = gameId;
    this.config = config;
    
    // Initialize basic game state
    this.gameState = {
      id: gameId,
      phase: GamePhase.WAITING_FOR_PLAYERS,
      players: {
        [Position.NORTH]: { id: '', name: '', position: Position.NORTH, type: PlayerType.AI, hand: [], isConnected: false },
        [Position.SOUTH]: { id: '', name: '', position: Position.SOUTH, type: PlayerType.AI, hand: [], isConnected: false },
        [Position.EAST]: { id: '', name: '', position: Position.EAST, type: PlayerType.AI, hand: [], isConnected: false },
        [Position.WEST]: { id: '', name: '', position: Position.WEST, type: PlayerType.AI, hand: [], isConnected: false }
      },
      hands: {
        [Position.NORTH]: [],
        [Position.SOUTH]: [],
        [Position.EAST]: [],
        [Position.WEST]: []
      },
      currentPlayer: Position.NORTH,
      dealer: Position.NORTH,
      vulnerabilities: { northSouth: false, eastWest: false },
      auction: [],
      contract: null,
      tricks: [],
      currentTrick: [],
  dummy: null,
      score: { northSouth: 0, eastWest: 0 },
  rubberScore: { northSouth: 0, eastWest: 0 },
      gameNumber: 1
    };
  }

  public addPlayer(connection: MockConnection): void {
    // Find available position
    const availablePosition = this.findAvailablePosition();
    if (!availablePosition) {
      connection.sendError('Game is full');
      return;
    }

    connection.position = availablePosition;
    this.players.set(availablePosition, connection);
    
    // Update game state
    this.gameState.players[availablePosition] = {
      id: connection.playerId,
      name: connection.playerName,
      position: availablePosition,
      type: PlayerType.HUMAN,
      hand: [],
      isConnected: true
    };

    // Notify all players
    this.broadcastMessage({
      type: GameMessageType.PLAYER_JOINED,
      gameId: this.id,
      playerId: 'server',
      timestamp: Date.now(),
      data: {
        playerId: connection.playerId,
        playerName: connection.playerName,
        position: availablePosition
      }
    });

    // Fill remaining positions with AI
    this.fillWithAI();

    // Start game if all positions filled
    if (this.players.size === 4) {
      this.startGame();
    }

    // Send current game state to new player
    this.sendGameState(connection);
  }

  public removePlayer(connection: MockConnection): void {
    if (connection.position) {
      this.players.delete(connection.position);
      
      // Update game state
      this.gameState.players[connection.position].isConnected = false;

      // Notify remaining players
      this.broadcastMessage({
        type: GameMessageType.PLAYER_LEFT,
        gameId: this.id,
        playerId: 'server',
        timestamp: Date.now(),
        data: {
          playerId: connection.playerId,
          position: connection.position
        }
      });
    }
  }

  public handleBid(connection: MockConnection, bid: Bid): void {
    // Validate bid and update game state
    // This is a simplified implementation
    this.gameState.auction.push(bid);
    
    this.broadcastMessage({
      type: GameMessageType.BID_MADE,
      gameId: this.id,
      playerId: 'server',
      timestamp: Date.now(),
      data: {
        position: connection.position,
          type: PlayerType.AI,
          hand: [],
        bid
      }
    });

    // Move to next player or start play phase
    this.advanceToNextPlayer();
  }

  public handleCardPlay(connection: MockConnection, card: Card): void {
    // Validate card play and update game state
    // This is a simplified implementation
    const playedCard = { card, player: connection.position! };
    this.gameState.currentTrick.push(playedCard);
    
    this.broadcastMessage({
      type: GameMessageType.CARD_PLAYED,
      gameId: this.id,
      playerId: 'server',
      timestamp: Date.now(),
      data: {
        position: connection.position,
          type: PlayerType.AI,
          hand: [],
        card
      }
    });

    // Check if trick is complete
    if (this.gameState.currentTrick.length === 4) {
      this.completeTrick();
    } else {
      this.advanceToNextPlayer();
    }
  }

  public sendGameState(connection: MockConnection): void {
    connection.send({
      type: GameMessageType.GAME_STATE_UPDATE,
      gameId: this.id,
      playerId: 'server',
      timestamp: Date.now(),
      data: { gameState: this.gameState }
    });
  }

  public isEmpty(): boolean {
    return this.players.size === 0;
  }

  private findAvailablePosition(): Position | null {
    const positions = [Position.NORTH, Position.SOUTH, Position.EAST, Position.WEST];
    return positions.find(pos => !this.players.has(pos)) || null;
  }

  private fillWithAI(): void {
    const positions = [Position.NORTH, Position.SOUTH, Position.EAST, Position.WEST];
    
    positions.forEach(position => {
      if (!this.players.has(position)) {
        // Create AI player
        this.gameState.players[position] = {
          id: `ai-${position}`,
          name: `AI ${position}`,
          position,
          type: PlayerType.AI,
          hand: [],
          isConnected: true
        };
      }
    });
  }

  private startGame(): void {
    this.gameState.phase = GamePhase.BIDDING;
    
    this.broadcastMessage({
      type: GameMessageType.GAME_STARTED,
      gameId: this.id,
      playerId: 'server',
      timestamp: Date.now(),
      data: {}
    });

    // Start AI actions if needed
    this.scheduleAIAction();
  }

  private advanceToNextPlayer(): void {
    const positions = [Position.NORTH, Position.EAST, Position.SOUTH, Position.WEST];
    const currentIndex = positions.indexOf(this.gameState.currentPlayer);
    const nextIndex = (currentIndex + 1) % 4;
    this.gameState.currentPlayer = positions[nextIndex];

    this.scheduleAIAction();
  }

  private completeTrick(): void {
    // Simplified trick completion logic
    const winner = Position.NORTH; // Placeholder
    
    const trick = {
      id: this.gameState.tricks.length + 1,
      cards: [...this.gameState.currentTrick],
      winner: Position.NORTH,
      leadPlayer: Position.NORTH
    };
    this.gameState.tricks.push(trick);
    this.gameState.currentTrick = [];
    this.gameState.currentPlayer = winner;

    this.broadcastMessage({
      type: GameMessageType.TRICK_COMPLETED,
      gameId: this.id,
      playerId: 'server',
      timestamp: Date.now(),
      data: {
        winner,
        trick: this.gameState.tricks[this.gameState.tricks.length - 1]
      }
    });
  }

  private scheduleAIAction(): void {
    const currentPlayer = this.gameState.currentPlayer;
    const connection = this.players.get(currentPlayer);
    
    // If current player is AI, schedule an action
    if (!connection && this.gameState.players[currentPlayer].id.startsWith('ai-')) {
      const timer = setTimeout(() => {
        this.performAIAction(currentPlayer);
      }, this.config.aiDelay);
      
      this.aiTimers.set(currentPlayer, timer);
    }
  }

  private performAIAction(position: Position): void {
    // Simplified AI action - just pass or play a random card
    if (this.gameState.phase === GamePhase.BIDDING) {
      const passBid: Bid = { player: Position.NORTH, value: { type: "pass" }, timestamp: new Date() };
      this.gameState.auction.push(passBid);
      
      this.broadcastMessage({
        type: GameMessageType.BID_MADE,
        gameId: this.id,
        playerId: 'server',
        timestamp: Date.now(),
        data: { position, bid: passBid }
      });
    }

    this.advanceToNextPlayer();
  }

  private broadcastMessage(message: GameMessage): void {
    this.players.forEach(connection => {
      connection.send(message);
    });
  }
}

export default MockGameServer;

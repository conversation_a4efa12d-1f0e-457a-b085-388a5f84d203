/**
 * Responsive design system for Bridge game
 * Provides consistent breakpoints, spacing, and scaling across all components
 */

/* CSS Custom Properties for Responsive Design */
:root {
  /* Breakpoints */
  --breakpoint-xs: 480px;
  --breakpoint-sm: 768px;
  --breakpoint-md: 1024px;
  --breakpoint-lg: 1280px;
  --breakpoint-xl: 1440px;
  --breakpoint-xxl: 1920px;

  /* Base spacing scale */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  --spacing-xxl: 48px;

  /* Typography scale */
  --font-size-xs: 12px;
  --font-size-sm: 14px;
  --font-size-md: 16px;
  --font-size-lg: 18px;
  --font-size-xl: 20px;
  --font-size-xxl: 24px;
  --font-size-xxxl: 32px;

  /* Card dimensions */
  --card-width-xs: 45px;
  --card-height-xs: 63px;
  --card-width-sm: 60px;
  --card-height-sm: 84px;
  --card-width-md: 75px;
  --card-height-md: 105px;
  --card-width-lg: 90px;
  --card-height-lg: 126px;

  /* Game table dimensions */
  --table-min-width: 800px;
  --table-min-height: 600px;
  --table-max-width: 1400px;
  --table-max-height: 1000px;

  /* Component dimensions */
  --sidebar-width-sm: 280px;
  --sidebar-width-md: 320px;
  --sidebar-width-lg: 360px;
  
  /* Z-index scale */
  --z-background: 0;
  --z-table: 10;
  --z-cards: 20;
  --z-ui: 100;
  --z-overlay: 200;
  --z-modal: 300;
  --z-tooltip: 400;
}

/* Base responsive container */
.responsive-container {
  width: 100%;
  max-width: var(--breakpoint-xxl);
  margin: 0 auto;
  padding: 0 var(--spacing-md);
}

/* Responsive grid system */
.responsive-grid {
  display: grid;
  gap: var(--spacing-md);
  width: 100%;
}

.responsive-flex {
  display: flex;
  gap: var(--spacing-md);
  flex-wrap: wrap;
}

/* Screen size specific styles */

/* Extra Small Screens (Mobile) */
@media (max-width: 479px) {
  :root {
    --spacing-md: 12px;
    --spacing-lg: 16px;
    --spacing-xl: 20px;
    --font-size-md: 14px;
    --font-size-lg: 16px;
    --sidebar-width-sm: 240px;
  }

  .responsive-container {
    padding: 0 var(--spacing-sm);
  }

  .hide-on-mobile {
    display: none !important;
  }

  .mobile-only {
    display: block !important;
  }
}

/* Small Screens (Tablets) */
@media (min-width: 480px) and (max-width: 767px) {
  :root {
    --spacing-md: 14px;
    --spacing-lg: 20px;
    --spacing-xl: 28px;
  }

  .hide-on-tablet {
    display: none !important;
  }

  .tablet-only {
    display: block !important;
  }
}

/* Medium Screens (Small Desktop) */
@media (min-width: 768px) and (max-width: 1023px) {
  :root {
    --table-min-width: 900px;
    --table-min-height: 650px;
  }

  .hide-on-small-desktop {
    display: none !important;
  }

  .small-desktop-only {
    display: block !important;
  }
}

/* Large Screens (Desktop) */
@media (min-width: 1024px) and (max-width: 1279px) {
  :root {
    --table-min-width: 1000px;
    --table-min-height: 700px;
    --sidebar-width-md: 340px;
  }

  .hide-on-desktop {
    display: none !important;
  }

  .desktop-only {
    display: block !important;
  }
}

/* Extra Large Screens (Large Desktop) */
@media (min-width: 1280px) and (max-width: 1439px) {
  :root {
    --table-min-width: 1100px;
    --table-min-height: 750px;
    --sidebar-width-lg: 380px;
  }

  .hide-on-large-desktop {
    display: none !important;
  }

  .large-desktop-only {
    display: block !important;
  }
}

/* XXL Screens (Ultra-wide) */
@media (min-width: 1440px) {
  :root {
    --table-min-width: 1200px;
    --table-min-height: 800px;
    --sidebar-width-lg: 400px;
    --spacing-xl: 40px;
    --spacing-xxl: 56px;
  }

  .hide-on-ultrawide {
    display: none !important;
  }

  .ultrawide-only {
    display: block !important;
  }
}

/* High DPI / Retina displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .card-image,
  .suit-symbol,
  .game-icon {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

/* Landscape orientation adjustments */
@media (orientation: landscape) and (max-height: 600px) {
  :root {
    --spacing-md: 12px;
    --spacing-lg: 16px;
    --table-min-height: 500px;
  }

  .landscape-compact {
    padding: var(--spacing-sm) !important;
  }
}

/* Portrait orientation adjustments */
@media (orientation: portrait) and (max-width: 768px) {
  .portrait-stack {
    flex-direction: column !important;
  }

  .portrait-full-width {
    width: 100% !important;
  }
}

/* Utility classes for responsive behavior */
.responsive-text {
  font-size: clamp(var(--font-size-sm), 2.5vw, var(--font-size-lg));
}

.responsive-spacing {
  padding: clamp(var(--spacing-sm), 2vw, var(--spacing-lg));
}

.responsive-margin {
  margin: clamp(var(--spacing-sm), 2vw, var(--spacing-lg));
}

/* Responsive card sizing */
.card-responsive {
  width: clamp(var(--card-width-xs), 5vw, var(--card-width-lg));
  height: clamp(var(--card-height-xs), 7vw, var(--card-height-lg));
}

/* Responsive table sizing */
.table-responsive {
  width: clamp(var(--table-min-width), 90vw, var(--table-max-width));
  height: clamp(var(--table-min-height), 80vh, var(--table-max-height));
}

/* Responsive sidebar */
.sidebar-responsive {
  width: clamp(var(--sidebar-width-sm), 25vw, var(--sidebar-width-lg));
}

/* Flexible layouts */
.flex-responsive {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-md);
}

.flex-responsive > * {
  flex: 1 1 auto;
  min-width: 0;
}

/* Grid layouts */
.grid-responsive {
  display: grid;
  gap: var(--spacing-md);
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

/* Aspect ratio utilities */
.aspect-ratio-card {
  aspect-ratio: 5 / 7; /* Standard playing card ratio */
}

.aspect-ratio-square {
  aspect-ratio: 1 / 1;
}

.aspect-ratio-wide {
  aspect-ratio: 16 / 9;
}

/* Container queries (for modern browsers) */
@supports (container-type: inline-size) {
  .container-responsive {
    container-type: inline-size;
  }

  @container (max-width: 400px) {
    .container-compact {
      padding: var(--spacing-sm);
      font-size: var(--font-size-sm);
    }
  }

  @container (min-width: 600px) {
    .container-expanded {
      padding: var(--spacing-lg);
      font-size: var(--font-size-lg);
    }
  }
}

/* Accessibility and reduced motion */
@media (prefers-reduced-motion: reduce) {
  .responsive-animation {
    animation: none !important;
    transition: none !important;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .responsive-contrast {
    border: 2px solid currentColor;
    background: Canvas;
    color: CanvasText;
  }
}

/* Print styles */
@media print {
  .responsive-container {
    max-width: none;
    padding: 0;
  }

  .hide-on-print {
    display: none !important;
  }

  .print-only {
    display: block !important;
  }
}

/* Focus management for keyboard navigation */
.responsive-focus:focus-visible {
  outline: 2px solid #4a9eff;
  outline-offset: 2px;
  border-radius: 4px;
}

/* Smooth scrolling for better UX */
@media (prefers-reduced-motion: no-preference) {
  html {
    scroll-behavior: smooth;
  }
}

/**
 * Apple-Inspired Design System
 * Clean, minimal, elegant UI with subtle depth and smooth animations
 */

/* Apple Design System Variables */
:root {
  /* Apple-inspired Color Palette */
  --apple-white: #ffffff;
  --apple-gray-50: #fafafa;
  --apple-gray-100: #f5f5f7;
  --apple-gray-200: #e8e8ed;
  --apple-gray-300: #d2d2d7;
  --apple-gray-400: #86868b;
  --apple-gray-500: #6e6e73;
  --apple-gray-600: #515154;
  --apple-gray-700: #424245;
  --apple-gray-800: #1d1d1f;
  --apple-gray-900: #000000;
  
  /* Apple Blue Accent */
  --apple-blue: #007aff;
  --apple-blue-light: #5ac8fa;
  --apple-blue-dark: #0051d5;
  
  /* Apple Green (for bridge theme) */
  --apple-green: #30d158;
  --apple-green-light: #63e6e2;
  --apple-green-dark: #248a3d;
  
  /* Apple System Colors */
  --apple-red: #ff3b30;
  --apple-orange: #ff9500;
  --apple-yellow: #ffcc00;
  --apple-purple: #af52de;
  --apple-pink: #ff2d92;
  --apple-indigo: #5856d6;
  --apple-teal: #5ac8fa;
  
  /* Background Colors */
  --bg-primary: var(--apple-gray-50);
  --bg-secondary: var(--apple-white);
  --bg-tertiary: var(--apple-gray-100);
  --bg-elevated: var(--apple-white);
  --bg-overlay: rgba(0, 0, 0, 0.4);
  
  /* Text Colors */
  --text-primary: var(--apple-gray-800);
  --text-secondary: var(--apple-gray-600);
  --text-tertiary: var(--apple-gray-500);
  --text-inverse: var(--apple-white);
  
  /* Border Colors */
  --border-primary: var(--apple-gray-200);
  --border-secondary: var(--apple-gray-300);
  --border-focus: var(--apple-blue);
  
  /* Apple Typography */
  --font-family-system: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'SF Pro Text', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  --font-family-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  
  /* Font Weights (Apple-style) */
  --font-weight-ultralight: 100;
  --font-weight-thin: 200;
  --font-weight-light: 300;
  --font-weight-regular: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-heavy: 800;
  --font-weight-black: 900;
  
  /* Font Sizes (Apple-style scale) */
  --font-size-caption2: 11px;
  --font-size-caption1: 12px;
  --font-size-footnote: 13px;
  --font-size-subheadline: 15px;
  --font-size-callout: 16px;
  --font-size-body: 17px;
  --font-size-headline: 17px;
  --font-size-title3: 20px;
  --font-size-title2: 22px;
  --font-size-title1: 28px;
  --font-size-large-title: 34px;
  
  /* Apple Spacing Scale */
  --spacing-2xs: 2px;
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 12px;
  --spacing-lg: 16px;
  --spacing-xl: 20px;
  --spacing-2xl: 24px;
  --spacing-3xl: 32px;
  --spacing-4xl: 40px;
  --spacing-5xl: 48px;
  --spacing-6xl: 64px;
  
  /* Apple Border Radius */
  --radius-xs: 4px;
  --radius-sm: 6px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
  --radius-2xl: 20px;
  --radius-3xl: 24px;
  --radius-full: 9999px;
  
  /* Apple Shadows */
  --shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.04);
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.05), 0 2px 4px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1), 0 10px 10px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px rgba(0, 0, 0, 0.15);
  
  /* Apple Transitions */
  --transition-fast: 0.15s ease-out;
  --transition-normal: 0.2s ease-out;
  --transition-slow: 0.3s ease-out;
  --transition-spring: 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  
  /* Apple Blur Effects */
  --blur-xs: blur(4px);
  --blur-sm: blur(8px);
  --blur-md: blur(12px);
  --blur-lg: blur(16px);
  --blur-xl: blur(24px);
  
  /* Z-Index Scale */
  --z-base: 0;
  --z-raised: 10;
  --z-floating: 100;
  --z-overlay: 200;
  --z-modal: 300;
  --z-popover: 400;
  --z-tooltip: 500;
  --z-toast: 600;
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  :root {
    --bg-primary: var(--apple-gray-800);
    --bg-secondary: var(--apple-gray-700);
    --bg-tertiary: var(--apple-gray-600);
    --bg-elevated: var(--apple-gray-700);
    --bg-overlay: rgba(0, 0, 0, 0.6);
    
    --text-primary: var(--apple-gray-100);
    --text-secondary: var(--apple-gray-300);
    --text-tertiary: var(--apple-gray-400);
    
    --border-primary: var(--apple-gray-600);
    --border-secondary: var(--apple-gray-500);
  }
}

/* Apple Base Styles */
.apple-base {
  font-family: var(--font-family-system);
  font-weight: var(--font-weight-regular);
  line-height: 1.47;
  letter-spacing: -0.022em;
  color: var(--text-primary);
  background-color: var(--bg-primary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

/* Apple Typography Classes */
.apple-large-title {
  font-size: var(--font-size-large-title);
  font-weight: var(--font-weight-bold);
  line-height: 1.2;
  letter-spacing: -0.025em;
}

.apple-title1 {
  font-size: var(--font-size-title1);
  font-weight: var(--font-weight-bold);
  line-height: 1.25;
  letter-spacing: -0.022em;
}

.apple-title2 {
  font-size: var(--font-size-title2);
  font-weight: var(--font-weight-bold);
  line-height: 1.27;
  letter-spacing: -0.020em;
}

.apple-title3 {
  font-size: var(--font-size-title3);
  font-weight: var(--font-weight-semibold);
  line-height: 1.3;
  letter-spacing: -0.018em;
}

.apple-headline {
  font-size: var(--font-size-headline);
  font-weight: var(--font-weight-semibold);
  line-height: 1.35;
  letter-spacing: -0.016em;
}

.apple-body {
  font-size: var(--font-size-body);
  font-weight: var(--font-weight-regular);
  line-height: 1.47;
  letter-spacing: -0.022em;
}

.apple-callout {
  font-size: var(--font-size-callout);
  font-weight: var(--font-weight-regular);
  line-height: 1.5;
  letter-spacing: -0.020em;
}

.apple-subheadline {
  font-size: var(--font-size-subheadline);
  font-weight: var(--font-weight-regular);
  line-height: 1.53;
  letter-spacing: -0.018em;
}

.apple-footnote {
  font-size: var(--font-size-footnote);
  font-weight: var(--font-weight-regular);
  line-height: 1.54;
  letter-spacing: -0.016em;
}

.apple-caption1 {
  font-size: var(--font-size-caption1);
  font-weight: var(--font-weight-regular);
  line-height: 1.58;
  letter-spacing: -0.014em;
}

.apple-caption2 {
  font-size: var(--font-size-caption2);
  font-weight: var(--font-weight-regular);
  line-height: 1.64;
  letter-spacing: -0.012em;
}

/* Apple Surface Styles */
.apple-surface {
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
}

.apple-surface-elevated {
  background-color: var(--bg-elevated);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
}

.apple-surface-glass {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: var(--blur-md);
  -webkit-backdrop-filter: var(--blur-md);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
}

@media (prefers-color-scheme: dark) {
  .apple-surface-glass {
    background: rgba(28, 28, 30, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
}

/* Apple Button Styles */
.apple-button {
  font-family: var(--font-family-system);
  font-size: var(--font-size-body);
  font-weight: var(--font-weight-semibold);
  line-height: 1;
  padding: var(--spacing-md) var(--spacing-2xl);
  border: none;
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all var(--transition-fast);
  user-select: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  min-height: 44px; /* Apple's minimum touch target */
}

.apple-button-primary {
  background-color: var(--apple-blue);
  color: var(--apple-white);
  box-shadow: var(--shadow-sm);
}

.apple-button-primary:hover {
  background-color: var(--apple-blue-dark);
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.apple-button-secondary {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  border: 1px solid var(--border-primary);
  box-shadow: var(--shadow-xs);
}

.apple-button-secondary:hover {
  background-color: var(--bg-tertiary);
  box-shadow: var(--shadow-sm);
  transform: translateY(-1px);
}

.apple-button-ghost {
  background-color: transparent;
  color: var(--apple-blue);
  border: none;
}

.apple-button-ghost:hover {
  background-color: rgba(0, 122, 255, 0.1);
}

/* Apple Focus States */
.apple-focus:focus-visible {
  outline: 2px solid var(--apple-blue);
  outline-offset: 2px;
  border-radius: var(--radius-sm);
}

/* Apple Animations */
@keyframes apple-fade-in {
  from {
    opacity: 0;
    transform: translateY(8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes apple-scale-in {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes apple-slide-up {
  from {
    opacity: 0;
    transform: translateY(16px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.apple-animate-fade-in {
  animation: apple-fade-in var(--transition-normal) ease-out;
}

.apple-animate-scale-in {
  animation: apple-scale-in var(--transition-normal) ease-out;
}

.apple-animate-slide-up {
  animation: apple-slide-up var(--transition-slow) ease-out;
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  .apple-animate-fade-in,
  .apple-animate-scale-in,
  .apple-animate-slide-up {
    animation: none;
  }
  
  .apple-button {
    transition: none;
  }
}

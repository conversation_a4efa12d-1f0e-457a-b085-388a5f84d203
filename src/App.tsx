/**
 * Main App component for the Bridge Game
 */

import React, { useState } from 'react';
import GameTable from './components/GameTable/GameTable';
import { Position } from './types/bridge';
import './App.css';
import './styles/responsive.css';

const App: React.FC = () => {
  return (
    <div className="app">
      <header className="app-header">
        <div className="app-logo">
          <img src="/bridge-logo.png" alt="Bridge" className="logo-image" />
        </div>
      </header>
      <main className="app-main">
        <GameTable />
      </main>
    </div>
  );
};

export default App;

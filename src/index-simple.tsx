/**
 * Ultra-simple test to verify JavaScript execution
 */

console.log('JavaScript is executing!');

const container = document.getElementById('root');
if (container) {
    console.log('Root element found');
    container.innerHTML = '<h1>JAVASCRIPT WORKING!</h1><p>No React, just plain JS</p>';
    console.log('Content set successfully');
} else {
    console.error('Root element not found');
}

// Test if we can access window object
if (typeof window !== 'undefined') {
    console.log('Window object available');
    (window as any).testMessage = 'JavaScript execution confirmed';
} else {
    console.error('Window object not available');
}

/**
 * PlayingArea component for displaying current trick and handling card play
 * Shows the four cards played in the current trick and manages card playing interactions
 */

import React, { useState, useEffect } from 'react';
import { GameState, Position, Card, PlayedCard, GamePhase } from '../../types/bridge';
import { getValidCards, playCard } from '../../lib/bridge/gameStateUtils';
import { getTrickWinner } from '../../lib/bridge/trickUtils';
import CardComponent from '../Card/Card';
import './PlayingArea.css';

interface PlayingAreaProps {
  gameState: GameState;
  onGameStateChange: (newGameState: GameState) => void;
  humanPosition: Position;
  onCardPlay?: (card: Card, position: Position) => void;
}

const PlayingArea: React.FC<PlayingAreaProps> = ({
  gameState,
  onGameStateChange,
  humanPosition,
  onCardPlay
}) => {
  const [selectedCard, setSelectedCard] = useState<Card | null>(null);
  const [validCards, setValidCards] = useState<Card[]>([]);
  const [isAnimating, setIsAnimating] = useState(false);
  const [trickWinner, setTrickWinner] = useState<Position | null>(null);

  // Update valid cards when game state changes
  useEffect(() => {
    if (gameState.currentPlayer === humanPosition) {
      const valid = getValidCards(gameState);
      setValidCards(valid);
    } else {
      setValidCards([]);
    }
  }, [gameState, humanPosition]);

  // Check for trick completion
  useEffect(() => {
    if (gameState.currentTrick.length === 4) {
      const winner = getTrickWinner(gameState.currentTrick, gameState.contract);
      setTrickWinner(winner);
      
      // Auto-complete trick after animation
      setTimeout(() => {
        completeTrick();
      }, 2000);
    } else {
      setTrickWinner(null);
    }
  }, [gameState.currentTrick]);

  const handleCardClick = (card: Card) => {
    if (gameState.currentPlayer !== humanPosition) {
      return; // Not player's turn
    }

    if (!validCards.some(c => c.rank === card.rank && c.suit === card.suit)) {
      return; // Invalid card
    }

    setSelectedCard(card);
  };

  const handlePlayCard = () => {
    if (!selectedCard || gameState.currentPlayer !== humanPosition) {
      return;
    }

    setIsAnimating(true);
    
    try {
      const newGameState = playCard(gameState, selectedCard, humanPosition);
      onGameStateChange(newGameState);
      
      if (onCardPlay) {
        onCardPlay(selectedCard, humanPosition);
      }
    } catch (error) {
      console.error('Error playing card:', error);
    } finally {
      setSelectedCard(null);
      setIsAnimating(false);
    }
  };

  const completeTrick = () => {
    if (gameState.currentTrick.length !== 4) return;

    const newGameState = { ...gameState };
    
    // Move current trick to completed tricks
    const trick = {
      id: newGameState.tricks.length + 1,
      cards: [...gameState.currentTrick],
      winner: trickWinner!,
      leadPlayer: gameState.currentTrick[0].player
    };
    newGameState.tricks.push(trick);
    newGameState.currentTrick = [];
    
    // Winner leads next trick
    if (trickWinner) {
      newGameState.currentPlayer = trickWinner;
    }
    
    // Check if hand is complete
    const totalTricks = newGameState.tricks.length;
    if (totalTricks === 13) {
      // Hand complete - calculate scores, etc.
      // This would trigger end-of-hand logic
    }
    
    onGameStateChange(newGameState);
    setTrickWinner(null);
  };

  const getCardPosition = (position: Position): { top: string; left: string; transform: string } => {
    switch (position) {
      case Position.NORTH:
        return { top: '20%', left: '50%', transform: 'translateX(-50%)' };
      case Position.EAST:
        return { top: '50%', left: '80%', transform: 'translateY(-50%)' };
      case Position.SOUTH:
        return { top: '80%', left: '50%', transform: 'translateX(-50%)' };
      case Position.WEST:
        return { top: '50%', left: '20%', transform: 'translateY(-50%)' };
      default:
        return { top: '50%', left: '50%', transform: 'translate(-50%, -50%)' };
    }
  };

  const getPlayedCard = (position: Position): PlayedCard | null => {
    return gameState.currentTrick.find(pc => pc.player === position) || null;
  };

  const isCardValid = (card: Card): boolean => {
    return validCards.some(c => c.rank === card.rank && c.suit === card.suit);
  };

  const isPlayerTurn = gameState.currentPlayer === humanPosition;
  const humanPlayer = gameState.players[humanPosition];

  // Show hand during play phase or when it's the player's turn
  const shouldShowHand = gameState.phase === GamePhase.PLAYING || isPlayerTurn;

  return (
    <div className="playing-area" data-testid="playing-area">
      {/* Current Trick Display */}
      <div className="trick-area">
        {/* Played cards in current trick */}
        {[Position.NORTH, Position.EAST, Position.SOUTH, Position.WEST].map(position => {
          const playedCard = getPlayedCard(position);
          const cardPosition = getCardPosition(position);
          
          return playedCard ? (
            <div
              key={position}
              className={`played-card ${position.toLowerCase()} ${trickWinner === position ? 'winner' : ''}`}
              style={cardPosition}
            >
              <CardComponent
                card={playedCard.card}
                size="medium"
              />
              <div className="player-label">
                {gameState.players[position].name}
              </div>
            </div>
          ) : (
            <div
              key={position}
              className={`card-placeholder ${position.toLowerCase()} ${gameState.currentPlayer === position ? 'current' : ''}`}
              style={cardPosition}
            >
              <div className="placeholder-card">
                {gameState.currentPlayer === position && (
                  <div className="thinking-indicator">
                    <div className="spinner"></div>
                  </div>
                )}
              </div>
              <div className="player-label">
                {gameState.players[position].name}
              </div>
            </div>
          );
        })}

        {/* Trick winner indicator */}
        {trickWinner && (
          <div className="trick-winner-indicator">
            <div className="winner-message">
              {gameState.players[trickWinner].name} wins the trick!
            </div>
          </div>
        )}
      </div>

      {/* Human Player's Hand - DISABLED: Hand now shows in South position */}
      {false && shouldShowHand && (
        <div className="player-hand-area">
          <div className="hand-title">Your Hand - Select a card to play</div>
          <div className="hand-cards">
            {humanPlayer.hand.map((card, index) => (
              <div
                key={`${card.suit}-${card.rank}-${index}`}
                className={`hand-card ${isCardValid(card) ? 'valid' : 'invalid'} ${
                  selectedCard?.rank === card.rank && selectedCard?.suit === card.suit ? 'selected' : ''
                }`}
                onClick={() => handleCardClick(card)}
              >
                <CardComponent
                  card={card}
                  size="small"
                  isPlayable={isCardValid(card)}
                />
              </div>
            ))}
          </div>
          
          {selectedCard && selectedCard !== null && (
            <div className="play-card-controls">
              <div className="selected-card-display">
                <span>Selected: </span>
                <CardComponent
                  card={selectedCard as Card}
                  size="small"
                />
              </div>
              <button
                className="play-card-button"
                onClick={handlePlayCard}
                disabled={isAnimating}
              >
                {isAnimating ? 'Playing...' : 'Play Card'}
              </button>
              <button
                className="cancel-button"
                onClick={() => setSelectedCard(null)}
                disabled={isAnimating}
              >
                Cancel
              </button>
            </div>
          )}
        </div>
      )}

      {/* Game Status */}
      <div className="playing-status">
        <div className="current-player">
          {isPlayerTurn ? "Your turn" : `${gameState.players[gameState.currentPlayer].name}'s turn`}
        </div>
        {gameState.contract && (
          <div className="contract-info">
            Contract: {gameState.contract.level}{gameState.contract.suit} 
            by {gameState.players[gameState.contract.declarer].name}
          </div>
        )}
        <div className="trick-count">
          Trick {gameState.tricks.length + 1} of 13
        </div>
      </div>
    </div>
  );
};

export default PlayingArea;

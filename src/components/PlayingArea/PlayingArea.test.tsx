/**
 * Unit tests for PlayingArea component
 */

import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import PlayingArea from './PlayingArea';
import { 
  Position, 
  PlayerType, 
  GamePhase, 
  GameState,
  Suit,
  Rank,
  Card
} from '../../types/bridge';

// Mock the game state utils
jest.mock('../../lib/bridge/gameStateUtils', () => ({
  getValidCards: jest.fn(),
  playCard: jest.fn(),
}));

// Mock the trick utils
jest.mock('../../lib/bridge/trickUtils', () => ({
  getTrickWinner: jest.fn(),
}));

// Mock the Card component
jest.mock('../Card/Card', () => {
  return function MockCard({ card, faceUp, size, className }: any) {
    return (
      <div 
        className={`mock-card ${className || ''}`}
        data-testid={`card-${card.rank}-${card.suit}`}
        data-face-up={faceUp}
        data-size={size}
      >
        {card.rank}{card.suit}
      </div>
    );
  };
});

const mockGetValidCards = require('../../lib/bridge/gameStateUtils').getValidCards;
const mockPlayCard = require('../../lib/bridge/gameStateUtils').playCard;
const mockGetTrickWinner = require('../../lib/bridge/trickUtils').getTrickWinner;

describe('PlayingArea', () => {
  const mockCard1: Card = { suit: Suit.HEARTS, rank: Rank.ACE };
  const mockCard2: Card = { suit: Suit.SPADES, rank: Rank.KING };
  const mockCard3: Card = { suit: Suit.DIAMONDS, rank: Rank.QUEEN };

  const mockGameState: GameState = {
    id: 'test-game',
    phase: GamePhase.PLAYING,
    dealer: Position.NORTH,
    currentPlayer: Position.SOUTH,
    players: {
      [Position.NORTH]: {
        id: 'north',
        name: 'North Player',
        position: Position.NORTH,
        type: PlayerType.AI,
        hand: []
      },
      [Position.SOUTH]: {
        id: 'south',
        name: 'You',
        position: Position.SOUTH,
        type: PlayerType.HUMAN,
        hand: [mockCard1, mockCard2, mockCard3]
      },
      [Position.EAST]: {
        id: 'east',
        name: 'East Player',
        position: Position.EAST,
        type: PlayerType.AI,
        hand: []
      },
      [Position.WEST]: {
        id: 'west',
        name: 'West Player',
        position: Position.WEST,
        type: PlayerType.AI,
        hand: []
      }
    },
    auction: [],
    contract: {
      level: 3,
      suit: Suit.NO_TRUMP,
      declarer: Position.SOUTH,
      doubled: 'none'
    },
    tricks: [],
    currentTrick: [],
    dummy: null,
    vulnerabilities: { northSouth: false, eastWest: false },
    score: { northSouth: 0, eastWest: 0 },
    gameNumber: 1,
    rubberScore: { northSouth: 0, eastWest: 0 }
  };

  const mockOnGameStateChange = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    mockGetValidCards.mockReturnValue([mockCard1, mockCard2]);
    mockPlayCard.mockReturnValue({ ...mockGameState });
    mockGetTrickWinner.mockReturnValue(Position.SOUTH);
  });

  it('renders without crashing', () => {
    render(
      <PlayingArea
        gameState={mockGameState}
        onGameStateChange={mockOnGameStateChange}
        humanPosition={Position.SOUTH}
      />
    );
    
    expect(screen.getByTestId('playing-area')).toBeInTheDocument();
  });

  it('displays trick area', () => {
    render(
      <PlayingArea
        gameState={mockGameState}
        onGameStateChange={mockOnGameStateChange}
        humanPosition={Position.SOUTH}
      />
    );
    
    expect(screen.getByText('Your turn')).toBeInTheDocument();
    expect(screen.getByText('Trick 1 of 13')).toBeInTheDocument();
  });

  it('shows contract information', () => {
    render(
      <PlayingArea
        gameState={mockGameState}
        onGameStateChange={mockOnGameStateChange}
        humanPosition={Position.SOUTH}
      />
    );
    
    expect(screen.getByText(/Contract: 3NO_TRUMP by You/)).toBeInTheDocument();
  });

  it('displays player hand when it is their turn', () => {
    render(
      <PlayingArea
        gameState={mockGameState}
        onGameStateChange={mockOnGameStateChange}
        humanPosition={Position.SOUTH}
      />
    );
    
    expect(screen.getByText('Your Hand - Select a card to play')).toBeInTheDocument();
    expect(screen.getByTestId('card-ACE-HEARTS')).toBeInTheDocument();
    expect(screen.getByTestId('card-KING-SPADES')).toBeInTheDocument();
    expect(screen.getByTestId('card-QUEEN-DIAMONDS')).toBeInTheDocument();
  });

  it('does not show player hand when it is not their turn', () => {
    const gameStateNotTurn = {
      ...mockGameState,
      currentPlayer: Position.NORTH
    };

    render(
      <PlayingArea
        gameState={gameStateNotTurn}
        onGameStateChange={mockOnGameStateChange}
        humanPosition={Position.SOUTH}
      />
    );
    
    expect(screen.queryByText('Your Hand - Select a card to play')).not.toBeInTheDocument();
    expect(screen.getByText("North Player's turn")).toBeInTheDocument();
  });

  it('allows card selection when valid', () => {
    render(
      <PlayingArea
        gameState={mockGameState}
        onGameStateChange={mockOnGameStateChange}
        humanPosition={Position.SOUTH}
      />
    );
    
    const cardElement = screen.getByTestId('card-ACE-HEARTS');
    fireEvent.click(cardElement);
    
    expect(screen.getByText('Selected:')).toBeInTheDocument();
    expect(screen.getByText('Play Card')).toBeInTheDocument();
    expect(screen.getByText('Cancel')).toBeInTheDocument();
  });

  it('does not allow selection of invalid cards', () => {
    // Mock only first card as valid
    mockGetValidCards.mockReturnValue([mockCard1]);
    
    render(
      <PlayingArea
        gameState={mockGameState}
        onGameStateChange={mockOnGameStateChange}
        humanPosition={Position.SOUTH}
      />
    );
    
    const invalidCard = screen.getByTestId('card-KING-SPADES');
    fireEvent.click(invalidCard);
    
    expect(screen.queryByText('Selected:')).not.toBeInTheDocument();
  });

  it('plays card when play button is clicked', () => {
    render(
      <PlayingArea
        gameState={mockGameState}
        onGameStateChange={mockOnGameStateChange}
        humanPosition={Position.SOUTH}
      />
    );
    
    // Select a card
    const cardElement = screen.getByTestId('card-ACE-HEARTS');
    fireEvent.click(cardElement);
    
    // Play the card
    const playButton = screen.getByText('Play Card');
    fireEvent.click(playButton);
    
    expect(mockPlayCard).toHaveBeenCalledWith(mockGameState, mockCard1, Position.SOUTH);
    expect(mockOnGameStateChange).toHaveBeenCalled();
  });

  it('cancels card selection', () => {
    render(
      <PlayingArea
        gameState={mockGameState}
        onGameStateChange={mockOnGameStateChange}
        humanPosition={Position.SOUTH}
      />
    );
    
    // Select a card
    const cardElement = screen.getByTestId('card-ACE-HEARTS');
    fireEvent.click(cardElement);
    
    expect(screen.getByText('Selected:')).toBeInTheDocument();
    
    // Cancel selection
    const cancelButton = screen.getByText('Cancel');
    fireEvent.click(cancelButton);
    
    expect(screen.queryByText('Selected:')).not.toBeInTheDocument();
  });

  it('displays played cards in current trick', () => {
    const gameStateWithTrick = {
      ...mockGameState,
      currentTrick: [
        { card: mockCard1, player: Position.NORTH },
        { card: mockCard2, player: Position.EAST }
      ]
    };

    render(
      <PlayingArea
        gameState={gameStateWithTrick}
        onGameStateChange={mockOnGameStateChange}
        humanPosition={Position.SOUTH}
      />
    );
    
    expect(screen.getByTestId('card-ACE-HEARTS')).toBeInTheDocument();
    expect(screen.getByTestId('card-KING-SPADES')).toBeInTheDocument();
  });

  it('shows trick winner when trick is complete', () => {
    const gameStateCompleteTrick = {
      ...mockGameState,
      currentTrick: [
        { card: mockCard1, player: Position.NORTH },
        { card: mockCard2, player: Position.EAST },
        { card: mockCard3, player: Position.SOUTH },
        { card: { suit: Suit.CLUBS, rank: Rank.JACK }, player: Position.WEST }
      ]
    };

    render(
      <PlayingArea
        gameState={gameStateCompleteTrick}
        onGameStateChange={mockOnGameStateChange}
        humanPosition={Position.SOUTH}
      />
    );
    
    expect(screen.getByText('You wins the trick!')).toBeInTheDocument();
  });

  it('calls onCardPlay callback when provided', () => {
    const mockOnCardPlay = jest.fn();
    
    render(
      <PlayingArea
        gameState={mockGameState}
        onGameStateChange={mockOnGameStateChange}
        humanPosition={Position.SOUTH}
        onCardPlay={mockOnCardPlay}
      />
    );
    
    // Select and play a card
    const cardElement = screen.getByTestId('card-ACE-HEARTS');
    fireEvent.click(cardElement);
    
    const playButton = screen.getByText('Play Card');
    fireEvent.click(playButton);
    
    expect(mockOnCardPlay).toHaveBeenCalledWith(mockCard1, Position.SOUTH);
  });

  it('shows current player indicator for AI players', () => {
    const gameStateAITurn = {
      ...mockGameState,
      currentPlayer: Position.NORTH
    };

    render(
      <PlayingArea
        gameState={gameStateAITurn}
        onGameStateChange={mockOnGameStateChange}
        humanPosition={Position.SOUTH}
      />
    );
    
    // Should show thinking indicator for current AI player
    expect(screen.getByText("North Player's turn")).toBeInTheDocument();
  });
});

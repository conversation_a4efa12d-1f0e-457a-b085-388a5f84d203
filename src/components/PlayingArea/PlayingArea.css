/**
 * PlayingArea component styles
 */

.playing-area {
  position: relative;
  width: 100%;
  height: 100%;
  min-height: 500px;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* Trick Area - Center of the table */
.trick-area {
  position: relative;
  flex: 1;
  min-height: 300px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  border: 2px dashed rgba(255, 255, 255, 0.2);
}

/* Played Cards */
.played-card {
  position: absolute;
  z-index: 10;
  transition: all 0.3s ease;
}

.played-card.winner {
  z-index: 20;
  transform: scale(1.1) !important;
  filter: drop-shadow(0 0 12px #ffd700);
}

.played-card .trick-card {
  transition: all 0.3s ease;
}

.played-card .trick-card.animating {
  animation: cardPlay 0.5s ease-out;
}

@keyframes cardPlay {
  0% {
    transform: scale(0.8) translateY(-20px);
    opacity: 0;
  }
  50% {
    transform: scale(1.1) translateY(-10px);
    opacity: 0.8;
  }
  100% {
    transform: scale(1) translateY(0);
    opacity: 1;
  }
}

/* Card Placeholders */
.card-placeholder {
  position: absolute;
  z-index: 5;
  transition: all 0.3s ease;
}

.card-placeholder.current {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
}

.placeholder-card {
  width: 80px;
  height: 112px;
  border: 2px dashed rgba(255, 255, 255, 0.4);
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.card-placeholder.current .placeholder-card {
  border-color: #4a9eff;
  background: rgba(74, 158, 255, 0.2);
}

/* Thinking Indicator */
.thinking-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
}

.thinking-indicator .spinner {
  width: 24px;
  height: 24px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-top: 3px solid #4a9eff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Player Labels */
.player-label {
  position: absolute;
  top: 120px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  white-space: nowrap;
  pointer-events: none;
}

.played-card.north .player-label,
.card-placeholder.north .player-label {
  top: -30px;
}

.played-card.south .player-label,
.card-placeholder.south .player-label {
  top: 120px;
}

.played-card.east .player-label,
.card-placeholder.east .player-label {
  top: 50%;
  left: -60px;
  transform: translateY(-50%);
}

.played-card.west .player-label,
.card-placeholder.west .player-label {
  top: 50%;
  left: 90px;
  transform: translateY(-50%);
}

/* Trick Winner Indicator */
.trick-winner-indicator {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 30;
  animation: fadeInScale 0.5s ease-out;
}

@keyframes fadeInScale {
  0% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.5);
  }
  100% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}

.winner-message {
  background: linear-gradient(135deg, #ffd700, #ffed4e);
  color: #333;
  padding: 12px 20px;
  border-radius: 8px;
  font-weight: 600;
  font-size: 16px;
  box-shadow: 0 4px 12px rgba(255, 215, 0, 0.4);
  text-align: center;
  border: 2px solid #ffd700;
}

/* Player Hand Area */
.player-hand-area {
  background: rgba(0, 0, 0, 0.6);
  border-radius: 8px;
  padding: 16px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.hand-title {
  color: #ffd700;
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 12px;
  text-align: center;
}

.hand-cards {
  display: flex;
  gap: 8px;
  justify-content: center;
  flex-wrap: nowrap;
  margin-bottom: 16px;
  overflow-x: auto;
}

.hand-card {
  cursor: pointer;
  transition: all 0.2s ease;
  border-radius: 6px;
  padding: 2px;
}

.hand-card:hover {
  transform: translateY(-4px);
}

.hand-card.valid {
  border: 2px solid transparent;
}

.hand-card.valid:hover {
  border-color: #4a9eff;
  box-shadow: 0 0 12px rgba(74, 158, 255, 0.4);
}

.hand-card.invalid {
  opacity: 0.5;
  cursor: not-allowed;
}

.hand-card.invalid:hover {
  transform: none;
}

.hand-card.selected {
  border-color: #ffd700;
  box-shadow: 0 0 16px rgba(255, 215, 0, 0.6);
  transform: translateY(-6px);
}

.playable-card.disabled {
  filter: grayscale(100%);
}

/* Play Card Controls */
.play-card-controls {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
  padding: 12px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 6px;
}

.selected-card-display {
  display: flex;
  align-items: center;
  gap: 8px;
  color: white;
  font-weight: 500;
}

.selected-card-preview {
  transform: scale(0.8);
}

.play-card-button {
  background: linear-gradient(135deg, #28a745, #20c997);
  color: white;
  border: none;
  border-radius: 6px;
  padding: 10px 20px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.play-card-button:hover:not(:disabled) {
  background: linear-gradient(135deg, #218838, #1ea085);
  transform: translateY(-1px);
}

.play-card-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.cancel-button {
  background: transparent;
  color: #dc3545;
  border: 2px solid #dc3545;
  border-radius: 6px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.cancel-button:hover:not(:disabled) {
  background: #dc3545;
  color: white;
}

.cancel-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Playing Status */
.playing-status {
  background: rgba(0, 0, 0, 0.6);
  border-radius: 6px;
  padding: 12px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 12px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.current-player {
  color: #4a9eff;
  font-weight: 600;
  font-size: 14px;
}

.contract-info {
  color: #ffd700;
  font-weight: 500;
  font-size: 14px;
}

.trick-count {
  color: #cccccc;
  font-size: 14px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .playing-area {
    padding: 12px;
    min-height: 400px;
  }
  
  .trick-area {
    min-height: 250px;
  }
  
  .placeholder-card {
    width: 60px;
    height: 84px;
  }
  
  .hand-cards {
    gap: 4px;
  }
  
  .play-card-controls {
    flex-direction: column;
    gap: 8px;
  }
  
  .playing-status {
    flex-direction: column;
    text-align: center;
    gap: 8px;
  }
  
  .player-label {
    font-size: 10px;
    padding: 2px 6px;
  }
}

/* Accessibility */
.playing-area button:focus {
  outline: 2px solid #4a9eff;
  outline-offset: 2px;
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .played-card,
  .card-placeholder,
  .hand-card,
  .play-card-button,
  .cancel-button {
    transition: none;
    animation: none;
  }
  
  .hand-card:hover {
    transform: none;
  }
  
  .thinking-indicator .spinner {
    animation: none;
  }
}

/**
 * BiddingStatus component showing current bidding information
 * Displays current contract, dealer, vulnerability, and bidding progress
 */

import React from 'react';
import { GameState, Position, GamePhase, BidSuit } from '../../types/bridge';
import { getLastContractBid } from '../../lib/bridge/biddingUtils';
import './BiddingStatus.css';

interface BiddingStatusProps {
  gameState: GameState;
}

const BiddingStatus: React.FC<BiddingStatusProps> = ({ gameState }) => {
  const lastContractBid = getLastContractBid(gameState.auction);
  const passCount = gameState.auction.slice(-3).filter(bid => bid.value === 'PASS').length;
  
  const formatPosition = (position: Position): string => {
    const names = {
      [Position.NORTH]: 'North',
      [Position.EAST]: 'East', 
      [Position.SOUTH]: 'South',
      [Position.WEST]: 'West'
    };
    return names[position];
  };

  const formatVulnerability = () => {
    const { northSouth, eastWest } = gameState.vulnerabilities;
    if (northSouth && eastWest) return 'Both Vulnerable';
    if (northSouth) return 'N-S Vulnerable';
    if (eastWest) return 'E-W Vulnerable';
    return 'None Vulnerable';
  };

  const formatContract = () => {
    if (!lastContractBid) return 'No bids yet';
    
    if (typeof lastContractBid.value === 'string') {
      return lastContractBid.value;
    }
    
    const { level, suit } = lastContractBid.value;
    const suitSymbol = suit === BidSuit.CLUBS ? '♣' :
                     suit === BidSuit.DIAMONDS ? '♦' :
                     suit === BidSuit.HEARTS ? '♥' :
                     suit === BidSuit.SPADES ? '♠' : 'NT';
    return `${level}${suitSymbol}`;
  };

  const getBiddingProgress = () => {
    if (gameState.phase !== GamePhase.BIDDING) {
      return 'Bidding Complete';
    }
    
    if (passCount === 3 && lastContractBid) {
      return 'Bidding ending...';
    }
    
    if (gameState.auction.length === 0) {
      return 'Bidding starting';
    }
    
    return `${gameState.auction.length} bid${gameState.auction.length === 1 ? '' : 's'} made`;
  };

  return (
    <div className="bidding-status" data-testid="bidding-status">
      <div className="bidding-status-header">
        <h3>Bidding Status</h3>
      </div>
      
      <div className="bidding-status-content">
        <div className="status-row">
          <span className="status-label">Dealer:</span>
          <span className="status-value">{formatPosition(gameState.dealer)}</span>
        </div>
        
        <div className="status-row">
          <span className="status-label">Vulnerability:</span>
          <span className="status-value vulnerability">{formatVulnerability()}</span>
        </div>
        
        <div className="status-row">
          <span className="status-label">Current Bid:</span>
          <span className="status-value current-bid">{formatContract()}</span>
        </div>
        
        <div className="status-row">
          <span className="status-label">Progress:</span>
          <span className="status-value progress">{getBiddingProgress()}</span>
        </div>
        
        {gameState.phase === GamePhase.BIDDING && (
          <div className="status-row">
            <span className="status-label">Turn:</span>
            <span className="status-value current-player">
              {formatPosition(gameState.currentPlayer)}
            </span>
          </div>
        )}
        
        {gameState.contract && (
          <div className="status-row">
            <span className="status-label">Declarer:</span>
            <span className="status-value declarer">
              {formatPosition(gameState.contract.declarer)}
            </span>
          </div>
        )}
      </div>
    </div>
  );
};

export default BiddingStatus;

/**
 * BiddingStatus component styles
 * Clean, compact display of bidding information
 */

.bidding-status {
  width: 100%;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  overflow: hidden;
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', sans-serif;
}

.bidding-status-header {
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  padding: 12px 16px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.bidding-status-header h3 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #ecf0f1;
  text-align: center;
}

.bidding-status-content {
  padding: 12px 16px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.status-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  font-size: 12px;
}

.status-row:last-child {
  border-bottom: none;
}

.status-label {
  color: #bdc3c7;
  font-weight: 500;
  flex: 0 0 auto;
}

.status-value {
  color: #ecf0f1;
  font-weight: 600;
  text-align: right;
  flex: 1;
}

/* Specific styling for different value types */
.status-value.vulnerability {
  color: #e74c3c;
}

.status-value.current-bid {
  color: #3498db;
  font-size: 13px;
  font-weight: 700;
}

.status-value.progress {
  color: #95a5a6;
  font-style: italic;
}

.status-value.current-player {
  color: #f39c12;
  font-weight: 700;
}

.status-value.declarer {
  color: #2ecc71;
  font-weight: 700;
}

/* Responsive design */
@media (max-width: 768px) {
  .bidding-status-header h3 {
    font-size: 13px;
  }
  
  .bidding-status-content {
    padding: 10px 12px;
    gap: 6px;
  }
  
  .status-row {
    font-size: 11px;
  }
  
  .status-value.current-bid {
    font-size: 12px;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .bidding-status {
    background: rgba(0, 0, 0, 0.3);
  }
  
  .bidding-status-header {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
  }
  
  .bidding-status-header h3 {
    color: #f5f5f5;
  }
  
  .status-label {
    color: #a0a0a0;
  }
  
  .status-value {
    color: #f5f5f5;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .bidding-status {
    background: white;
    border: 2px solid black;
  }
  
  .bidding-status-header {
    background: black;
  }
  
  .bidding-status-header h3 {
    color: white;
  }
  
  .status-label {
    color: #333;
  }
  
  .status-value {
    color: black;
  }
  
  .status-value.vulnerability {
    color: #cc0000;
  }
  
  .status-value.current-bid {
    color: #0066cc;
  }
}

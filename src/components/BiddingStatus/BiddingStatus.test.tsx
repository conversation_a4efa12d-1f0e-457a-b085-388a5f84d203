/**
 * Tests for BiddingStatus component
 */

import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import BiddingStatus from './BiddingStatus';
import { Position, GameState, GamePhase, PlayerType, BidLevel, BidSuit } from '../../types/bridge';

const createMockGameState = (): GameState => ({
  id: 'test-game',
  players: {
    [Position.NORTH]: { type: PlayerType.AI, hand: [] },
    [Position.EAST]: { type: PlayerType.AI, hand: [] },
    [Position.SOUTH]: { type: PlayerType.HUMAN, hand: [] },
    [Position.WEST]: { type: PlayerType.AI, hand: [] }
  },
  currentPlayer: Position.SOUTH,
  phase: GamePhase.BIDDING,
  auction: [
    {
      player: Position.SOUTH,
      value: { level: BidLevel.ONE, suit: BidSuit.CLUBS },
      timestamp: new Date()
    }
  ],
  currentTrick: [],
  tricks: [],
  score: { northSouth: 0, eastWest: 0 },
  dealer: Position.NORTH,
  vulnerabilities: { northSouth: false, eastWest: true },
  contract: null,
  dummy: null
});

describe('BiddingStatus', () => {
  it('renders bidding status information', () => {
    const gameState = createMockGameState();
    render(<BiddingStatus gameState={gameState} />);

    expect(screen.getByText('Bidding Status')).toBeInTheDocument();
    expect(screen.getByText('North')).toBeInTheDocument(); // Dealer
    expect(screen.getByText('E-W Vulnerable')).toBeInTheDocument(); // Vulnerability
    expect(screen.getByText('1♣')).toBeInTheDocument(); // Current bid
  });

  it('shows correct vulnerability status', () => {
    const gameState = createMockGameState();
    gameState.vulnerabilities = { northSouth: true, eastWest: true };
    
    render(<BiddingStatus gameState={gameState} />);
    expect(screen.getByText('Both Vulnerable')).toBeInTheDocument();
  });

  it('shows no vulnerability when none are vulnerable', () => {
    const gameState = createMockGameState();
    gameState.vulnerabilities = { northSouth: false, eastWest: false };
    
    render(<BiddingStatus gameState={gameState} />);
    expect(screen.getByText('None Vulnerable')).toBeInTheDocument();
  });

  it('shows current player during bidding', () => {
    const gameState = createMockGameState();
    gameState.currentPlayer = Position.EAST;
    
    render(<BiddingStatus gameState={gameState} />);
    expect(screen.getByText('East')).toBeInTheDocument();
  });

  it('shows bidding progress', () => {
    const gameState = createMockGameState();
    
    render(<BiddingStatus gameState={gameState} />);
    expect(screen.getByText('1 bid made')).toBeInTheDocument();
  });

  it('shows no bids yet when auction is empty', () => {
    const gameState = createMockGameState();
    gameState.auction = [];
    
    render(<BiddingStatus gameState={gameState} />);
    expect(screen.getByText('No bids yet')).toBeInTheDocument();
    expect(screen.getByText('Bidding starting')).toBeInTheDocument();
  });

  it('shows declarer when contract is established', () => {
    const gameState = createMockGameState();
    gameState.contract = {
      level: BidLevel.THREE,
      suit: BidSuit.NO_TRUMP,
      declarer: Position.SOUTH,
      doubled: 'none'
    };
    
    render(<BiddingStatus gameState={gameState} />);
    expect(screen.getByText('Declarer:')).toBeInTheDocument();
    expect(screen.getAllByText('South')).toHaveLength(2); // Current player and declarer
  });
});

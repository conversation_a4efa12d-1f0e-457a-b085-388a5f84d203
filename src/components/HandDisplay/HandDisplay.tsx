/**
 * HandDisplay component for showing a player's hand in a fanned layout
 * Supports sorting, selection, and different display modes
 */

import React, { useState, useEffect } from 'react';
import { Card, Suit, Rank } from '../../types/bridge';
import { sortCards } from '../../lib/bridge/cardUtils';
import CardComponent from '../Card/Card';
import './HandDisplay.css';

interface HandDisplayProps {
  cards: Card[];
  isVisible?: boolean;
  isSelectable?: boolean;
  selectedCards?: Card[];
  validCards?: Card[];
  onCardSelect?: (card: Card) => void;
  onCardDeselect?: (card: Card) => void;
  sortBy?: 'suit' | 'rank' | 'none';
  fanDirection?: 'horizontal' | 'vertical';
  maxFanAngle?: number;
  cardSpacing?: number;
  size?: 'small' | 'medium' | 'large';
  className?: string;
}

const HandDisplay: React.FC<HandDisplayProps> = ({
  cards,
  isVisible = true,
  isSelectable = false,
  selectedCards = [],
  validCards = [],
  onCardSelect,
  onCardDeselect,
  sortBy = 'suit',
  fanDirection = 'horizontal',
  maxFanAngle = 30,
  cardSpacing = 20,
  size = 'medium',
  className = ''
}) => {
  const [sortedCards, setSortedCards] = useState<Card[]>([]);
  const [hoveredCard, setHoveredCard] = useState<Card | null>(null);

  // Sort cards when cards or sortBy changes
  useEffect(() => {
    if (sortBy === 'none') {
      setSortedCards([...cards]);
    } else {
      const sorted = sortCards(cards, sortBy);
      setSortedCards(sorted);
    }
  }, [cards, sortBy]);

  // Calculate card positions for fanning effect
  const getCardStyle = (index: number, totalCards: number): React.CSSProperties => {
    if (totalCards <= 1) {
      return { transform: 'none', zIndex: index };
    }

    const centerIndex = (totalCards - 1) / 2;
    const relativeIndex = index - centerIndex;
    
    if (fanDirection === 'horizontal') {
      const angle = (relativeIndex / centerIndex) * (maxFanAngle / 2);
      const translateX = relativeIndex * cardSpacing;
      const translateY = Math.abs(relativeIndex) * 2; // Slight arc effect
      
      return {
        transform: `translateX(${translateX}px) translateY(${translateY}px) rotate(${angle}deg)`,
        zIndex: index,
        transformOrigin: 'center bottom'
      };
    } else {
      // Vertical fanning
      const angle = (relativeIndex / centerIndex) * (maxFanAngle / 2);
      const translateY = relativeIndex * cardSpacing;
      const translateX = Math.abs(relativeIndex) * 2;
      
      return {
        transform: `translateY(${translateY}px) translateX(${translateX}px) rotate(${angle}deg)`,
        zIndex: index,
        transformOrigin: 'center center'
      };
    }
  };

  // Check if a card is selected
  const isCardSelected = (card: Card): boolean => {
    return selectedCards.some(c => c.rank === card.rank && c.suit === card.suit);
  };

  // Check if a card is valid for selection
  const isCardValid = (card: Card): boolean => {
    if (validCards.length === 0) return true;
    return validCards.some(c => c.rank === card.rank && c.suit === card.suit);
  };

  // Handle card click
  const handleCardClick = (card: Card) => {
    if (!isSelectable) return;
    
    if (!isCardValid(card)) return;

    if (isCardSelected(card)) {
      onCardDeselect?.(card);
    } else {
      onCardSelect?.(card);
    }
  };

  // Handle card hover
  const handleCardHover = (card: Card | null) => {
    setHoveredCard(card);
  };

  // Get card container width for centering
  const getContainerWidth = (): number => {
    if (fanDirection === 'horizontal') {
      return Math.max(300, sortedCards.length * cardSpacing + 80);
    }
    return 120; // Fixed width for vertical
  };

  if (!isVisible) {
    return (
      <div className={`hand-display hidden ${className}`}>
        <div className="card-back-stack">
          {Array.from({ length: Math.min(cards.length, 5) }, (_, i) => (
            <div
              key={i}
              className="card-back"
              style={{
                transform: `translateX(${i * 2}px) translateY(${i * -1}px)`,
                zIndex: i
              }}
            />
          ))}
        </div>
        <div className="card-count">{cards.length} cards</div>
      </div>
    );
  }

  return (
    <div 
      className={`hand-display ${fanDirection} ${size} ${className}`}
      style={{ width: getContainerWidth() }}
      data-testid="hand-display"
    >
      <div className="cards-container">
        {sortedCards.map((card, index) => {
          const selected = isCardSelected(card);
          const valid = isCardValid(card);
          const hovered = hoveredCard?.rank === card.rank && hoveredCard?.suit === card.suit;
          
          return (
            <div
              key={`hand-display-${card.suit}-${card.rank}-${index}`}
              className={`card-wrapper ${selected ? 'selected' : ''} ${
                !valid ? 'invalid' : ''
              } ${hovered ? 'hovered' : ''} ${isSelectable ? 'selectable' : ''}`}
              style={getCardStyle(index, sortedCards.length)}
              onClick={() => handleCardClick(card)}
              onMouseEnter={() => handleCardHover(card)}
              onMouseLeave={() => handleCardHover(null)}
            >
              <CardComponent
                card={card}
                size={size}
                isPlayable={valid}
              />
              
              {/* Selection indicator */}
              {selected && (
                <div className="selection-indicator">
                  <div className="selection-checkmark">✓</div>
                </div>
              )}
              
              {/* Hover highlight */}
              {hovered && valid && !selected && (
                <div className="hover-highlight" />
              )}
            </div>
          );
        })}
      </div>
      
      {/* Hand summary */}
      <div className="hand-summary">
        <div className="card-count">{cards.length} cards</div>
        {selectedCards.length > 0 && (
          <div className="selection-count">{selectedCards.length} selected</div>
        )}
      </div>
    </div>
  );
};

export default HandDisplay;

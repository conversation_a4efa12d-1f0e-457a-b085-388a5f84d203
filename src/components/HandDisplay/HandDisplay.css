/**
 * HandDisplay component styles
 */

.hand-display {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10px;
  user-select: none;
}

.hand-display.horizontal {
  min-height: 140px;
}

.hand-display.vertical {
  min-width: 120px;
  height: 300px;
}

/* Cards Container */
.cards-container {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  flex: 1;
}

.hand-display.horizontal .cards-container {
  height: 100px;
  width: 100%;
}

.hand-display.vertical .cards-container {
  width: 80px;
  height: 100%;
  flex-direction: column;
}

/* Card Wrapper */
.card-wrapper {
  position: absolute;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: default;
  border-radius: 8px;
}

.card-wrapper.selectable {
  cursor: pointer;
}

.card-wrapper.selectable:hover {
  transform-origin: center bottom !important;
}

.hand-display.horizontal .card-wrapper.selectable:hover {
  transform: translateY(-10px) scale(1.05) !important;
  z-index: 1000 !important;
}

.hand-display.vertical .card-wrapper.selectable:hover {
  transform: translateX(-10px) scale(1.05) !important;
  z-index: 1000 !important;
}

/* Selected Cards */
.card-wrapper.selected {
  z-index: 999 !important;
}

.hand-display.horizontal .card-wrapper.selected {
  transform: translateY(-15px) scale(1.1) !important;
}

.hand-display.vertical .card-wrapper.selected {
  transform: translateX(-15px) scale(1.1) !important;
}

/* Invalid Cards */
.card-wrapper.invalid {
  opacity: 0.5;
  cursor: not-allowed;
  filter: grayscale(50%);
}

.card-wrapper.invalid:hover {
  transform: none !important;
}

/* Selection Indicator */
.selection-indicator {
  position: absolute;
  top: -8px;
  right: -8px;
  width: 24px;
  height: 24px;
  background: linear-gradient(135deg, #28a745, #20c997);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(40, 167, 69, 0.4);
  animation: selectionPop 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

@keyframes selectionPop {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.selection-checkmark {
  color: white;
  font-size: 14px;
  font-weight: bold;
  line-height: 1;
}

/* Hover Highlight */
.hover-highlight {
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border: 2px solid #4a9eff;
  border-radius: 10px;
  background: rgba(74, 158, 255, 0.1);
  animation: hoverGlow 0.3s ease-out;
}

@keyframes hoverGlow {
  0% {
    opacity: 0;
    transform: scale(0.95);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* Hidden Hand Display */
.hand-display.hidden {
  justify-content: center;
  min-height: 80px;
}

.card-back-stack {
  position: relative;
  width: 60px;
  height: 84px;
}

.card-back {
  position: absolute;
  width: 60px;
  height: 84px;
  background: linear-gradient(135deg, #1a365d, #2d5a87);
  border: 2px solid #4a9eff;
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.card-back::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 40px;
  height: 40px;
  background: repeating-linear-gradient(
    45deg,
    #4a9eff,
    #4a9eff 2px,
    transparent 2px,
    transparent 6px
  );
  border-radius: 4px;
  opacity: 0.6;
}

/* Hand Summary */
.hand-summary {
  margin-top: 8px;
  text-align: center;
  font-size: 12px;
  color: #cccccc;
}

.card-count {
  font-weight: 500;
  margin-bottom: 2px;
}

.selection-count {
  color: #28a745;
  font-weight: 600;
}

/* Size Variations */
.hand-display.small .cards-container {
  height: 70px;
}

.hand-display.small.vertical .cards-container {
  width: 60px;
  height: 200px;
}

.hand-display.large .cards-container {
  height: 130px;
}

.hand-display.large.vertical .cards-container {
  width: 100px;
  height: 400px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .hand-display {
    padding: 5px;
  }
  
  .hand-display.horizontal {
    min-height: 100px;
  }
  
  .hand-display.horizontal .cards-container {
    height: 80px;
  }
  
  .card-wrapper.selectable:hover {
    transform: translateY(-5px) scale(1.02) !important;
  }
  
  .card-wrapper.selected {
    transform: translateY(-8px) scale(1.05) !important;
  }
  
  .selection-indicator {
    width: 20px;
    height: 20px;
    top: -6px;
    right: -6px;
  }
  
  .selection-checkmark {
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .hand-display.horizontal .cards-container {
    height: 60px;
  }
  
  .hand-display.vertical .cards-container {
    width: 50px;
    height: 150px;
  }
  
  .card-back-stack {
    width: 45px;
    height: 63px;
  }
  
  .card-back {
    width: 45px;
    height: 63px;
  }
  
  .card-back::before {
    width: 30px;
    height: 30px;
  }
}

/* Accessibility */
.card-wrapper:focus {
  outline: 2px solid #4a9eff;
  outline-offset: 2px;
}

.card-wrapper.selectable:focus:hover {
  outline-color: #28a745;
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .card-wrapper,
  .selection-indicator,
  .hover-highlight {
    transition: none;
    animation: none;
  }
  
  .card-wrapper.selectable:hover {
    transform: none !important;
  }
  
  .card-wrapper.selected {
    transform: none !important;
    border: 2px solid #28a745;
  }
}

/* Print styles */
@media print {
  .hand-display {
    break-inside: avoid;
  }
  
  .selection-indicator,
  .hover-highlight {
    display: none;
  }
  
  .card-wrapper {
    position: static !important;
    transform: none !important;
    display: inline-block;
    margin: 2px;
  }
}


/* Enhanced Responsive Design for HandDisplay */
@media (max-width: 1024px) {
  .hand-display.horizontal {
    min-height: 90px;
  }
  
  .hand-display.horizontal .cards-container {
    height: 80px;
  }
  
  .card-wrapper.selectable:hover {
    transform: translateY(-6px) scale(1.03) !important;
  }
}

@media (max-width: 768px) {
  .hand-display {
    padding: 6px;
  }
  
  .hand-display.horizontal {
    min-height: 70px;
  }
  
  .hand-display.horizontal .cards-container {
    height: 60px;
  }
  
  .hand-display.vertical .cards-container {
    width: 50px;
    height: 120px;
  }
  
  .card-wrapper.selectable:hover {
    transform: translateY(-4px) scale(1.02) !important;
  }
  
  .card-wrapper.selected {
    transform: translateY(-6px) scale(1.05) !important;
  }
  
  .selection-indicator {
    width: 18px;
    height: 18px;
    top: -5px;
    right: -5px;
  }
  
  .selection-checkmark {
    font-size: 11px;
  }
}

@media (max-width: 480px) {
  .hand-display {
    padding: 4px;
  }
  
  .hand-display.horizontal {
    min-height: 50px;
  }
  
  .hand-display.horizontal .cards-container {
    height: 40px;
  }
  
  .hand-display.vertical .cards-container {
    width: 40px;
    height: 100px;
  }
  
  .card-wrapper.selectable:hover {
    transform: translateY(-2px) scale(1.01) !important;
  }
  
  .card-wrapper.selected {
    transform: translateY(-4px) scale(1.03) !important;
  }
  
  .selection-indicator {
    width: 16px;
    height: 16px;
    top: -4px;
    right: -4px;
  }
  
  .selection-checkmark {
    font-size: 10px;
  }
  
  .hand-summary {
    font-size: 10px;
  }
  
  .card-back-stack {
    width: 35px;
    height: 49px;
  }
  
  .card-back {
    width: 35px;
    height: 49px;
  }
}

/* Landscape orientation adjustments */
@media (max-width: 768px) and (orientation: landscape) {
  .hand-display.horizontal .cards-container {
    height: 50px;
  }
  
  .hand-display.vertical .cards-container {
    width: 45px;
    height: 80px;
  }
}

/* Ultra-wide screen optimizations */
@media (min-width: 1440px) {
  .hand-display.horizontal {
    min-height: 160px;
  }
  
  .hand-display.horizontal .cards-container {
    height: 120px;
  }
  
  .hand-display.vertical .cards-container {
    width: 100px;
    height: 350px;
  }
}

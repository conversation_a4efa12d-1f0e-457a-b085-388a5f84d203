/**
 * Unit tests for HandDisplay component
 */

import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import HandDisplay from './HandDisplay';
import { Card, Suit, Rank } from '../../types/bridge';

// Mock the card utils
jest.mock('../../lib/bridge/cardUtils', () => ({
  sortCards: jest.fn((cards) => [...cards]),
}));

// Mock the Card component
jest.mock('../Card/Card', () => {
  return function MockCard({ card, size, isPlayable }: any) {
    return (
      <div 
        className={`mock-card ${size} ${isPlayable ? 'playable' : 'not-playable'}`}
        data-testid={`card-${card.rank}-${card.suit}`}
      >
        {card.rank}{card.suit}
      </div>
    );
  };
});

describe('HandDisplay', () => {
  const mockCards: Card[] = [
    { suit: Suit.HEARTS, rank: Rank.ACE },
    { suit: Suit.SPADES, rank: Rank.KING },
    { suit: Suit.DIAMONDS, rank: Rank.QUEEN },
    { suit: Suit.CLUBS, rank: Rank.JACK }
  ];

  const mockOnCardSelect = jest.fn();
  const mockOnCardDeselect = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders without crashing', () => {
    render(<HandDisplay cards={mockCards} />);
    expect(screen.getByTestId('hand-display')).toBeInTheDocument();
  });

  it('displays all cards', () => {
    render(<HandDisplay cards={mockCards} />);
    
    expect(screen.getByTestId('card-ACE-HEARTS')).toBeInTheDocument();
    expect(screen.getByTestId('card-KING-SPADES')).toBeInTheDocument();
    expect(screen.getByTestId('card-QUEEN-DIAMONDS')).toBeInTheDocument();
    expect(screen.getByTestId('card-JACK-CLUBS')).toBeInTheDocument();
  });

  it('shows card count', () => {
    render(<HandDisplay cards={mockCards} />);
    expect(screen.getByText('4 cards')).toBeInTheDocument();
  });

  it('handles card selection when selectable', () => {
    render(
      <HandDisplay
        cards={mockCards}
        isSelectable={true}
        onCardSelect={mockOnCardSelect}
      />
    );
    
    const card = screen.getByTestId('card-ACE-HEARTS');
    fireEvent.click(card);
    
    expect(mockOnCardSelect).toHaveBeenCalledWith(mockCards[0]);
  });

  it('handles card deselection', () => {
    render(
      <HandDisplay
        cards={mockCards}
        isSelectable={true}
        selectedCards={[mockCards[0]]}
        onCardDeselect={mockOnCardDeselect}
      />
    );
    
    const card = screen.getByTestId('card-ACE-HEARTS');
    fireEvent.click(card);
    
    expect(mockOnCardDeselect).toHaveBeenCalledWith(mockCards[0]);
  });

  it('shows selection count when cards are selected', () => {
    render(
      <HandDisplay
        cards={mockCards}
        selectedCards={[mockCards[0], mockCards[1]]}
      />
    );
    
    expect(screen.getByText('2 selected')).toBeInTheDocument();
  });

  it('does not allow selection of invalid cards', () => {
    render(
      <HandDisplay
        cards={mockCards}
        isSelectable={true}
        validCards={[mockCards[0]]} // Only first card is valid
        onCardSelect={mockOnCardSelect}
      />
    );
    
    // Try to click invalid card
    const invalidCard = screen.getByTestId('card-KING-SPADES');
    fireEvent.click(invalidCard);
    
    expect(mockOnCardSelect).not.toHaveBeenCalled();
  });

  it('renders hidden when not visible', () => {
    render(<HandDisplay cards={mockCards} isVisible={false} />);
    
    expect(screen.getByText('4 cards')).toBeInTheDocument();
    expect(screen.queryByTestId('card-ACE-HEARTS')).not.toBeInTheDocument();
  });

  it('applies correct size class', () => {
    render(<HandDisplay cards={mockCards} size="large" />);
    expect(screen.getByTestId('hand-display')).toHaveClass('large');
  });

  it('applies correct fan direction class', () => {
    render(<HandDisplay cards={mockCards} fanDirection="vertical" />);
    expect(screen.getByTestId('hand-display')).toHaveClass('vertical');
  });

  it('handles empty card array', () => {
    render(<HandDisplay cards={[]} />);
    expect(screen.getByText('0 cards')).toBeInTheDocument();
  });

  it('applies custom className', () => {
    render(<HandDisplay cards={mockCards} className="custom-class" />);
    expect(screen.getByTestId('hand-display')).toHaveClass('custom-class');
  });
});

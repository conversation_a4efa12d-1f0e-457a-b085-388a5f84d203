/**
 * DummyDisplay component styles
 */

.dummy-display {
  background: rgba(0, 0, 0, 0.8);
  border-radius: 12px;
  padding: 16px;
  color: white;
  min-width: 280px;
  max-width: 400px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.dummy-display.vertical {
  max-width: 200px;
  min-height: 400px;
}

.dummy-display.horizontal {
  min-height: 200px;
}

/* Dummy Header */
.dummy-header {
  text-align: center;
  margin-bottom: 16px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding-bottom: 12px;
}

.dummy-name {
  font-size: 18px;
  font-weight: 700;
  color: #ffd700;
  margin-bottom: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.declarer-badge {
  background: linear-gradient(135deg, #ffd700, #ffed4e);
  color: #333;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
}

.dummy-status {
  font-size: 12px;
  color: #cccccc;
  font-style: italic;
}

/* Suits Container */
.suits-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 16px;
}

.dummy-display.horizontal .suits-container {
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: space-between;
}

/* Suit Section */
.suit-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.dummy-display.horizontal .suit-section {
  flex: 1;
  min-width: 120px;
}

.suit-header {
  display: flex;
  align-items: center;
  gap: 6px;
  font-weight: 600;
  font-size: 14px;
}

.suit-symbol {
  font-size: 18px;
  font-weight: bold;
}

.suit-count {
  color: #cccccc;
  font-size: 12px;
}

/* Suit Cards */
.suit-cards {
  display: flex;
  flex-wrap: wrap;
  gap: 2px;
  position: relative;
}

.dummy-display.vertical .suit-cards {
  flex-direction: row;
}

.dummy-display.horizontal .suit-cards {
  flex-direction: column;
  align-items: flex-start;
}

/* Dummy Card */
.dummy-card {
  position: relative;
  transition: all 0.2s ease;
  cursor: default;
  border-radius: 4px;
}

.dummy-card.selectable {
  cursor: pointer;
}

.dummy-card.selectable:hover {
  transform: translateY(-4px) scale(1.05);
  z-index: 1000 !important;
}

.dummy-card.selected {
  transform: translateY(-6px) scale(1.1);
  z-index: 999 !important;
}

.dummy-card.invalid {
  opacity: 0.5;
  cursor: not-allowed;
  filter: grayscale(50%);
}

.dummy-card.invalid:hover {
  transform: none;
}

/* Selection Highlight */
.selection-highlight {
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border: 2px solid #28a745;
  border-radius: 6px;
  background: rgba(40, 167, 69, 0.2);
  animation: selectionPulse 1s infinite;
}

@keyframes selectionPulse {
  0%, 100% {
    opacity: 0.6;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.02);
  }
}

/* Hidden State */
.dummy-display.hidden {
  justify-content: center;
  align-items: center;
  min-height: 120px;
  text-align: center;
}

.concealed-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  margin-top: 12px;
}

.card-back-dummy {
  width: 50px;
  height: 70px;
  background: linear-gradient(135deg, #1a365d, #2d5a87);
  border: 2px solid #4a9eff;
  border-radius: 6px;
  position: relative;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.card-back-dummy::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 30px;
  height: 30px;
  background: repeating-linear-gradient(
    45deg,
    #4a9eff,
    #4a9eff 2px,
    transparent 2px,
    transparent 6px
  );
  border-radius: 3px;
  opacity: 0.6;
}

/* Dummy Summary */
.dummy-summary {
  text-align: center;
  font-size: 12px;
  color: #cccccc;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 12px;
}

.total-cards {
  font-weight: 500;
  margin-bottom: 4px;
}

.valid-cards {
  color: #28a745;
  font-weight: 600;
}

/* Size Variations */
.dummy-display.small {
  padding: 12px;
  min-width: 240px;
  max-width: 320px;
}

.dummy-display.small .dummy-name {
  font-size: 16px;
}

.dummy-display.small .suit-symbol {
  font-size: 16px;
}

.dummy-display.large {
  padding: 20px;
  min-width: 320px;
  max-width: 480px;
}

.dummy-display.large .dummy-name {
  font-size: 20px;
}

.dummy-display.large .suit-symbol {
  font-size: 20px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .dummy-display {
    min-width: 200px;
    max-width: 280px;
    padding: 12px;
  }
  
  .dummy-display.horizontal .suits-container {
    flex-direction: column;
  }
  
  .suit-cards {
    gap: 1px;
  }
  
  .dummy-card.selectable:hover {
    transform: translateY(-2px) scale(1.02);
  }
  
  .dummy-card.selected {
    transform: translateY(-3px) scale(1.05);
  }
  
  .card-back-dummy {
    width: 40px;
    height: 56px;
  }
  
  .card-back-dummy::before {
    width: 24px;
    height: 24px;
  }
}

@media (max-width: 480px) {
  .dummy-display {
    min-width: 160px;
    max-width: 220px;
    padding: 8px;
  }
  
  .dummy-name {
    font-size: 14px;
  }
  
  .suit-symbol {
    font-size: 14px;
  }
  
  .suit-header {
    font-size: 12px;
  }
  
  .dummy-summary {
    font-size: 10px;
  }
}

/* Accessibility */
.dummy-card:focus {
  outline: 2px solid #4a9eff;
  outline-offset: 2px;
}

.dummy-card.selectable:focus:hover {
  outline-color: #28a745;
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .dummy-card,
  .selection-highlight {
    transition: none;
    animation: none;
  }
  
  .dummy-card.selectable:hover {
    transform: none;
  }
  
  .dummy-card.selected {
    border: 2px solid #28a745;
    transform: none;
  }
}

/* Print styles */
@media print {
  .dummy-display {
    break-inside: avoid;
    background: white;
    color: black;
    border: 1px solid #ccc;
  }
  
  .selection-highlight {
    display: none;
  }
  
  .dummy-card {
    position: static !important;
    transform: none !important;
    display: inline-block;
    margin: 1px;
  }
  
  .suit-symbol {
    color: black !important;
  }
  
  .suit-section.hearts .suit-symbol,
  .suit-section.diamonds .suit-symbol {
    color: #dc3545 !important;
  }
}

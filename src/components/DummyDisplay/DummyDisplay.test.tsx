/**
 * Unit tests for DummyDisplay component
 */

import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import DummyDisplay from './DummyDisplay';
import { Card, Suit, Rank } from '../../types/bridge';

// Mock the card utils
jest.mock('../../lib/bridge/cardUtils', () => ({
  groupCardsBySuit: jest.fn((cards) => {
    const groups = {
      [Suit.SPADES]: [],
      [Suit.HEARTS]: [],
      [Suit.DIAMONDS]: [],
      [Suit.CLUBS]: []
    };
    cards.forEach((card: Card) => {
      groups[card.suit].push(card);
    });
    return groups;
  }),
  sortCards: jest.fn((cards) => [...cards]),
}));

// Mock the Card component
jest.mock('../Card/Card', () => {
  return function MockCard({ card, size, isPlayable }: any) {
    return (
      <div 
        className={`mock-card ${size} ${isPlayable ? 'playable' : 'not-playable'}`}
        data-testid={`card-${card.rank}-${card.suit}`}
      >
        {card.rank}{card.suit}
      </div>
    );
  };
});

describe('DummyDisplay', () => {
  const mockCards: Card[] = [
    { suit: Suit.SPADES, rank: Rank.ACE },
    { suit: Suit.SPADES, rank: Rank.KING },
    { suit: Suit.HEARTS, rank: Rank.QUEEN },
    { suit: Suit.DIAMONDS, rank: Rank.JACK },
    { suit: Suit.CLUBS, rank: Rank.TEN }
  ];

  const mockOnCardSelect = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders without crashing', () => {
    render(<DummyDisplay cards={mockCards} />);
    expect(screen.getByTestId('dummy-display')).toBeInTheDocument();
  });

  it('displays dummy header with default name', () => {
    render(<DummyDisplay cards={mockCards} />);
    expect(screen.getByText('Dummy')).toBeInTheDocument();
    expect(screen.getByText("Dummy's hand")).toBeInTheDocument();
  });

  it('displays custom player name', () => {
    render(<DummyDisplay cards={mockCards} playerName="North" />);
    expect(screen.getByText('North')).toBeInTheDocument();
  });

  it('shows declarer badge when isDeclarer is true', () => {
    render(<DummyDisplay cards={mockCards} isDeclarer={true} />);
    expect(screen.getByText('Declarer')).toBeInTheDocument();
  });

  it('displays cards organized by suit', () => {
    render(<DummyDisplay cards={mockCards} />);
    
    // Check for suit symbols
    expect(screen.getByText('♠')).toBeInTheDocument();
    expect(screen.getByText('♥')).toBeInTheDocument();
    expect(screen.getByText('♦')).toBeInTheDocument();
    expect(screen.getByText('♣')).toBeInTheDocument();
    
    // Check for cards
    expect(screen.getByTestId('card-ACE-SPADES')).toBeInTheDocument();
    expect(screen.getByTestId('card-KING-SPADES')).toBeInTheDocument();
    expect(screen.getByTestId('card-QUEEN-HEARTS')).toBeInTheDocument();
    expect(screen.getByTestId('card-JACK-DIAMONDS')).toBeInTheDocument();
    expect(screen.getByTestId('card-TEN-CLUBS')).toBeInTheDocument();
  });

  it('shows suit counts', () => {
    render(<DummyDisplay cards={mockCards} />);
    
    expect(screen.getByText('(2)')).toBeInTheDocument(); // Spades count
    expect(screen.getByText('(1)')).toBeInTheDocument(); // Hearts count
  });

  it('displays total card count', () => {
    render(<DummyDisplay cards={mockCards} />);
    expect(screen.getByText('5 cards remaining')).toBeInTheDocument();
  });

  it('handles card selection when selectable', () => {
    render(
      <DummyDisplay
        cards={mockCards}
        isSelectable={true}
        validCards={[mockCards[0]]}
        onCardSelect={mockOnCardSelect}
      />
    );
    
    const card = screen.getByTestId('card-ACE-SPADES');
    fireEvent.click(card);
    
    expect(mockOnCardSelect).toHaveBeenCalledWith(mockCards[0]);
  });

  it('shows selection prompt when selectable', () => {
    render(
      <DummyDisplay
        cards={mockCards}
        isSelectable={true}
      />
    );
    
    expect(screen.getByText('Select a card to play')).toBeInTheDocument();
  });

  it('shows valid card count when selectable', () => {
    const validCards = [mockCards[0], mockCards[1]];
    render(
      <DummyDisplay
        cards={mockCards}
        isSelectable={true}
        validCards={validCards}
      />
    );
    
    expect(screen.getByText('2 playable')).toBeInTheDocument();
  });

  it('does not allow selection of invalid cards', () => {
    render(
      <DummyDisplay
        cards={mockCards}
        isSelectable={true}
        validCards={[mockCards[0]]} // Only first card is valid
        onCardSelect={mockOnCardSelect}
      />
    );
    
    // Try to click invalid card
    const invalidCard = screen.getByTestId('card-QUEEN-HEARTS');
    fireEvent.click(invalidCard);
    
    expect(mockOnCardSelect).not.toHaveBeenCalled();
  });

  it('highlights selected card', () => {
    render(
      <DummyDisplay
        cards={mockCards}
        selectedCard={mockCards[0]}
      />
    );
    
    // The selected card should have selection styling
    // This would be tested through CSS classes in a real implementation
    expect(screen.getByTestId('card-ACE-SPADES')).toBeInTheDocument();
  });

  it('renders hidden when not visible', () => {
    render(<DummyDisplay cards={mockCards} isVisible={false} />);
    
    expect(screen.getByText('Hand concealed')).toBeInTheDocument();
    expect(screen.getByText('5 cards')).toBeInTheDocument();
    expect(screen.queryByTestId('card-ACE-SPADES')).not.toBeInTheDocument();
  });

  it('applies correct size class', () => {
    render(<DummyDisplay cards={mockCards} size="large" />);
    expect(screen.getByTestId('dummy-display')).toHaveClass('large');
  });

  it('applies correct layout class', () => {
    render(<DummyDisplay cards={mockCards} layout="vertical" />);
    expect(screen.getByTestId('dummy-display')).toHaveClass('vertical');
  });

  it('handles empty card array', () => {
    render(<DummyDisplay cards={[]} />);
    expect(screen.getByText('0 cards remaining')).toBeInTheDocument();
  });

  it('applies custom className', () => {
    render(<DummyDisplay cards={mockCards} className="custom-class" />);
    expect(screen.getByTestId('dummy-display')).toHaveClass('custom-class');
  });

  it('does not show suits with no cards', () => {
    const spadesOnly = [{ suit: Suit.SPADES, rank: Rank.ACE }];
    render(<DummyDisplay cards={spadesOnly} />);
    
    expect(screen.getByText('♠')).toBeInTheDocument();
    // Hearts, Diamonds, Clubs should not appear since they have no cards
    expect(screen.queryByText('♥')).not.toBeInTheDocument();
    expect(screen.queryByText('♦')).not.toBeInTheDocument();
    expect(screen.queryByText('♣')).not.toBeInTheDocument();
  });
});

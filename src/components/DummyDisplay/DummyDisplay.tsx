/**
 * DummyDisplay component for showing the dummy's exposed hand during play
 * Displays cards organized by suit in a traditional bridge layout
 */

import React, { useState, useEffect } from 'react';
import { Card, Suit, Rank } from '../../types/bridge';
import { sortCards, groupCardsBySuit } from '../../lib/bridge/cardUtils';
import CardComponent from '../Card/Card';
import './DummyDisplay.css';

interface DummyDisplayProps {
  cards: Card[];
  isVisible?: boolean;
  isSelectable?: boolean;
  selectedCard?: Card | null;
  validCards?: Card[];
  onCardSelect?: (card: Card) => void;
  playerName?: string;
  isDeclarer?: boolean;
  size?: 'small' | 'medium' | 'large';
  layout?: 'horizontal' | 'vertical';
  className?: string;
}

const DummyDisplay: React.FC<DummyDisplayProps> = ({
  cards,
  isVisible = true,
  isSelectable = false,
  selectedCard = null,
  validCards = [],
  onCardSelect,
  playerName = 'Dummy',
  isDeclarer = false,
  size = 'small',
  layout = 'horizontal',
  className = ''
}) => {
  const [suitGroups, setSuitGroups] = useState<Record<Suit, Card[]>>({
    [Suit.SPADES]: [],
    [Suit.HEARTS]: [],
    [Suit.DIAMONDS]: [],
    [Suit.CLUBS]: []
  });

  // Group and sort cards by suit
  useEffect(() => {
    const grouped = groupCardsBySuit(cards);
    
    // Sort cards within each suit
    const sortedGroups = Object.entries(grouped).reduce((acc, [suit, suitCards]) => {
      acc[suit as Suit] = sortCards(suitCards, 'rank');
      return acc;
    }, {} as Record<Suit, Card[]>);
    
    setSuitGroups(sortedGroups);
  }, [cards]);

  // Check if a card is valid for selection
  const isCardValid = (card: Card): boolean => {
    if (!isSelectable) return false;
    if (validCards.length === 0) return true;
    return validCards.some(c => c.rank === card.rank && c.suit === card.suit);
  };

  // Check if a card is selected
  const isCardSelected = (card: Card): boolean => {
    return selectedCard?.rank === card.rank && selectedCard?.suit === card.suit;
  };

  // Handle card click
  const handleCardClick = (card: Card) => {
    if (!isSelectable || !isCardValid(card)) return;
    onCardSelect?.(card);
  };

  // Get suit symbol for display
  const getSuitSymbol = (suit: Suit): string => {
    switch (suit) {
      case Suit.SPADES: return '♠';
      case Suit.HEARTS: return '♥';
      case Suit.DIAMONDS: return '♦';
      case Suit.CLUBS: return '♣';
      default: return '';
    }
  };

  // Get suit color
  const getSuitColor = (suit: Suit): string => {
    return suit === Suit.HEARTS || suit === Suit.DIAMONDS ? '#dc3545' : '#000000';
  };

  // Render a suit section
  const renderSuitSection = (suit: Suit) => {
    const suitCards = suitGroups[suit];
    if (suitCards.length === 0) return null;

    return (
      <div key={suit} className={`suit-section ${suit.toLowerCase()}`}>
        <div className="suit-header">
          <span 
            className="suit-symbol" 
            style={{ color: getSuitColor(suit) }}
          >
            {getSuitSymbol(suit)}
          </span>
          <span className="suit-count">({suitCards.length})</span>
        </div>
        
        <div className="suit-cards">
          {suitCards.map((card, index) => {
            const valid = isCardValid(card);
            const selected = isCardSelected(card);
            
            return (
              <div
                key={`${card.suit}-${card.rank}-${index}`}
                className={`dummy-card ${valid ? 'valid' : 'invalid'} ${
                  selected ? 'selected' : ''
                } ${isSelectable ? 'selectable' : ''}`}
                onClick={() => handleCardClick(card)}
                style={{
                  marginLeft: index > 0 ? '-12px' : '0',
                  zIndex: suitCards.length - index
                }}
              >
                <CardComponent
                  card={card}
                  size={size}
                  isPlayable={valid}
                />
                
                {selected && (
                  <div className="selection-highlight" />
                )}
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  if (!isVisible) {
    return (
      <div className={`dummy-display hidden ${className}`}>
        <div className="dummy-header">
          <div className="dummy-name">{playerName}</div>
          <div className="dummy-status">Hand concealed</div>
        </div>
        <div className="concealed-indicator">
          <div className="card-back-dummy" />
          <div className="card-count">{cards.length} cards</div>
        </div>
      </div>
    );
  }

  const suitOrder = [Suit.SPADES, Suit.HEARTS, Suit.DIAMONDS, Suit.CLUBS];

  return (
    <div 
      className={`dummy-display ${layout} ${size} ${className}`}
      data-testid="dummy-display"
    >
      {/* Dummy Header */}
      <div className="dummy-header">
        <div className="dummy-name">
          {playerName}
          {isDeclarer && <span className="declarer-badge">Declarer</span>}
        </div>
        <div className="dummy-status">
          {isSelectable ? 'Select a card to play' : 'Dummy\'s hand'}
        </div>
      </div>

      {/* Suits Display */}
      <div className="suits-container">
        {suitOrder.map(suit => renderSuitSection(suit))}
      </div>

      {/* Summary */}
      <div className="dummy-summary">
        <div className="total-cards">{cards.length} cards remaining</div>
        {isSelectable && validCards.length > 0 && (
          <div className="valid-cards">{validCards.length} playable</div>
        )}
      </div>
    </div>
  );
};

export default DummyDisplay;

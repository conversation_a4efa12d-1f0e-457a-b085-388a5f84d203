/**
 * Enhanced Bridge Coach Component
 * Provides expert coaching advice and move execution for Bridge players
 */

import React, { useState, useEffect } from 'react';
import { Position, GameState, PlayerType, GamePhase, Card, BidValue, SpecialBid, Bid } from '../../types/bridge';
import './SimpleCoach.css';

interface SimpleCoachProps {
  gameState: GameState;
  humanPosition: Position;
  onGameStateChange?: (newState: GameState) => void;
}

const SimpleCoach: React.FC<SimpleCoachProps> = ({
  gameState,
  humanPosition,
  onGameStateChange
}) => {
  const [isVisible, setIsVisible] = useState(true);
  const [advice, setAdvice] = useState<string>('');
  const [speechEnabled, setSpeechEnabled] = useState(false);
  const [expertMove, setExpertMove] = useState<{bid?: BidValue, card?: Card, reasoning: string} | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [debugInfo, setDebugInfo] = useState<string | null>(null);

  // Generate simple advice based on game state
  useEffect(() => {
    console.log('🎯 SimpleCoach useEffect triggered:', {
      currentPlayer: gameState.currentPlayer,
      humanPosition,
      isMyTurn: gameState.currentPlayer === humanPosition,
      phase: gameState.phase
    });

    if (gameState.currentPlayer === humanPosition) {
      const newAdvice = generateAdvice(gameState, humanPosition);
      setAdvice(newAdvice);

      // Speak advice if enabled
      if (speechEnabled && newAdvice) {
        speakAdvice(newAdvice);
      }

      // Generate expert move recommendation
      generateExpertMove(gameState, humanPosition);
    }
  }, [gameState, humanPosition, speechEnabled]);

  // Helper function to convert suit letters to symbols
  const getSuitSymbol = (suit: string): string => {
    switch (suit) {
      case 'S': return '♠';
      case 'H': return '♥';
      case 'D': return '♦';
      case 'C': return '♣';
      case 'NT': return 'NT';
      default: return suit;
    }
  };

  // Helper function to get suit color class
  const getSuitColorClass = (suit: string): string => {
    return (suit === 'H' || suit === 'D') ? 'red-suit' : 'black-suit';
  };

  const generateAdvice = (state: GameState, position: Position): string => {
    const hand = state.players[position].hand;
    
    if (state.phase === GamePhase.BIDDING) {
      // Count high card points (simplified)
      let hcp = 0;
      hand.forEach(card => {
        if (card.rank === 'A') hcp += 4;
        else if (card.rank === 'K') hcp += 3;
        else if (card.rank === 'Q') hcp += 2;
        else if (card.rank === 'J') hcp += 1;
      });
      
      if (hand.length === 0) {
        return "Waiting for cards to be dealt...";
      } else if (hcp < 6) {
        return `You have ${hcp} high card points. With fewer than 6 points, it's usually best to pass.`;
      } else if (hcp >= 12) {
        // Count cards in each suit for distribution analysis
        const suitCounts = { S: 0, H: 0, D: 0, C: 0 };
        hand.forEach(card => {
          suitCounts[card.suit as keyof typeof suitCounts]++;
        });
        const longestSuit = Object.entries(suitCounts).reduce((a, b) => a[1] > b[1] ? a : b);
        const suitName = longestSuit[0] === 'S' ? 'Spades' : longestSuit[0] === 'H' ? 'Hearts' :
                        longestSuit[0] === 'D' ? 'Diamonds' : 'Clubs';
        return `You have ${hcp} high card points. With 12+ points, consider opening. Your longest suit is ${suitName} with ${longestSuit[1]} cards.`;
      } else {
        return `You have ${hcp} high card points. Look for opportunities to respond to partner's bids.`;
      }
    } else if (state.phase === GamePhase.PLAYING) {
      const currentTrick = state.currentTrick;
      
      if (currentTrick.length === 0) {
        return "You're leading this trick. Consider leading from your strongest suit or a safe card.";
      } else {
        const leadSuit = currentTrick[0].card.suit;
        const hasLeadSuit = hand.some(card => card.suit === leadSuit);
        
        if (hasLeadSuit) {
          const suitSymbol = leadSuit === 'S' ? '♠' : leadSuit === 'H' ? '♥' : leadSuit === 'D' ? '♦' : '♣';
          return `You must follow suit (${suitSymbol}). Try to play your highest card if you can win the trick.`;
        } else {
          const contract = state.contract;
          if (contract && contract.suit !== 'NT') {
            const hasTrump = hand.some(card => card.suit === (contract.suit as any));
            if (hasTrump) {
              const trumpSymbol = contract.suit === 'S' ? '♠' : contract.suit === 'H' ? '♥' :
                                 contract.suit === 'D' ? '♦' : '♣';
              return `You can't follow suit. Consider trumping with your ${trumpSymbol} to win the trick.`;
            }
          }
          return "You can't follow suit. Discard a card you don't need.";
        }
      }
    }
    
    return "Analyze your hand and think about your strategy.";
  };

  // Generate expert move recommendation using AI logic
  const generateExpertMove = async (state: GameState, position: Position) => {
    setIsAnalyzing(true);
    try {
      const { AIPlayer } = await import("../../lib/bridge/aiPlayer");
      const aiPlayer = new AIPlayer(position);
      const hand = state.players[position].hand;

      // Debug: Always log the basic info first
      console.log('🎯 COACH ANALYSIS START:', {
        position,
        phase: state.phase,
        handSize: hand.length,
        currentPlayer: state.currentPlayer,
        isMyTurn: state.currentPlayer === position
      });
      
      if (state.phase === GamePhase.BIDDING) {
        const expertBid = aiPlayer.makeBid(hand, state.auction);
        let reasoning = "";

        // Debug: Log the hand being analyzed
        console.log('🔍 BIDDING ANALYSIS DEBUG:', {
          position,
          hand: hand.map(c => `${c.rank}${getSuitSymbol(c.suit)}`),
          expertBid,
          auction: state.auction.map(bid => `${bid.player}: ${typeof bid.value === 'string' ? bid.value : `${bid.value.level}${getSuitSymbol(bid.value.suit)}`}`)
        });

        if (expertBid === SpecialBid.PASS) {
          reasoning = "Expert recommends PASS. Your hand lacks the strength or distribution for a bid.";
        } else if (typeof expertBid === "object" && expertBid.level && expertBid.suit) {
          // Validate that the bid makes sense for the hand
          const { countCardsBySuit } = await import("../../lib/bridge/cardUtils");
          const suitCounts = countCardsBySuit(hand);
          const bidSuit = expertBid.suit;

          // Convert bid suit to card suit for validation
          let cardSuit: string | null = null;
          if (bidSuit === 'S') cardSuit = 'SPADES';
          else if (bidSuit === 'H') cardSuit = 'HEARTS';
          else if (bidSuit === 'D') cardSuit = 'DIAMONDS';
          else if (bidSuit === 'C') cardSuit = 'CLUBS';

          if (cardSuit && suitCounts[cardSuit as keyof typeof suitCounts] === 0) {
            const debugMessage = `🚨 INVALID BID RECOMMENDATION: Expert suggested ${expertBid.level}${getSuitSymbol(expertBid.suit)} but you have 0 cards in ${getSuitSymbol(expertBid.suit)}! Your hand: ${hand.map(c => `${c.rank}${getSuitSymbol(c.suit)}`).join(', ')}`;
            console.error('Expert recommended bid for suit not in hand:', {
              recommendedBid: `${expertBid.level}${expertBid.suit}`,
              playerHand: hand.map(c => `${c.rank}${c.suit}`),
              suitCounts,
              position
            });
            setDebugInfo(debugMessage);
            setExpertMove({ reasoning: "Unable to analyze bidding position. The recommended bid doesn't match your hand." });
            return;
          }

          reasoning = `Expert recommends ${expertBid.level} ${getSuitSymbol(expertBid.suit)}. This bid shows your hand strength and distribution optimally.`;
        }

        setExpertMove({ bid: expertBid, reasoning });
      } else if (state.phase === GamePhase.PLAYING) {
        const expertCard = aiPlayer.playCard(state);
        const hand = state.players[position].hand;

        // Verify the recommended card is actually in the player's hand
        const cardInHand = hand.some(card =>
          card.rank === expertCard.rank && card.suit === expertCard.suit
        );

        if (!cardInHand) {
          const debugMessage = `🚨 INVALID RECOMMENDATION: Expert suggested ${expertCard.rank}${getSuitSymbol(expertCard.suit)} but it's not in your hand! Your cards: ${hand.map(c => `${c.rank}${getSuitSymbol(c.suit)}`).join(', ')}`;
          console.error('Expert recommended card not in hand:', {
            recommendedCard: `${expertCard.rank}${expertCard.suit}`,
            playerHand: hand.map(c => `${c.rank}${c.suit}`),
            position
          });
          setDebugInfo(debugMessage);
          setExpertMove({ reasoning: "Unable to analyze position. The recommended card is not available." });
          return;
        } else {
          // Clear debug info if recommendation is valid
          setDebugInfo(null);
        }

        // Check if the recommended card is actually playable according to Bridge rules
        const { getValidCardsForPlayer } = await import("../../lib/bridge/gameStateUtils");
        const validCards = getValidCardsForPlayer(state, position);
        const isRecommendedCardValid = validCards.some(vc =>
          vc.suit === expertCard.suit && vc.rank === expertCard.rank
        );

        console.log('🎯 COACH RECOMMENDATION DEBUG:', {
          recommendedCard: `${expertCard.rank}${expertCard.suit}`,
          isValid: isRecommendedCardValid,
          validCards: validCards.map(c => `${c.rank}${c.suit}`),
          currentTrick: state.currentTrick.map(pc => `${pc.card.rank}${pc.card.suit} (${pc.player})`),
          position
        });

        const reasoning = `Expert recommends playing ${expertCard.rank}${getSuitSymbol(expertCard.suit)}. This is the optimal card based on bridge strategy.${!isRecommendedCardValid ? ' ⚠️ WARNING: This card may not be playable due to Bridge rules!' : ''}`;

        setExpertMove({ card: expertCard, reasoning });
      }
    } catch (error) {
      console.error("Expert analysis error:", error);
      setExpertMove({ reasoning: "Unable to analyze position. Please make your best judgment." });
    } finally {
      setIsAnalyzing(false);
    }
  };

  // Execute the expert move on behalf of the player
  const executeExpertMove = async () => {
    if (!expertMove || !onGameStateChange) return;

    try {
      if (expertMove.bid && gameState.phase === GamePhase.BIDDING) {
        const { processBid } = await import("../../lib/bridge/gameStateUtils");
        const bid: Bid = {
          player: humanPosition,
          value: expertMove.bid,
          timestamp: new Date()
        };
        const newGameState = processBid(gameState, bid);
        onGameStateChange(newGameState);

        // Speak the move if enabled
        if (speechEnabled) {
          const bidText = expertMove.bid === SpecialBid.PASS ? "Pass" :
            typeof expertMove.bid === "object" && expertMove.bid.level && expertMove.bid.suit ?
            `${expertMove.bid.level} ${expertMove.bid.suit}` : "Pass";
          speakAdvice(`I bid ${bidText} for you.`);
        }
      } else if (expertMove.card && gameState.phase === GamePhase.PLAYING) {
        const { playCard } = await import("../../lib/bridge/gameStateUtils");
        const newGameState = playCard(gameState, expertMove.card, humanPosition);
        onGameStateChange(newGameState);

        // Speak the move if enabled
        if (speechEnabled) {
          const cardSuit = expertMove.card.suit === 'S' ? 'Spades' :
                          expertMove.card.suit === 'H' ? 'Hearts' :
                          expertMove.card.suit === 'D' ? 'Diamonds' : 'Clubs';
          speakAdvice(`I played the ${expertMove.card.rank} of ${cardSuit} for you.`);
        }
      }

      // Clear the expert move after execution
      setExpertMove(null);
    } catch (error) {
      console.error("Error executing expert move:", error);
      if (speechEnabled) {
        speakAdvice("Sorry, I couldn't execute that move. Please try manually.");
      }
    }
  };

  // Refresh the expert analysis
  const refreshAnalysis = () => {
    if (gameState.currentPlayer === humanPosition) {
      generateExpertMove(gameState, humanPosition);
      const newAdvice = generateAdvice(gameState, humanPosition);
      setAdvice(newAdvice);

      if (speechEnabled) {
        speakAdvice("Analysis refreshed. " + newAdvice);
      }
    }
  };

  const speakAdvice = (text: string) => {
    if ('speechSynthesis' in window) {
      const utterance = new SpeechSynthesisUtterance(text);
      utterance.rate = 0.8;
      utterance.pitch = 1;
      utterance.volume = 0.7;
      speechSynthesis.speak(utterance);
    }
  };

  const getHandAnalysis = () => {
    const hand = gameState.players[humanPosition].hand;
    const suits: Record<string, number> = { '♠': 0, '♥': 0, '♦': 0, '♣': 0 };
    let hcp = 0;

    hand.forEach(card => {
      // Map suit codes to Unicode symbols
      const suitSymbol = card.suit === 'S' ? '♠' :
                        card.suit === 'H' ? '♥' :
                        card.suit === 'D' ? '♦' : '♣';
      suits[suitSymbol]++;

      // Calculate high card points
      if (card.rank === 'A') hcp += 4;
      else if (card.rank === 'K') hcp += 3;
      else if (card.rank === 'Q') hcp += 2;
      else if (card.rank === 'J') hcp += 1;
    });

    const distribution = Object.values(suits).sort((a, b) => b - a).join('-');

    return { hcp, distribution, suits };
  };

  if (!isVisible) {
    return (
      <button 
        className="coach-toggle-btn"
        onClick={() => setIsVisible(true)}
        title="Show Bridge Coach"
      >
        🎓 Coach
      </button>
    );
  }

  const analysis = getHandAnalysis();

  return (
    <div className="simple-coach">
      <div className="coach-header">
        <h3>🎓 Bridge Coach</h3>
        <div className="coach-controls">
          <button
            className={`speech-toggle ${speechEnabled ? 'active' : ''}`}
            onClick={() => setSpeechEnabled(!speechEnabled)}
            title="Toggle voice coaching"
          >
            {speechEnabled ? '🔊' : '🔇'}
          </button>
          <button
            className="coach-close"
            onClick={() => setIsVisible(false)}
            title="Hide coach"
          >
            ✕
          </button>
        </div>
      </div>

      <div className="coach-content">
        {gameState.currentPlayer === humanPosition ? (
          <div className="your-turn">
            <h4>Your Turn!</h4>
            <div className="advice-box">
              <p>{advice}</p>
            </div>
            
            {/* Expert Move Recommendation */}
            <div className="expert-move-section">
              <h5>🎯 Expert Recommendation:</h5>
              {isAnalyzing ? (
                <div className="analyzing">
                  <span className="spinner">⏳</span> Analyzing position...
                </div>
              ) : expertMove ? (
                <div className="expert-move">
                  <div className="move-recommendation">
                    {expertMove.bid && (
                      <div className="recommended-bid">
                        <strong>Recommended Bid: </strong>
                        <span className={`bid-value ${
                          expertMove.bid !== SpecialBid.PASS &&
                          typeof expertMove.bid === "object" &&
                          expertMove.bid.suit ?
                          getSuitColorClass(expertMove.bid.suit) : ''
                        }`}>
                          {expertMove.bid === SpecialBid.PASS ? "PASS" :
                           typeof expertMove.bid === "object" && expertMove.bid.level && expertMove.bid.suit ?
                           `${expertMove.bid.level} ${getSuitSymbol(expertMove.bid.suit)}` : "PASS"}
                        </span>
                      </div>
                    )}
                    {expertMove.card && (
                      <div className="recommended-card">
                        <strong>Recommended Play: </strong>
                        <span className={`card-value ${(expertMove.card.suit === 'H' || expertMove.card.suit === 'D') ? 'red-suit' : 'black-suit'}`}>
                          {expertMove.card.rank}{expertMove.card.suit === 'S' ? '♠' :
                                                  expertMove.card.suit === 'H' ? '♥' :
                                                  expertMove.card.suit === 'D' ? '♦' : '♣'}
                        </span>
                      </div>
                    )}
                  </div>
                  <div className="move-reasoning">
                    <p>{expertMove.reasoning}</p>
                  </div>
                  <div className="expert-actions">
                    <button 
                      className="execute-move-btn"
                      onClick={executeExpertMove}
                      disabled={!onGameStateChange}
                      title="Let the coach make this move for you"
                    >
                      🤖 Execute Move
                    </button>
                    <button
                      className="refresh-analysis-btn"
                      onClick={refreshAnalysis}
                      title="Re-analyze position"
                    >
                      🔄 Re-analyze
                    </button>
                  </div>
                </div>
              ) : (
                <button
                  className="get-expert-advice-btn"
                  onClick={refreshAnalysis}
                  disabled={isAnalyzing}
                >
                  🧠 Get Expert Advice
                </button>
              )}
            </div>
            
            <div className="hand-analysis">
              <h5>Your Hand Analysis:</h5>
              <div className="analysis-stats">
                <div className="stat">
                  <span>High Card Points:</span>
                  <span className="value">{analysis.hcp}</span>
                </div>
                <div className="stat">
                  <span>Distribution:</span>
                  <span className="value">{analysis.distribution}</span>
                </div>
              </div>
              
              <div className="suit-breakdown">
                <div className="suit">♠: {analysis.suits['♠']}</div>
                <div className="suit">♥: {analysis.suits['♥']}</div>
                <div className="suit">♦: {analysis.suits['♦']}</div>
                <div className="suit">♣: {analysis.suits['♣']}</div>
              </div>
            </div>
          </div>
        ) : (
          <div className="waiting">
            <h4>Waiting for your turn...</h4>
            <p>Current player: {gameState.currentPlayer}</p>
            <p>Phase: {gameState.phase}</p>
            
            <div className="hand-analysis">
              <h5>Your Hand Analysis:</h5>
              <div className="analysis-stats">
                <div className="stat">
                  <span>High Card Points:</span>
                  <span className="value">{analysis.hcp}</span>
                </div>
                <div className="stat">
                  <span>Distribution:</span>
                  <span className="value">{analysis.distribution}</span>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Temporary Debug Panel */}
        {debugInfo && (
          <div className="debug-panel" style={{
            position: 'fixed',
            top: '10px',
            right: '10px',
            background: '#ff4444',
            color: 'white',
            padding: '10px',
            borderRadius: '5px',
            maxWidth: '300px',
            fontSize: '12px',
            zIndex: 9999,
            boxShadow: '0 2px 10px rgba(0,0,0,0.3)'
          }}>
            <div style={{ fontWeight: 'bold', marginBottom: '5px' }}>
              DEBUG: Invalid Card Recommendation
            </div>
            <div>{debugInfo}</div>
            <button
              onClick={() => setDebugInfo(null)}
              style={{
                marginTop: '5px',
                background: 'rgba(255,255,255,0.2)',
                border: 'none',
                color: 'white',
                padding: '2px 6px',
                borderRadius: '3px',
                cursor: 'pointer',
                fontSize: '10px'
              }}
            >
              ✕ Close
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default SimpleCoach;

/**
 * Simple Bridge Coach Component Styles
 * Clean, Apple-inspired design for basic coaching
 */

/* Coach Toggle Button */
.coach-toggle-btn {
  position: relative;
  background: linear-gradient(145deg, #007aff 0%, #0051d5 100%);
  color: white;
  border: none;
  border-radius: 12px;
  padding: 12px 16px;
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', sans-serif;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  box-shadow:
    0 4px 12px rgba(0, 122, 255, 0.3),
    0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
  z-index: 1000;
  user-select: none;
  width: 100%;
}

.coach-toggle-btn:hover {
  transform: translateY(-2px);
  box-shadow: 
    0 6px 16px rgba(0, 122, 255, 0.4),
    0 3px 6px rgba(0, 0, 0, 0.15);
}

.coach-toggle-btn:active {
  transform: translateY(-1px);
}

/* Main Coach Panel */
.simple-coach {
  position: relative;
  width: 100%;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 16px;
  z-index: 1000;
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.15),
    0 8px 16px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', sans-serif;
}

/* Coach Header */
.coach-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.coach-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1d1d1f;
}

.coach-controls {
  display: flex;
  gap: 8px;
}

.speech-toggle,
.coach-close {
  background: none;
  border: none;
  font-size: 16px;
  cursor: pointer;
  padding: 6px;
  border-radius: 8px;
  transition: background-color 0.2s ease;
}

.speech-toggle:hover,
.coach-close:hover {
  background: rgba(0, 0, 0, 0.05);
}

.speech-toggle.active {
  background: rgba(0, 122, 255, 0.1);
  color: #007aff;
}

/* Coach Content */
.coach-content {
  padding: 12px 16px;
  max-height: 350px;
  overflow-y: auto;
}

/* Your Turn Section */
.your-turn h4,
.waiting h4 {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1d1d1f;
}

.advice-box {
  background: linear-gradient(145deg, #007aff 0%, #0051d5 100%);
  color: white;
  padding: 12px;
  border-radius: 10px;
  margin-bottom: 12px;
  box-shadow: 0 4px 12px rgba(0, 122, 255, 0.3);
}

.advice-box p {
  margin: 0;
  font-size: 13px;
  line-height: 1.4;
  font-weight: 500;
}

/* Hand Analysis */
.hand-analysis {
  background: #f8f9fa;
  border-radius: 10px;
  padding: 12px;
  margin-bottom: 12px;
}

.hand-analysis h5 {
  margin: 0 0 8px 0;
  font-size: 13px;
  font-weight: 600;
  color: #1d1d1f;
}

.analysis-stats {
  margin-bottom: 12px;
}

.stat {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 0;
  font-size: 13px;
  border-bottom: 1px solid #e5e5e7;
}

.stat:last-child {
  border-bottom: none;
}

.stat span:first-child {
  color: #6e6e73;
}

.value {
  font-weight: 600;
  color: #1d1d1f;
}

.suit-breakdown {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
  margin-top: 8px;
}

.suit {
  background: white;
  padding: 8px;
  border-radius: 8px;
  text-align: center;
  font-size: 13px;
  font-weight: 600;
  color: #1d1d1f;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Learning Tips */
.learning-tips {
  background: #f0f9ff;
  border: 1px solid #bfdbfe;
  border-radius: 12px;
  padding: 16px;
}

.learning-tips h5 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
  color: #1e40af;
}

.learning-tips ul {
  margin: 0;
  padding-left: 16px;
  font-size: 12px;
  color: #1e3a8a;
}

.learning-tips li {
  margin-bottom: 4px;
  line-height: 1.4;
}

/* Waiting Section */
.waiting {
  text-align: center;
  color: #6e6e73;
}

.waiting p {
  margin: 8px 0;
  font-size: 14px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .simple-coach {
    width: calc(100vw - 40px);
    max-width: 100%;
  }
  
  .coach-content {
    padding: 16px;
  }
  
  .suit-breakdown {
    grid-template-columns: 1fr 1fr 1fr 1fr;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .simple-coach {
    background: rgba(28, 28, 30, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  .coach-header {
    background: linear-gradient(135deg, #2c2c2e 0%, #1c1c1e 100%);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  .coach-header h3 {
    color: #f2f2f7;
  }
  
  .hand-analysis {
    background: #2c2c2e;
  }
  
  .hand-analysis h5 {
    color: #f2f2f7;
  }
  
  .stat {
    border-bottom-color: rgba(255, 255, 255, 0.1);
  }
  
  .stat span:first-child {
    color: #98989d;
  }
  
  .value {
    color: #f2f2f7;
  }
  
  .suit {
    background: #1c1c1e;
    color: #f2f2f7;
  }
  
  .learning-tips {
    background: #1e3a8a;
    border-color: #3b82f6;
  }
  
  .learning-tips h5 {
    color: #93c5fd;
  }
  
  .learning-tips ul {
    color: #dbeafe;
  }
  
  .waiting {
    color: #98989d;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .simple-coach {
    background: white;
    border: 2px solid black;
  }
  
  .advice-box {
    background: #0066cc;
    border: 1px solid black;
  }
  
  .hand-analysis,
  .learning-tips {
    background: white;
    border: 1px solid black;
  }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  .coach-toggle-btn {
    transition: none;
  }
  
  .coach-toggle-btn:hover {
    transform: none;
  }
}

/* Interactive Coach Enhancements */
.interactive-coach {
  width: 400px;
  max-height: 85vh;
}

/* Coach Tabs */
.coach-tabs {
  display: flex;
  background: #f8f9fa;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.tab {
  flex: 1;
  background: none;
  border: none;
  padding: 12px 16px;
  font-family: inherit;
  font-size: 14px;
  font-weight: 500;
  color: #6e6e73;
  cursor: pointer;
  transition: all 0.2s ease;
  border-bottom: 2px solid transparent;
}

.tab:hover {
  background: rgba(0, 122, 255, 0.05);
  color: #007aff;
}

.tab.active {
  color: #007aff;
  border-bottom-color: #007aff;
  background: rgba(0, 122, 255, 0.05);
}

/* Chat Tab Styles */
.chat-tab {
  display: flex;
  flex-direction: column;
  height: calc(80vh - 120px);
}

.quick-questions {
  padding: 16px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.quick-questions h5 {
  margin: 0 0 8px 0;
  font-size: 13px;
  font-weight: 600;
  color: #1d1d1f;
}

.quick-question-buttons {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.quick-question-buttons button {
  background: linear-gradient(145deg, #f8f9fa 0%, #e9ecef 100%);
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  padding: 8px 12px;
  font-size: 12px;
  font-weight: 500;
  color: #1d1d1f;
  cursor: pointer;
  transition: all 0.2s ease;
}

.quick-question-buttons button:hover {
  background: linear-gradient(145deg, #007aff 0%, #0051d5 100%);
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 122, 255, 0.3);
}

/* Chat Messages */
.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.chat-message {
  max-width: 85%;
  word-wrap: break-word;
}

.chat-message.user {
  align-self: flex-end;
}

.chat-message.coach {
  align-self: flex-start;
}

.message-content {
  padding: 12px 16px;
  border-radius: 16px;
  font-size: 14px;
  line-height: 1.4;
}

.chat-message.user .message-content {
  background: linear-gradient(145deg, #007aff 0%, #0051d5 100%);
  color: white;
  border-bottom-right-radius: 4px;
}

.chat-message.coach .message-content {
  background: #f8f9fa;
  color: #1d1d1f;
  border: 1px solid rgba(0, 0, 0, 0.05);
  border-bottom-left-radius: 4px;
}

.message-time {
  font-size: 11px;
  color: #6e6e73;
  margin-top: 4px;
  text-align: right;
}

.chat-message.coach .message-time {
  text-align: left;
}

/* Typing Indicator */
.typing-indicator {
  display: flex;
  gap: 4px;
  align-items: center;
}

.typing-indicator span {
  width: 6px;
  height: 6px;
  background: #6e6e73;
  border-radius: 50%;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-indicator span:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-indicator span:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typing {
  0%, 60%, 100% {
    transform: translateY(0);
    opacity: 0.5;
  }
  30% {
    transform: translateY(-10px);
    opacity: 1;
  }
}

/* Chat Input */
.chat-input {
  display: flex;
  padding: 16px;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  background: #f8f9fa;
  gap: 8px;
}

.question-input {
  flex: 1;
  background: white;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 20px;
  padding: 10px 16px;
  font-family: inherit;
  font-size: 14px;
  outline: none;
  transition: border-color 0.2s ease;
}

.question-input:focus {
  border-color: #007aff;
  box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
}

.send-button {
  background: linear-gradient(145deg, #007aff 0%, #0051d5 100%);
  border: none;
  border-radius: 20px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 16px;
}

.send-button:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 122, 255, 0.3);
}

.send-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Responsive Design for Interactive Coach */
@media (max-width: 768px) {
  .interactive-coach {
    width: calc(100vw - 40px);
    max-width: 400px;
  }
  
  .quick-question-buttons {
    grid-template-columns: 1fr;
  }
  
  .chat-messages {
    padding: 12px;
  }
}

/* Dark Mode Support for Interactive Features */
@media (prefers-color-scheme: dark) {
  .coach-tabs {
    background: #2c2c2e;
    border-bottom-color: rgba(255, 255, 255, 0.1);
  }
  
  .tab {
    color: #98989d;
  }
  
  .tab:hover {
    background: rgba(0, 122, 255, 0.1);
    color: #64d2ff;
  }
  
  .tab.active {
    color: #64d2ff;
    border-bottom-color: #64d2ff;
    background: rgba(0, 122, 255, 0.1);
  }
  
  .quick-questions {
    border-bottom-color: rgba(255, 255, 255, 0.1);
  }
  
  .quick-questions h5 {
    color: #f2f2f7;
  }
  
  .quick-question-buttons button {
    background: linear-gradient(145deg, #2c2c2e 0%, #1c1c1e 100%);
    border-color: rgba(255, 255, 255, 0.1);
    color: #f2f2f7;
  }
  
  .quick-question-buttons button:hover {
    background: linear-gradient(145deg, #007aff 0%, #0051d5 100%);
    color: white;
  }
  
  .chat-message.coach .message-content {
    background: #2c2c2e;
    color: #f2f2f7;
    border-color: rgba(255, 255, 255, 0.1);
  }
  
  .message-time {
    color: #98989d;
  }
  
  .typing-indicator span {
    background: #98989d;
  }
  
  .chat-input {
    background: #2c2c2e;
    border-top-color: rgba(255, 255, 255, 0.1);
  }
  
  .question-input {
    background: #1c1c1e;
    border-color: rgba(255, 255, 255, 0.1);
    color: #f2f2f7;
  }
  
  .question-input:focus {
    border-color: #64d2ff;
    box-shadow: 0 0 0 3px rgba(100, 210, 255, 0.1);
  }
}

/* Voice Input Button */
.voice-button {
  background: linear-gradient(145deg, #28a745 0%, #20c997 100%);
  border: none;
  border-radius: 20px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 16px;
  margin-left: 8px;
}

.voice-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
}

.voice-button.listening {
  background: linear-gradient(145deg, #dc3545 0%, #e74c3c 100%);
  animation: pulse 1.5s infinite;
}

.voice-button.listening:hover {
  box-shadow: 0 4px 8px rgba(220, 53, 69, 0.3);
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

/* Update chat input to accommodate voice button */
.chat-input {
  display: flex;
  padding: 16px;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  background: #f8f9fa;
  gap: 8px;
  align-items: center;
}

.question-input {
  flex: 1;
  background: white;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 20px;
  padding: 10px 16px;
  font-family: inherit;
  font-size: 14px;
  outline: none;
  transition: border-color 0.2s ease;
}

.question-input:disabled {
  background: #f8f9fa;
  color: #6c757d;
  cursor: not-allowed;
}

.question-input:focus {
  border-color: #007aff;
  box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
}

/* Dark mode support for voice features */
@media (prefers-color-scheme: dark) {
  .voice-button {
    background: linear-gradient(145deg, #28a745 0%, #20c997 100%);
  }
  
  .voice-button.listening {
    background: linear-gradient(145deg, #dc3545 0%, #e74c3c 100%);
  }
  
  .question-input:disabled {
    background: #2c2c2e;
    color: #98989d;
  }

  .recommended-bid,
  .recommended-card {
    color: #f2f2f7 !important; /* Light text for dark mode - force override */
  }

  .recommended-bid strong,
  .recommended-card strong {
    color: #f2f2f7 !important; /* Light text for dark mode - force override */
  }

  /* Suit colors in dark mode */
  .card-value.red-suit,
  .bid-value.red-suit {
    color: #ef4444 !important; /* Brighter red for dark mode */
    text-shadow: 0 1px 2px rgba(239, 68, 68, 0.4);
  }

  .card-value.black-suit,
  .bid-value.black-suit {
    color: #f2f2f7 !important; /* Light color for black suits in dark mode */
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  }

  /* More specific selectors for dark mode */
  .expert-move-section .move-recommendation .recommended-bid,
  .expert-move-section .move-recommendation .recommended-card {
    color: #f2f2f7 !important;
  }

  .expert-move-section .move-recommendation .recommended-bid strong,
  .expert-move-section .move-recommendation .recommended-card strong {
    color: #f2f2f7 !important;
  }
}

/* Responsive design for voice input */
@media (max-width: 768px) {
  .chat-input {
    padding: 12px;
  }
  
  .voice-button {
    width: 36px;
    height: 36px;
    font-size: 14px;
  }
}

/* Expert Move Recommendation Styles */
.expert-move-section {
  margin-top: 16px;
  padding: 16px;
  background: linear-gradient(135deg, #f0f8ff 0%, #e6f3ff 100%);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 12px;
}

.expert-move-section h5 {
  margin: 0 0 12px 0;
  color: #1e40af;
  font-weight: 600;
  font-size: 14px;
}

.analyzing {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #6b7280;
  font-style: italic;
}

.spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.expert-move {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.move-recommendation {
  padding: 12px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 8px;
  border-left: 4px solid #10b981;
}

.recommended-bid,
.recommended-card {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #000000 !important; /* Force black text */
}

/* Suit colors for recommended cards and bids */
.card-value.red-suit,
.bid-value.red-suit {
  color: #dc2626 !important; /* Red for Hearts and Diamonds */
  font-weight: bold;
  text-shadow: 0 1px 2px rgba(220, 38, 38, 0.3);
}

.card-value.black-suit,
.bid-value.black-suit {
  color: #000000 !important; /* Black for Spades and Clubs */
  font-weight: bold;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.recommended-bid strong,
.recommended-card strong {
  color: #000000 !important; /* Force black text */
}

.recommended-bid *,
.recommended-card * {
  color: #000000 !important; /* Force black text on all child elements */
}

/* More specific selectors to override any parent styling */
.expert-move-section .move-recommendation .recommended-bid,
.expert-move-section .move-recommendation .recommended-card {
  color: #000000 !important; /* Force black text */
}

.expert-move-section .move-recommendation .recommended-bid strong,
.expert-move-section .move-recommendation .recommended-card strong {
  color: #000000 !important; /* Force black text */
}

/* Even more specific - target all text elements */
.expert-move-section .move-recommendation .recommended-bid *,
.expert-move-section .move-recommendation .recommended-card * {
  color: #000000 !important; /* Force black text on all child elements */
}

.bid-value,
.card-value {
  font-weight: bold;
  color: #059669;
  font-size: 16px;
  padding: 4px 8px;
  background: rgba(16, 185, 129, 0.1);
  border-radius: 6px;
}

.move-reasoning {
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 8px;
  font-size: 13px;
  color: #374151;
  line-height: 1.4;
}

.move-reasoning p {
  margin: 0;
}

.expert-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.execute-move-btn,
.refresh-analysis-btn,
.get-expert-advice-btn {
  flex: 1;
  min-width: 120px;
  padding: 10px 16px;
  border: none;
  border-radius: 8px;
  font-size: 13px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

.execute-move-btn {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  box-shadow: 0 2px 4px rgba(16, 185, 129, 0.3);
}

.execute-move-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
  box-shadow: 0 4px 8px rgba(16, 185, 129, 0.4);
  transform: translateY(-1px);
}

.execute-move-btn:disabled {
  background: #d1d5db;
  color: #9ca3af;
  cursor: not-allowed;
  box-shadow: none;
}

.refresh-analysis-btn {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  color: white;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
}

.refresh-analysis-btn:hover {
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.4);
  transform: translateY(-1px);
}

.get-expert-advice-btn {
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
  color: white;
  box-shadow: 0 2px 4px rgba(139, 92, 246, 0.3);
  width: 100%;
}

.get-expert-advice-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #7c3aed 0%, #6d28d9 100%);
  box-shadow: 0 4px 8px rgba(139, 92, 246, 0.4);
  transform: translateY(-1px);
}

.get-expert-advice-btn:disabled {
  background: #d1d5db;
  color: #9ca3af;
  cursor: not-allowed;
}

/* Responsive adjustments */
@media (max-width: 480px) {
  .expert-actions {
    flex-direction: column;
  }
  
  .execute-move-btn,
  .refresh-analysis-btn {
    min-width: auto;
  }
}

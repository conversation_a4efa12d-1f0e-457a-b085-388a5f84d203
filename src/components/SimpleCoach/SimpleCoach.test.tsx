/**
 * Tests for SimpleCoach component
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import SimpleCoach from './SimpleCoach';
import { Position, GameState, GamePhase, PlayerType, Suit, Rank } from '../../types/bridge';

// Mock the AI player module
jest.mock('../../lib/bridge/aiPlayer', () => ({
  AIPlayer: jest.fn().mockImplementation(() => ({
    makeBid: jest.fn().mockReturnValue('PASS'),
    playCard: jest.fn().mockReturnValue({ suit: Suit.SPADES, rank: Rank.ACE })
  }))
}));

// Mock the game state utils
jest.mock('../../lib/bridge/gameStateUtils', () => ({
  processBid: jest.fn().mockImplementation((state, bid) => ({ ...state, auction: [...state.auction, bid] })),
  playCard: jest.fn().mockImplementation((state, card, player) => ({ ...state, currentTrick: [...state.currentTrick, { player, card }] }))
}));

// Mock speech synthesis
Object.defineProperty(window, 'speechSynthesis', {
  writable: true,
  value: {
    speak: jest.fn(),
    cancel: jest.fn(),
    pause: jest.fn(),
    resume: jest.fn(),
    getVoices: jest.fn().mockReturnValue([])
  }
});

Object.defineProperty(window, 'SpeechSynthesisUtterance', {
  writable: true,
  value: jest.fn().mockImplementation((text) => ({
    text,
    rate: 1,
    pitch: 1,
    volume: 1
  }))
});

const createMockGameState = (phase: GamePhase = GamePhase.BIDDING): GameState => ({
  id: 'test-game',
  players: {
    [Position.NORTH]: { 
      type: PlayerType.AI, 
      hand: [
        { suit: Suit.SPADES, rank: Rank.ACE },
        { suit: Suit.HEARTS, rank: Rank.KING }
      ] 
    },
    [Position.EAST]: { 
      type: PlayerType.AI, 
      hand: [
        { suit: Suit.DIAMONDS, rank: Rank.QUEEN },
        { suit: Suit.CLUBS, rank: Rank.JACK }
      ] 
    },
    [Position.SOUTH]: { 
      type: PlayerType.HUMAN, 
      hand: [
        { suit: Suit.SPADES, rank: Rank.KING },
        { suit: Suit.HEARTS, rank: Rank.ACE },
        { suit: Suit.DIAMONDS, rank: Rank.ACE },
        { suit: Suit.CLUBS, rank: Rank.ACE }
      ] 
    },
    [Position.WEST]: { 
      type: PlayerType.AI, 
      hand: [
        { suit: Suit.SPADES, rank: Rank.TWO },
        { suit: Suit.HEARTS, rank: Rank.THREE }
      ] 
    }
  },
  currentPlayer: Position.SOUTH,
  phase,
  auction: [],
  currentTrick: [],
  tricks: [],
  score: { northSouth: 0, eastWest: 0 },
  dealer: Position.NORTH,
  vulnerability: { northSouth: false, eastWest: false },
  contract: null
});

describe('SimpleCoach', () => {
  const mockOnGameStateChange = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders coach interface', () => {
    const gameState = createMockGameState();
    render(
      <SimpleCoach
        gameState={gameState}
        humanPosition={Position.SOUTH}
        onGameStateChange={mockOnGameStateChange}
      />
    );

    expect(screen.getByText('🎓 Bridge Coach')).toBeInTheDocument();
    expect(screen.getByText('Your Turn!')).toBeInTheDocument();
  });

  it('calculates high card points correctly', () => {
    const gameState = createMockGameState();
    render(
      <SimpleCoach
        gameState={gameState}
        humanPosition={Position.SOUTH}
        onGameStateChange={mockOnGameStateChange}
      />
    );

    // South has K♠, A♥, A♦, A♣ = 15 HCP
    expect(screen.getByText('15')).toBeInTheDocument();
  });

  it('shows correct suit distribution', () => {
    const gameState = createMockGameState();
    render(
      <SimpleCoach
        gameState={gameState}
        humanPosition={Position.SOUTH}
        onGameStateChange={mockOnGameStateChange}
      />
    );

    // Should show 1-1-1-1 distribution
    expect(screen.getByText('1-1-1-1')).toBeInTheDocument();
  });

  it('provides appropriate bidding advice for strong hand', () => {
    const gameState = createMockGameState();
    render(
      <SimpleCoach
        gameState={gameState}
        humanPosition={Position.SOUTH}
        onGameStateChange={mockOnGameStateChange}
      />
    );

    // Should suggest opening with 15 HCP
    expect(screen.getByText(/15 high card points/)).toBeInTheDocument();
    expect(screen.getByText(/consider opening/)).toBeInTheDocument();
  });

  it('shows waiting message when not player turn', () => {
    const gameState = createMockGameState();
    gameState.currentPlayer = Position.NORTH;
    
    render(
      <SimpleCoach
        gameState={gameState}
        humanPosition={Position.SOUTH}
        onGameStateChange={mockOnGameStateChange}
      />
    );

    expect(screen.getByText('Waiting for your turn...')).toBeInTheDocument();
  });

  it('can be hidden and shown', () => {
    const gameState = createMockGameState();
    render(
      <SimpleCoach
        gameState={gameState}
        humanPosition={Position.SOUTH}
        onGameStateChange={mockOnGameStateChange}
      />
    );

    // Hide the coach
    const closeButton = screen.getByTitle('Hide coach');
    fireEvent.click(closeButton);

    // Should show toggle button
    expect(screen.getByText('🎓 Coach')).toBeInTheDocument();
    expect(screen.queryByText('Your Turn!')).not.toBeInTheDocument();

    // Show the coach again
    const toggleButton = screen.getByText('🎓 Coach');
    fireEvent.click(toggleButton);

    expect(screen.getByText('Your Turn!')).toBeInTheDocument();
  });

  it('toggles speech functionality', () => {
    const gameState = createMockGameState();
    render(
      <SimpleCoach
        gameState={gameState}
        humanPosition={Position.SOUTH}
        onGameStateChange={mockOnGameStateChange}
      />
    );

    const speechToggle = screen.getByTitle('Toggle voice coaching');
    
    // Initially should be inactive
    expect(speechToggle).not.toHaveClass('active');
    
    // Click to activate
    fireEvent.click(speechToggle);
    expect(speechToggle).toHaveClass('active');
  });
});

/**
 * Test for bid symbol display in SimpleCoach
 */

import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import SimpleCoach from './SimpleCoach';
import { GameState, GamePhase, Position, PlayerType, Suit, Rank } from '../../types/bridge';

// Mock the AI player to return predictable bids
jest.mock('../../lib/bridge/aiPlayer', () => ({
  AIPlayer: jest.fn().mockImplementation(() => ({
    makeBid: jest.fn().mockReturnValue({
      level: 1,
      suit: 'H' // Hearts
    })
  }))
}));

const createMockGameState = (phase: GamePhase = GamePhase.BIDDING): GameState => ({
  id: 'test-game',
  players: {
    [Position.NORTH]: {
      type: PlayerType.AI,
      hand: [
        { suit: Suit.SPADES, rank: Rank.ACE },
        { suit: Suit.HEARTS, rank: Rank.KING }
      ]
    },
    [Position.EAST]: {
      type: PlayerType.AI,
      hand: [
        { suit: Suit.DIAMONDS, rank: Rank.QUEEN },
        { suit: Suit.CLUBS, rank: Rank.JACK }
      ]
    },
    [Position.SOUTH]: {
      type: PlayerType.HUMAN,
      hand: [
        { suit: Suit.SPADES, rank: Rank.KING },
        { suit: Suit.HEARTS, rank: Rank.ACE },
        { suit: Suit.DIAMONDS, rank: Rank.ACE },
        { suit: Suit.CLUBS, rank: Rank.ACE }
      ]
    },
    [Position.WEST]: {
      type: PlayerType.AI,
      hand: [
        { suit: Suit.SPADES, rank: Rank.TWO },
        { suit: Suit.HEARTS, rank: Rank.THREE }
      ]
    }
  },
  phase,
  currentPlayer: Position.SOUTH,
  auction: [],
  currentTrick: [],
  tricks: [],
  dealer: Position.NORTH,
  vulnerable: { ns: false, ew: false },
  contract: null,
  dummy: null,
  declarer: null
});

describe('SimpleCoach Bid Symbol Display', () => {
  it('displays suit symbols instead of letters in recommended bid', async () => {
    const mockGameState: GameState = {
      ...createMockGameState(),
      phase: GamePhase.BIDDING,
      currentPlayer: Position.SOUTH
    };

    render(
      <SimpleCoach 
        gameState={mockGameState} 
        humanPosition={Position.SOUTH}
      />
    );

    // Wait for the expert recommendation to load
    await waitFor(() => {
      expect(screen.queryByText(/Analyzing position/)).not.toBeInTheDocument();
    }, { timeout: 3000 });

    // Check that the recommended bid shows suit symbol (♥) instead of letter (H)
    const bidElement = screen.getByText(/Recommended Bid:/);
    expect(bidElement).toBeInTheDocument();
    
    // Look for the bid value with heart symbol (should appear in both bid display and reasoning)
    const bidElements = screen.getAllByText(/1 ♥/);
    expect(bidElements.length).toBeGreaterThanOrEqual(1);

    // Check specifically for the bid value span
    const bidValueElement = screen.getByText(/Recommended Bid:/).parentElement?.querySelector('.bid-value');
    expect(bidValueElement).toHaveTextContent('1 ♥');

    // Ensure it's NOT showing the letter H
    expect(screen.queryByText(/1 H[^♥]/)).not.toBeInTheDocument();
  });

  it('displays different suit symbols correctly', async () => {
    // Test different suits
    const testCases = [
      { suit: 'S', symbol: '♠', name: 'Spades' },
      { suit: 'H', symbol: '♥', name: 'Hearts' },
      { suit: 'D', symbol: '♦', name: 'Diamonds' },
      { suit: 'C', symbol: '♣', name: 'Clubs' },
      { suit: 'NT', symbol: 'NT', name: 'No Trump' }
    ];

    for (const testCase of testCases) {
      // Mock AI player for this specific suit
      const { AIPlayer } = require('../../lib/bridge/aiPlayer');
      AIPlayer.mockImplementation(() => ({
        makeBid: jest.fn().mockReturnValue({
          level: 1,
          suit: testCase.suit
        })
      }));

      const mockGameState: GameState = {
        ...createMockGameState(),
        phase: GamePhase.BIDDING,
        currentPlayer: Position.SOUTH
      };

      const { unmount } = render(
        <SimpleCoach 
          gameState={mockGameState} 
          humanPosition={Position.SOUTH}
        />
      );

      // Wait for the expert recommendation to load
      await waitFor(() => {
        expect(screen.queryByText(/Analyzing position/)).not.toBeInTheDocument();
      }, { timeout: 3000 });

      // Check that the recommended bid shows the correct suit symbol
      const expectedBid = `1 ${testCase.symbol}`;
      const bidElements = screen.getAllByText(new RegExp(expectedBid));
      expect(bidElements.length).toBeGreaterThanOrEqual(1);

      // Check specifically for the bid value span
      const bidValueElement = screen.getByText(/Recommended Bid:/).parentElement?.querySelector('.bid-value');
      expect(bidValueElement).toHaveTextContent(expectedBid);

      unmount();
    }
  });

  it('displays suit symbols in reasoning text', async () => {
    const { AIPlayer } = require('../../lib/bridge/aiPlayer');
    AIPlayer.mockImplementation(() => ({
      makeBid: jest.fn().mockReturnValue({
        level: 1,
        suit: 'S' // Spades
      })
    }));

    const mockGameState: GameState = {
      ...createMockGameState(),
      phase: GamePhase.BIDDING,
      currentPlayer: Position.SOUTH
    };

    render(
      <SimpleCoach 
        gameState={mockGameState} 
        humanPosition={Position.SOUTH}
      />
    );

    // Wait for the expert recommendation to load
    await waitFor(() => {
      expect(screen.queryByText(/Analyzing position/)).not.toBeInTheDocument();
    }, { timeout: 3000 });

    // Check that the reasoning text also shows suit symbol
    expect(screen.getByText(/Expert recommends 1 ♠/)).toBeInTheDocument();
  });
});

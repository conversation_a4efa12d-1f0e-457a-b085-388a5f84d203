/**
 * GameSettings component styles
 * Modern, accessible design for Bridge game configuration
 */

.game-settings-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.game-settings-panel {
  background: linear-gradient(145deg, #2a3d2a 0%, #1a2d1a 100%);
  border: 2px solid rgba(255, 215, 0, 0.3);
  border-radius: 16px;
  box-shadow: 
    0 20px 40px rgba(0, 0, 0, 0.5),
    0 0 0 1px rgba(255, 255, 255, 0.1);
  max-width: 600px;
  width: 100%;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* Header */
.settings-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  border-bottom: 1px solid rgba(255, 215, 0, 0.2);
  background: rgba(255, 215, 0, 0.1);
}

.settings-header h2 {
  margin: 0;
  color: #ffd700;
  font-size: 24px;
  font-weight: 600;
}

.close-button {
  background: none;
  border: none;
  color: #ffd700;
  font-size: 24px;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.close-button:hover {
  background: rgba(255, 215, 0, 0.2);
  transform: scale(1.1);
}

/* Content */
.settings-content {
  flex: 1;
  overflow-y: auto;
  padding: 24px;
}

.settings-section {
  margin-bottom: 32px;
}

.settings-section:last-child {
  margin-bottom: 0;
}

.settings-section h3 {
  margin: 0 0 16px 0;
  color: #ffffff;
  font-size: 18px;
  font-weight: 600;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding-bottom: 8px;
}

.setting-group {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

/* Setting Items */
.setting-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  cursor: pointer;
  padding: 12px;
  border-radius: 8px;
  transition: all 0.2s ease;
  background: rgba(255, 255, 255, 0.05);
}

.setting-item:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateX(4px);
}

.setting-item input[type="checkbox"] {
  width: 18px;
  height: 18px;
  accent-color: #ffd700;
  margin-top: 2px;
}

.setting-label {
  color: #ffffff;
  font-weight: 500;
  flex: 1;
}

.setting-description {
  color: #cccccc;
  font-size: 14px;
  margin-top: 4px;
  display: block;
}

/* Radio Groups */
.radio-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.radio-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  cursor: pointer;
  padding: 12px;
  border-radius: 8px;
  transition: all 0.2s ease;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid transparent;
}

.radio-item:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 215, 0, 0.3);
}

.radio-item input[type="radio"] {
  width: 18px;
  height: 18px;
  accent-color: #ffd700;
  margin-top: 2px;
}

.radio-label {
  color: #ffffff;
  font-weight: 500;
  flex: 1;
}

.radio-description {
  color: #cccccc;
  font-size: 14px;
  margin-top: 4px;
  display: block;
}

/* Select Inputs */
.select-input {
  width: 100%;
  padding: 12px 16px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  color: #ffffff;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.select-input:hover {
  border-color: rgba(255, 215, 0, 0.5);
  background: rgba(255, 255, 255, 0.15);
}

.select-input:focus {
  outline: none;
  border-color: #ffd700;
  box-shadow: 0 0 0 2px rgba(255, 215, 0, 0.2);
}

.select-input option {
  background: #2a3d2a;
  color: #ffffff;
}

/* Footer */
.settings-footer {
  display: flex;
  gap: 12px;
  padding: 20px 24px;
  border-top: 1px solid rgba(255, 215, 0, 0.2);
  background: rgba(0, 0, 0, 0.2);
}

.reset-button,
.apply-button {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.reset-button {
  background: rgba(255, 255, 255, 0.1);
  color: #ffffff;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.reset-button:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

.apply-button {
  background: linear-gradient(145deg, #ffd700 0%, #ffed4e 100%);
  color: #2a3d2a;
  flex: 1;
  box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3);
}

.apply-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(255, 215, 0, 0.4);
}

/* Responsive Design */
@media (max-width: 768px) {
  .game-settings-overlay {
    padding: 10px;
  }
  
  .game-settings-panel {
    max-height: 95vh;
  }
  
  .settings-header {
    padding: 16px 20px;
  }
  
  .settings-header h2 {
    font-size: 20px;
  }
  
  .settings-content {
    padding: 20px;
  }
  
  .settings-section {
    margin-bottom: 24px;
  }
  
  .settings-footer {
    flex-direction: column;
    padding: 16px 20px;
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .setting-item,
  .radio-item,
  .select-input,
  .reset-button,
  .apply-button,
  .close-button {
    transition: none;
  }
  
  .setting-item:hover,
  .apply-button:hover {
    transform: none;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .game-settings-panel {
    border-color: #ffffff;
    background: #000000;
  }
  
  .setting-item,
  .radio-item {
    border: 1px solid #ffffff;
  }
  
  .select-input {
    border-color: #ffffff;
  }
}

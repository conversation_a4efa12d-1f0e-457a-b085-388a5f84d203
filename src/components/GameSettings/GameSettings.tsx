/**
 * GameSettings component for configuring Bridge game options
 * Provides authentic Bridge settings instead of artificial difficulty levels
 */

import React, { useState } from 'react';
import './GameSettings.css';

export interface GameSettings {
  // Play Mode Settings
  openHandedPlay: boolean;
  showDummyAfterAuction: boolean;
  enableCoachingHints: boolean;
  
  // AI Settings
  aiSkillLevel: 'beginner' | 'intermediate' | 'advanced';
  
  // Bidding System
  biddingSystem: 'sayc' | 'precision' | 'acol' | 'two-over-one';
  
  // Conventions
  conventions: {
    stayman: boolean;
    blackwood: boolean;
    transfers: boolean;
    negativeDoubles: boolean;
    takeoutDoubles: boolean;
  };
  
  // Game Mode
  gameMode: 'practice' | 'learning' | 'standard' | 'tournament';
}

interface GameSettingsProps {
  settings: GameSettings;
  onSettingsChange: (settings: GameSettings) => void;
  onClose?: () => void;
  isOpen?: boolean;
}

const DEFAULT_SETTINGS: GameSettings = {
  openHandedPlay: true, // Changed to true for easier troubleshooting
  showDummyAfterAuction: true,
  enableCoachingHints: true,
  aiSkillLevel: 'intermediate',
  biddingSystem: 'sayc',
  conventions: {
    stayman: true,
    blackwood: true,
    transfers: false,
    negativeDoubles: false,
    takeoutDoubles: true,
  },
  gameMode: 'standard',
};

const GameSettings: React.FC<GameSettingsProps> = ({
  settings,
  onSettingsChange,
  onClose,
  isOpen = true
}) => {
  const [localSettings, setLocalSettings] = useState<GameSettings>(settings);

  const handleSettingChange = (key: keyof GameSettings, value: any) => {
    const newSettings = { ...localSettings, [key]: value };
    setLocalSettings(newSettings);
    onSettingsChange(newSettings);
  };

  const handleConventionChange = (convention: keyof GameSettings['conventions'], value: boolean) => {
    const newConventions = { ...localSettings.conventions, [convention]: value };
    const newSettings = { ...localSettings, conventions: newConventions };
    setLocalSettings(newSettings);
    onSettingsChange(newSettings);
  };

  const resetToDefaults = () => {
    setLocalSettings(DEFAULT_SETTINGS);
    onSettingsChange(DEFAULT_SETTINGS);
  };

  if (!isOpen) return null;

  return (
    <div className="game-settings-overlay" data-testid="game-settings">
      <div className="game-settings-panel">
        <div className="settings-header">
          <h2>🎯 Bridge Game Settings</h2>
          {onClose && (
            <button className="close-button" onClick={onClose} aria-label="Close settings">
              ✕
            </button>
          )}
        </div>

        <div className="settings-content">
          {/* Play Mode Section */}
          <div className="settings-section">
            <h3>🃏 Play Mode</h3>
            <div className="setting-group">
              <label className="setting-item">
                <input
                  type="checkbox"
                  checked={localSettings.openHandedPlay}
                  onChange={(e) => handleSettingChange('openHandedPlay', e.target.checked)}
                />
                <span className="setting-label">Open-handed play (all cards visible)</span>
                <span className="setting-description">Perfect for learning and practice</span>
              </label>

              <label className="setting-item">
                <input
                  type="checkbox"
                  checked={localSettings.showDummyAfterAuction}
                  onChange={(e) => handleSettingChange('showDummyAfterAuction', e.target.checked)}
                />
                <span className="setting-label">Show dummy after auction</span>
                <span className="setting-description">Standard Bridge rule</span>
              </label>

              <label className="setting-item">
                <input
                  type="checkbox"
                  checked={localSettings.enableCoachingHints}
                  onChange={(e) => handleSettingChange('enableCoachingHints', e.target.checked)}
                />
                <span className="setting-label">Enable coaching hints</span>
                <span className="setting-description">Get suggestions during play</span>
              </label>
            </div>
          </div>

          {/* AI Skill Section */}
          <div className="settings-section">
            <h3>🤖 AI Skill Level</h3>
            <div className="setting-group">
              <div className="radio-group">
                {[
                  { value: 'beginner', label: 'Beginner', description: 'Basic bidding and play' },
                  { value: 'intermediate', label: 'Intermediate', description: 'Standard conventions' },
                  { value: 'advanced', label: 'Advanced', description: 'Expert-level play' }
                ].map(({ value, label, description }) => (
                  <label key={value} className="radio-item">
                    <input
                      type="radio"
                      name="aiSkillLevel"
                      value={value}
                      checked={localSettings.aiSkillLevel === value}
                      onChange={(e) => handleSettingChange('aiSkillLevel', e.target.value)}
                    />
                    <span className="radio-label">{label}</span>
                    <span className="radio-description">{description}</span>
                  </label>
                ))}
              </div>
            </div>
          </div>

          {/* Bidding System Section */}
          <div className="settings-section">
            <h3>📋 Bidding System</h3>
            <div className="setting-group">
              <select
                value={localSettings.biddingSystem}
                onChange={(e) => handleSettingChange('biddingSystem', e.target.value)}
                className="select-input"
              >
                <option value="sayc">Standard American (SAYC)</option>
                <option value="precision">Precision Club</option>
                <option value="acol">Acol</option>
                <option value="two-over-one">2/1 Game Force</option>
              </select>
            </div>
          </div>

          {/* Game Mode Section */}
          <div className="settings-section">
            <h3>🎮 Game Mode</h3>
            <div className="setting-group">
              <select
                value={localSettings.gameMode}
                onChange={(e) => handleSettingChange('gameMode', e.target.value)}
                className="select-input"
              >
                <option value="practice">Practice Mode</option>
                <option value="learning">Learning Mode</option>
                <option value="standard">Standard Play</option>
                <option value="tournament">Tournament Mode</option>
              </select>
            </div>
          </div>

          {/* Conventions Section */}
          <div className="settings-section">
            <h3>🔧 Conventions</h3>
            <div className="setting-group">
              {Object.entries(localSettings.conventions).map(([convention, enabled]) => (
                <label key={convention} className="setting-item">
                  <input
                    type="checkbox"
                    checked={enabled}
                    onChange={(e) => handleConventionChange(convention as keyof GameSettings['conventions'], e.target.checked)}
                  />
                  <span className="setting-label">
                    {convention.charAt(0).toUpperCase() + convention.slice(1).replace(/([A-Z])/g, ' $1')}
                  </span>
                </label>
              ))}
            </div>
          </div>
        </div>

        <div className="settings-footer">
          <button className="reset-button" onClick={resetToDefaults}>
            Reset to Defaults
          </button>
          {onClose && (
            <button className="apply-button" onClick={onClose}>
              Apply Settings
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default GameSettings;
export { DEFAULT_SETTINGS };

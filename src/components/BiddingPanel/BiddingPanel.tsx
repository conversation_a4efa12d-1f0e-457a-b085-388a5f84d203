/**
 * BiddingPanel component for handling bidding interface and logic
 * Displays bid buttons, auction history, and manages bidding interactions
 */

import React, { useState } from 'react';
import { BidLevel, BidSuit, SpecialBid, GameState, Position, Bid } from '../../types/bridge';
import { getLastContractBid, isValidSimpleBid } from '../../lib/bridge/biddingUtils';
import './BiddingPanel.css';

interface BiddingPanelProps {
  gameState: GameState;
  currentPlayer: Position;
  onBid: (bid: Bid) => void;
  isPlayerTurn: boolean;
  disabled?: boolean;
}

const BiddingPanel: React.FC<BiddingPanelProps> = ({
  gameState,
  currentPlayer,
  onBid,
  isPlayerTurn,
  disabled = false
}) => {
  const [selectedLevel, setSelectedLevel] = useState<BidLevel | null>(null);
  const [selectedSuit, setSelectedSuit] = useState<BidSuit | null>(null);

  const lastBid = getLastContractBid(gameState.auction);
  const canBid = isPlayerTurn && !disabled;

  // Get available bid levels based on last bid
  const getAvailableLevels = (): BidLevel[] => {
    if (!lastBid || typeof lastBid.value === 'string') {
      return [BidLevel.ONE, BidLevel.TWO, BidLevel.THREE, BidLevel.FOUR, BidLevel.FIVE, BidLevel.SIX, BidLevel.SEVEN];
    }
    
    const lastLevel = lastBid.value.level;
    const lastSuit = lastBid.value.suit;
    
    // If same level, must bid higher suit
    const levels: BidLevel[] = [];
    for (let level = lastLevel; level <= BidLevel.SEVEN; level++) {
      if (level > lastLevel || (level === lastLevel && canBidHigherSuit(lastSuit))) {
        levels.push(level);
      }
    }
    
    return levels;
  };

  // Get available suits for selected level
  const getAvailableSuits = (level: BidLevel): BidSuit[] => {
    if (!lastBid || typeof lastBid.value === 'string') {
      return [BidSuit.CLUBS, BidSuit.DIAMONDS, BidSuit.HEARTS, BidSuit.SPADES, BidSuit.NO_TRUMP];
    }
    
    const lastLevel = lastBid.value.level;
    const lastSuit = lastBid.value.suit;
    
    if (level > lastLevel) {
      return [BidSuit.CLUBS, BidSuit.DIAMONDS, BidSuit.HEARTS, BidSuit.SPADES, BidSuit.NO_TRUMP];
    }
    
    if (level === lastLevel) {
      return getSuitsHigherThan(lastSuit);
    }
    
    return [];
  };

  const canBidHigherSuit = (lastSuit: BidSuit): boolean => {
    return getSuitsHigherThan(lastSuit).length > 0;
  };

  const getSuitsHigherThan = (suit: BidSuit): BidSuit[] => {
    const suitOrder = [BidSuit.CLUBS, BidSuit.DIAMONDS, BidSuit.HEARTS, BidSuit.SPADES, BidSuit.NO_TRUMP];
    const lastIndex = suitOrder.indexOf(suit);
    return suitOrder.slice(lastIndex + 1);
  };

  const getSuitSymbol = (suit: BidSuit): string => {
    switch (suit) {
      case BidSuit.CLUBS: return '♣';
      case BidSuit.DIAMONDS: return '♦';
      case BidSuit.HEARTS: return '♥';
      case BidSuit.SPADES: return '♠';
      case BidSuit.NO_TRUMP: return 'NT';
    }
  };

  const getSuitColor = (suit: BidSuit): string => {
    switch (suit) {
      case BidSuit.HEARTS:
      case BidSuit.DIAMONDS:
        return 'red';
      case BidSuit.CLUBS:
      case BidSuit.SPADES:
        return 'black';
      case BidSuit.NO_TRUMP:
        return 'blue';
    }
  };

  const handleLevelSelect = (level: BidLevel) => {
    if (!canBid) return;
    setSelectedLevel(level);
    setSelectedSuit(null);
  };

  const handleSuitSelect = (suit: BidSuit) => {
    if (!canBid || !selectedLevel) return;
    setSelectedSuit(suit);
  };

  const handleMakeBid = () => {
    if (!canBid || !selectedLevel || !selectedSuit) return;
    
    const bidValue = { level: selectedLevel, suit: selectedSuit };
    const bid: Bid = {
      player: currentPlayer,
      value: bidValue,
      timestamp: new Date()
    };
    
    if (isValidSimpleBid(bid)) {
      onBid(bid);
      setSelectedLevel(null);
      setSelectedSuit(null);
    }
  };

  const handleSpecialBid = (specialBid: SpecialBid) => {
    if (!canBid) return;
    
    const bid: Bid = {
      player: currentPlayer,
      value: specialBid,
      timestamp: new Date()
    };
    
    if (isValidSimpleBid(bid)) {
      onBid(bid);
      setSelectedLevel(null);
      setSelectedSuit(null);
    }
  };

  const canDouble = (): boolean => {
    if (!lastBid || typeof lastBid.value === 'string') return false;
    
    // Can only double opponent's bid
    const isOpponentBid = (currentPlayer === Position.NORTH || currentPlayer === Position.SOUTH) ?
      (lastBid.player === Position.EAST || lastBid.player === Position.WEST) :
      (lastBid.player === Position.NORTH || lastBid.player === Position.SOUTH);
    
    return isOpponentBid;
  };

  const canRedouble = (): boolean => {
    // Check if last bid was doubled by opponents
    const lastSpecialBid = gameState.auction
      .slice()
      .reverse()
      .find(bid => typeof bid.value === 'string');
    
    return lastSpecialBid?.value === SpecialBid.DOUBLE;
  };

  const availableLevels = getAvailableLevels();
  const availableSuits = selectedLevel ? getAvailableSuits(selectedLevel) : [];

  return (
    <div className={`bidding-panel ${canBid ? 'active' : 'inactive'}`} data-testid="bidding-panel">
      <div className="bidding-header">
        <h3>Bidding</h3>
        <div className="current-player">
          {canBid ? "Your turn" : `${gameState.players[currentPlayer].name}'s turn`}
        </div>
      </div>

      {canBid && (
        <div className="bidding-controls">
          {/* Level Selection */}
          <div className="level-selection">
            <h4>Level</h4>
            <div className="level-buttons">
              {availableLevels.map(level => (
                <button
                  key={level}
                  className={`level-button ${selectedLevel === level ? 'selected' : ''}`}
                  onClick={() => handleLevelSelect(level)}
                  disabled={disabled}
                >
                  {level}
                </button>
              ))}
            </div>
          </div>

          {/* Suit Selection */}
          {selectedLevel && (
            <div className="suit-selection">
              <h4>Suit</h4>
              <div className="suit-buttons">
                {availableSuits.map(suit => (
                  <button
                    key={suit}
                    className={`suit-button ${getSuitColor(suit)} ${selectedSuit === suit ? 'selected' : ''}`}
                    onClick={() => handleSuitSelect(suit)}
                    disabled={disabled}
                  >
                    <span className="suit-symbol">{getSuitSymbol(suit)}</span>
                    <span className="suit-name">{suit}</span>
                  </button>
                ))}
              </div>
            </div>
          )}

          {/* Make Bid Button */}
          {selectedLevel && selectedSuit && (
            <div className="make-bid">
              <button
                className={`make-bid-button ${selectedSuit ? getSuitColor(selectedSuit) : ''}`}
                onClick={handleMakeBid}
                disabled={disabled}
              >
                Bid {selectedLevel}<span className={`suit-symbol ${selectedSuit ? getSuitColor(selectedSuit) : ''}`}>{getSuitSymbol(selectedSuit)}</span>
              </button>
            </div>
          )}

          {/* Special Bids */}
          <div className="special-bids">
            <button
              className="special-bid-button pass"
              onClick={() => handleSpecialBid(SpecialBid.PASS)}
              disabled={disabled}
            >
              Pass
            </button>
            
            {canDouble() && (
              <button
                className="special-bid-button double"
                onClick={() => handleSpecialBid(SpecialBid.DOUBLE)}
                disabled={disabled}
              >
                Double
              </button>
            )}
            
            {canRedouble() && (
              <button
                className="special-bid-button redouble"
                onClick={() => handleSpecialBid(SpecialBid.REDOUBLE)}
                disabled={disabled}
              >
                Redouble
              </button>
            )}
          </div>
        </div>
      )}

      {!canBid && (
        <div className="waiting-message">
          <p>Waiting for {gameState.players[currentPlayer].name} to bid...</p>
        </div>
      )}
    </div>
  );
};

export default BiddingPanel;

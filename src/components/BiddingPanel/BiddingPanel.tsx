/**
 * BiddingPanel component for handling bidding interface and logic
 * Displays bid buttons, auction history, and manages bidding interactions
 */

import React, { useState, useEffect } from 'react';
import { BidLevel, BidSuit, SpecialBid, GameState, Position, Bid, BidValue } from '../../types/bridge';
import { getLastContractBid, isValidSimpleBid } from '../../lib/bridge/biddingUtils';
import './BiddingPanel.css';

interface BiddingPanelProps {
  gameState: GameState;
  currentPlayer: Position;
  onBid: (bid: Bid) => void;
  isPlayerTurn: boolean;
  disabled?: boolean;
}

const BiddingPanel: React.FC<BiddingPanelProps> = ({
  gameState,
  currentPlayer,
  onBid,
  isPlayerTurn,
  disabled = false
}) => {
  const [selectedLevel, setSelectedLevel] = useState<BidLevel | null>(null);
  const [selectedSuit, setSelectedSuit] = useState<BidSuit | null>(null);
  const [expertRecommendation, setExpertRecommendation] = useState<{bid?: BidValue, reasoning: string} | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);

  const lastBid = getLastContractBid(gameState.auction);
  const canBid = isPlayerTurn && !disabled;

  // Generate expert recommendation when it's the player's turn
  useEffect(() => {
    if (canBid && !expertRecommendation) {
      generateExpertRecommendation();
    }
  }, [canBid, gameState.auction, currentPlayer]);

  const generateExpertRecommendation = async () => {
    setIsAnalyzing(true);
    try {
      // Simple recommendation logic - in a real app this would use AI
      const playerHand = gameState.players[currentPlayer].hand;
      const highCardPoints = calculateHighCardPoints(playerHand);

      let recommendedBid: BidValue;
      let reasoning: string;

      if (highCardPoints >= 12) {
        // Find longest suit
        const suitCounts = {
          'S': playerHand.filter(c => c.suit === 'S').length,
          'H': playerHand.filter(c => c.suit === 'H').length,
          'D': playerHand.filter(c => c.suit === 'D').length,
          'C': playerHand.filter(c => c.suit === 'C').length,
        };

        const longestSuitKey = Object.entries(suitCounts).reduce((a, b) =>
          suitCounts[a[0] as keyof typeof suitCounts] > suitCounts[b[0] as keyof typeof suitCounts] ? a : b
        )[0];

        // Convert suit key to BidSuit enum
        const longestSuit = longestSuitKey === 'S' ? BidSuit.SPADES :
                           longestSuitKey === 'H' ? BidSuit.HEARTS :
                           longestSuitKey === 'D' ? BidSuit.DIAMONDS :
                           BidSuit.CLUBS;

        recommendedBid = { level: BidLevel.ONE, suit: longestSuit };
        reasoning = `You have ${highCardPoints} high card points. With 12+ points, consider opening. Your longest suit is ${longestSuit} with ${suitCounts[longestSuitKey as keyof typeof suitCounts]} cards.`;
      } else {
        recommendedBid = SpecialBid.PASS;
        reasoning = `You have ${highCardPoints} high card points. With fewer than 12 points, passing is usually best.`;
      }

      setExpertRecommendation({ bid: recommendedBid, reasoning });
    } catch (error) {
      console.error('Error generating recommendation:', error);
      setExpertRecommendation({
        bid: SpecialBid.PASS,
        reasoning: "Unable to analyze hand. Consider your options carefully."
      });
    } finally {
      setIsAnalyzing(false);
    }
  };

  const calculateHighCardPoints = (hand: any[]) => {
    return hand.reduce((points, card) => {
      switch (card.rank) {
        case 'A': return points + 4;
        case 'K': return points + 3;
        case 'Q': return points + 2;
        case 'J': return points + 1;
        default: return points;
      }
    }, 0);
  };

  // Get available bid levels based on last bid
  const getAvailableLevels = (): BidLevel[] => {
    if (!lastBid || typeof lastBid.value === 'string') {
      return [BidLevel.ONE, BidLevel.TWO, BidLevel.THREE, BidLevel.FOUR, BidLevel.FIVE, BidLevel.SIX, BidLevel.SEVEN];
    }
    
    const lastLevel = lastBid.value.level;
    const lastSuit = lastBid.value.suit;
    
    // If same level, must bid higher suit
    const levels: BidLevel[] = [];
    for (let level = lastLevel; level <= BidLevel.SEVEN; level++) {
      if (level > lastLevel || (level === lastLevel && canBidHigherSuit(lastSuit))) {
        levels.push(level);
      }
    }
    
    return levels;
  };

  // Get available suits for selected level
  const getAvailableSuits = (level: BidLevel): BidSuit[] => {
    if (!lastBid || typeof lastBid.value === 'string') {
      return [BidSuit.CLUBS, BidSuit.DIAMONDS, BidSuit.HEARTS, BidSuit.SPADES, BidSuit.NO_TRUMP];
    }
    
    const lastLevel = lastBid.value.level;
    const lastSuit = lastBid.value.suit;
    
    if (level > lastLevel) {
      return [BidSuit.CLUBS, BidSuit.DIAMONDS, BidSuit.HEARTS, BidSuit.SPADES, BidSuit.NO_TRUMP];
    }
    
    if (level === lastLevel) {
      return getSuitsHigherThan(lastSuit);
    }
    
    return [];
  };

  const canBidHigherSuit = (lastSuit: BidSuit): boolean => {
    return getSuitsHigherThan(lastSuit).length > 0;
  };

  const getSuitsHigherThan = (suit: BidSuit): BidSuit[] => {
    const suitOrder = [BidSuit.CLUBS, BidSuit.DIAMONDS, BidSuit.HEARTS, BidSuit.SPADES, BidSuit.NO_TRUMP];
    const lastIndex = suitOrder.indexOf(suit);
    return suitOrder.slice(lastIndex + 1);
  };

  const getSuitSymbol = (suit: BidSuit): string => {
    switch (suit) {
      case BidSuit.CLUBS: return '♣';
      case BidSuit.DIAMONDS: return '♦';
      case BidSuit.HEARTS: return '♥';
      case BidSuit.SPADES: return '♠';
      case BidSuit.NO_TRUMP: return 'NT';
    }
  };

  const getSuitColor = (suit: BidSuit): string => {
    switch (suit) {
      case BidSuit.HEARTS:
      case BidSuit.DIAMONDS:
        return 'red';
      case BidSuit.CLUBS:
      case BidSuit.SPADES:
        return 'black';
      case BidSuit.NO_TRUMP:
        return 'blue';
    }
  };

  const handleLevelSelect = (level: BidLevel) => {
    if (!canBid) return;
    setSelectedLevel(level);
    setSelectedSuit(null);
  };

  const handleSuitSelect = (suit: BidSuit) => {
    if (!canBid || !selectedLevel) return;
    setSelectedSuit(suit);
  };

  const handleMakeBid = () => {
    if (!canBid || !selectedLevel || !selectedSuit) return;

    const bidValue = { level: selectedLevel, suit: selectedSuit };
    const bid: Bid = {
      player: currentPlayer,
      value: bidValue,
      timestamp: new Date()
    };

    if (isValidSimpleBid(bid)) {
      onBid(bid);
      setSelectedLevel(null);
      setSelectedSuit(null);
      setExpertRecommendation(null);
    }
  };

  const handleSpecialBid = (specialBid: SpecialBid) => {
    if (!canBid) return;

    const bid: Bid = {
      player: currentPlayer,
      value: specialBid,
      timestamp: new Date()
    };

    if (isValidSimpleBid(bid)) {
      onBid(bid);
      setSelectedLevel(null);
      setSelectedSuit(null);
      setExpertRecommendation(null);
    }
  };

  const handleExecuteRecommendation = () => {
    if (!canBid || !expertRecommendation?.bid) return;

    const bid: Bid = {
      player: currentPlayer,
      value: expertRecommendation.bid,
      timestamp: new Date()
    };

    if (isValidSimpleBid(bid)) {
      onBid(bid);
      setSelectedLevel(null);
      setSelectedSuit(null);
      setExpertRecommendation(null);
    }
  };

  const canDouble = (): boolean => {
    if (!lastBid || typeof lastBid.value === 'string') return false;
    
    // Can only double opponent's bid
    const isOpponentBid = (currentPlayer === Position.NORTH || currentPlayer === Position.SOUTH) ?
      (lastBid.player === Position.EAST || lastBid.player === Position.WEST) :
      (lastBid.player === Position.NORTH || lastBid.player === Position.SOUTH);
    
    return isOpponentBid;
  };

  const canRedouble = (): boolean => {
    // Check if last bid was doubled by opponents
    const lastSpecialBid = gameState.auction
      .slice()
      .reverse()
      .find(bid => typeof bid.value === 'string');
    
    return lastSpecialBid?.value === SpecialBid.DOUBLE;
  };

  const availableLevels = getAvailableLevels();
  const availableSuits = selectedLevel ? getAvailableSuits(selectedLevel) : [];

  return (
    <div className={`bidding-panel ${canBid ? 'active' : 'inactive'}`} data-testid="bidding-panel">
      <div className="bidding-header">
        <h3>Bidding</h3>
        <div className="current-player">
          {canBid ? "Your turn" : `${gameState.players[currentPlayer].name}'s turn`}
        </div>
      </div>

      {canBid && (
        <div className="bidding-controls">
          {/* Integrated Coach Recommendation */}
          <div className="coach-recommendation">
            <div className="recommendation-header">
              <span className="coach-icon">🎯</span>
              <span>Expert Recommendation:</span>
            </div>
            {isAnalyzing ? (
              <div className="analyzing">
                <span className="spinner">⏳</span> Analyzing position...
              </div>
            ) : expertRecommendation ? (
              <>
                <div className="recommended-bid-display">
                  <span className={`bid-suggestion ${
                    expertRecommendation.bid !== SpecialBid.PASS &&
                    typeof expertRecommendation.bid === "object" &&
                    expertRecommendation.bid.suit ?
                    getSuitColor(expertRecommendation.bid.suit) : ''
                  }`}>
                    {expertRecommendation.bid === SpecialBid.PASS ? "PASS" :
                     typeof expertRecommendation.bid === "object" && expertRecommendation.bid.level && expertRecommendation.bid.suit ?
                     `${expertRecommendation.bid.level} ${getSuitSymbol(expertRecommendation.bid.suit)}` : "PASS"}
                  </span>
                  <button
                    className="execute-recommendation"
                    title="Use recommended bid"
                    onClick={() => handleExecuteRecommendation()}
                  >
                    ✓ Execute
                  </button>
                </div>
                <div className="recommendation-reason">
                  {expertRecommendation.reasoning}
                </div>
              </>
            ) : (
              <div className="no-recommendation">
                Click to get expert advice
              </div>
            )}
          </div>

          {/* Compact Level & Suit Grid */}
          <div className="bid-grid">
            <div className="grid-header">
              <span>Level</span>
              <span>Suit</span>
            </div>

            {/* Combined Level/Suit Selection */}
            <div className="level-suit-grid">
              {availableLevels.map(level => (
                <div key={level} className="level-row">
                  <button
                    className={`level-button ${selectedLevel === level ? 'selected' : ''}`}
                    onClick={() => handleLevelSelect(level)}
                    disabled={disabled}
                  >
                    {level}
                  </button>

                  {selectedLevel === level && (
                    <div className="suit-row">
                      {availableSuits.map(suit => (
                        <button
                          key={suit}
                          className={`suit-button compact ${getSuitColor(suit)} ${selectedSuit === suit ? 'selected' : ''}`}
                          onClick={() => handleSuitSelect(suit)}
                          disabled={disabled}
                          title={`${level} ${suit}`}
                        >
                          {getSuitSymbol(suit)}
                        </button>
                      ))}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Action Buttons */}
          <div className="action-buttons">
            {selectedLevel && selectedSuit && (
              <button
                className={`make-bid-button primary ${selectedSuit ? getSuitColor(selectedSuit) : ''}`}
                onClick={handleMakeBid}
                disabled={disabled}
              >
                Bid {selectedLevel}{getSuitSymbol(selectedSuit)}
              </button>
            )}

            <div className="special-bids-row">
              <button
                className="special-bid-button pass"
                onClick={() => handleSpecialBid(SpecialBid.PASS)}
                disabled={disabled}
              >
                Pass
              </button>

              {canDouble() && (
                <button
                  className="special-bid-button double"
                  onClick={() => handleSpecialBid(SpecialBid.DOUBLE)}
                  disabled={disabled}
                >
                  Double
                </button>
              )}

              {canRedouble() && (
                <button
                  className="special-bid-button redouble"
                  onClick={() => handleSpecialBid(SpecialBid.REDOUBLE)}
                  disabled={disabled}
                >
                  Redouble
                </button>
              )}
            </div>
          </div>
        </div>
      )}

      {!canBid && (
        <div className="waiting-message">
          <p>Waiting for {gameState.players[currentPlayer].name} to bid...</p>
        </div>
      )}
    </div>
  );
};

export default BiddingPanel;

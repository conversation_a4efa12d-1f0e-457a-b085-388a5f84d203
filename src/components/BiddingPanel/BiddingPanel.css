/**
 * BiddingPanel component styles
 */

.bidding-panel {
  background: rgba(0, 0, 0, 0.6);
  border-radius: 8px;
  padding: 12px;
  color: white;
  width: 100%;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
  font-size: 13px;
}

.bidding-panel.inactive {
  opacity: 0.7;
}

.bidding-header {
  text-align: center;
  margin-bottom: 12px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding-bottom: 8px;
}

.bidding-header h3 {
  margin: 0 0 4px 0;
  font-size: 14px;
  font-weight: 600;
  color: #ffd700;
}

.current-player {
  font-size: 11px;
  color: #cccccc;
  font-weight: 500;
}

.bidding-controls {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

/* Integrated Coach Recommendation */
.coach-recommendation {
  background: rgba(0, 122, 255, 0.15);
  border: 1px solid rgba(0, 122, 255, 0.3);
  border-radius: 8px;
  padding: 10px;
  margin-bottom: 8px;
}

.recommendation-header {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  font-weight: 600;
  color: #87ceeb;
  margin-bottom: 6px;
}

.coach-icon {
  font-size: 14px;
}

.recommended-bid-display {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 6px;
}

.bid-suggestion {
  font-size: 18px;
  font-weight: bold;
  color: #ffd700;
  padding: 4px 8px;
  background: rgba(255, 215, 0, 0.2);
  border-radius: 4px;
}

.execute-recommendation {
  background: rgba(34, 197, 94, 0.8);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 11px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.execute-recommendation:hover {
  background: rgba(34, 197, 94, 1);
}

.recommendation-reason {
  font-size: 11px;
  color: #cccccc;
  line-height: 1.3;
  font-style: italic;
}

.analyzing {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #87ceeb;
  padding: 8px 0;
}

.spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.no-recommendation {
  font-size: 12px;
  color: #999;
  padding: 8px 0;
  text-align: center;
  font-style: italic;
}

/* Compact Bid Grid */
.bid-grid {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 6px;
  padding: 8px;
}

.grid-header {
  display: grid;
  grid-template-columns: 40px 1fr;
  gap: 8px;
  font-size: 11px;
  font-weight: 600;
  color: #cccccc;
  margin-bottom: 6px;
  padding-bottom: 4px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.level-suit-grid {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.level-row {
  display: flex;
  align-items: center;
  gap: 8px;
}

.level-button {
  width: 32px;
  height: 32px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  flex-shrink: 0;
}

.level-button:hover {
  background: rgba(255, 255, 255, 0.2);
}

.level-button.selected {
  background: rgba(0, 122, 255, 0.8);
  border-color: rgba(0, 122, 255, 1);
  color: white;
}

.suit-row {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.suit-button.compact {
  width: 28px;
  height: 28px;
  padding: 0;
  font-size: 14px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.make-bid-button.primary {
  background: rgba(34, 197, 94, 0.8);
  color: white;
  border: none;
  border-radius: 6px;
  padding: 10px 16px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s;
}

.make-bid-button.primary:hover {
  background: rgba(34, 197, 94, 1);
}

.special-bids-row {
  display: flex;
  gap: 6px;
  justify-content: space-between;
}

/* Level Selection */
.level-selection h4,
.suit-selection h4 {
  margin: 0 0 6px 0;
  font-size: 12px;
  font-weight: 600;
  color: #ffd700;
  text-align: center;
}

.level-buttons {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
  justify-content: center;
}

.level-button {
  width: 32px;
  height: 32px;
  border: 1px solid #4a9eff;
  background: rgba(74, 158, 255, 0.1);
  color: #4a9eff;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.level-button:hover {
  background: rgba(74, 158, 255, 0.2);
  transform: translateY(-1px);
}

.level-button.selected {
  background: #4a9eff;
  color: white;
  box-shadow: 0 0 12px rgba(74, 158, 255, 0.5);
}

.level-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/* Suit Selection */
.suit-buttons {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4px;
}

.suit-button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 8px;
  border: 1px solid transparent;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 32px;
  font-size: 11px;
}

.suit-button.compact {
  gap: 0;
  padding: 0;
  min-height: auto;
  justify-content: center;
}

.suit-button:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

.suit-button.selected {
  border-color: #ffd700;
  background: rgba(255, 215, 0, 0.2);
  box-shadow: 0 0 12px rgba(255, 215, 0, 0.3);
}

.suit-button.red {
  color: #dc2626; /* Match card red color */
  text-shadow: 0 1px 2px rgba(220, 38, 38, 0.3);
}

.suit-button.black {
  color: #ffffff; /* White for good contrast on dark background */
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.suit-button.blue {
  color: #4a9eff; /* No Trump stays blue */
}

.suit-symbol {
  font-size: 16px;
  font-weight: bold;
  min-width: 20px;
  text-align: center;
}

.suit-name {
  font-size: 10px;
  font-weight: 500;
  text-transform: capitalize;
}

.suit-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/* Make Bid Button */
.make-bid {
  text-align: center;
}

.make-bid-button {
  background: linear-gradient(135deg, #28a745, #20c997);
  color: white;
  border: none;
  border-radius: 6px;
  padding: 8px 16px;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
  width: 100%;
}

.make-bid-button:hover {
  background: linear-gradient(135deg, #218838, #1ea085);
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(40, 167, 69, 0.4);
}

.make-bid-button:active {
  transform: translateY(0);
}

.make-bid-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Special Bids */
.special-bids {
  display: flex;
  gap: 4px;
  justify-content: center;
  flex-wrap: wrap;
}

.special-bid-button {
  padding: 6px 12px;
  border: 1px solid;
  background: transparent;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 60px;
  flex: 1;
}

.special-bid-button.pass {
  border-color: #6c757d;
  color: #6c757d;
}

.special-bid-button.pass:hover {
  background: #6c757d;
  color: white;
}

.special-bid-button.double {
  border-color: #dc3545;
  color: #dc3545;
}

.special-bid-button.double:hover {
  background: #dc3545;
  color: white;
}

.special-bid-button.redouble {
  border-color: #fd7e14;
  color: #fd7e14;
}

.special-bid-button.redouble:hover {
  background: #fd7e14;
  color: white;
}

.special-bid-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Waiting Message */
.waiting-message {
  text-align: center;
  padding: 20px 12px;
}

.waiting-message p {
  margin: 0;
  font-size: 12px;
  color: #cccccc;
  font-style: italic;
}

/* Compact design optimized for side panel */
.right-panel-section .bidding-panel {
  max-height: 280px;
  overflow-y: auto;
}

.right-panel-section .bidding-panel::-webkit-scrollbar {
  width: 4px;
}

.right-panel-section .bidding-panel::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 2px;
}

.right-panel-section .bidding-panel::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 2px;
}

.right-panel-section .bidding-panel::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* Accessibility */
.bidding-panel button:focus {
  outline: 2px solid #4a9eff;
  outline-offset: 2px;
}

/* Animations */
@keyframes bidSubmitted {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.make-bid-button.submitted {
  animation: bidSubmitted 0.3s ease;
}

/* Suit colors for make bid button */
.make-bid-button .suit-symbol.red {
  color: #dc2626 !important;
  text-shadow: 0 1px 2px rgba(220, 38, 38, 0.3);
}

.make-bid-button .suit-symbol.black {
  color: #000000 !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.make-bid-button .suit-symbol.blue {
  color: #4a9eff !important;
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .bidding-panel button,
  .level-button,
  .suit-button,
  .make-bid-button,
  .special-bid-button {
    transition: none;
    transform: none;
  }
  
  .make-bid-button.submitted {
    animation: none;
  }
}




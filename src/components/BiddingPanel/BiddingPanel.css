/**
 * BiddingPanel component styles
 */

.bidding-panel {
  background: rgba(0, 0, 0, 0.6);
  border-radius: 8px;
  padding: 12px;
  color: white;
  width: 100%;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
  font-size: 13px;
}

.bidding-panel.inactive {
  opacity: 0.7;
}

.bidding-header {
  text-align: center;
  margin-bottom: 12px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding-bottom: 8px;
}

.bidding-header h3 {
  margin: 0 0 4px 0;
  font-size: 14px;
  font-weight: 600;
  color: #ffd700;
}

.current-player {
  font-size: 11px;
  color: #cccccc;
  font-weight: 500;
}

.bidding-controls {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

/* Level Selection */
.level-selection h4,
.suit-selection h4 {
  margin: 0 0 6px 0;
  font-size: 12px;
  font-weight: 600;
  color: #ffd700;
  text-align: center;
}

.level-buttons {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
  justify-content: center;
}

.level-button {
  width: 32px;
  height: 32px;
  border: 1px solid #4a9eff;
  background: rgba(74, 158, 255, 0.1);
  color: #4a9eff;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.level-button:hover {
  background: rgba(74, 158, 255, 0.2);
  transform: translateY(-1px);
}

.level-button.selected {
  background: #4a9eff;
  color: white;
  box-shadow: 0 0 12px rgba(74, 158, 255, 0.5);
}

.level-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/* Suit Selection */
.suit-buttons {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4px;
}

.suit-button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 8px;
  border: 1px solid transparent;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 32px;
  font-size: 11px;
}

.suit-button:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

.suit-button.selected {
  border-color: #ffd700;
  background: rgba(255, 215, 0, 0.2);
  box-shadow: 0 0 12px rgba(255, 215, 0, 0.3);
}

.suit-button.red {
  color: #dc2626; /* Match card red color */
  text-shadow: 0 1px 2px rgba(220, 38, 38, 0.3);
}

.suit-button.black {
  color: #ffffff; /* White for good contrast on dark background */
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.suit-button.blue {
  color: #4a9eff; /* No Trump stays blue */
}

.suit-symbol {
  font-size: 16px;
  font-weight: bold;
  min-width: 20px;
  text-align: center;
}

.suit-name {
  font-size: 10px;
  font-weight: 500;
  text-transform: capitalize;
}

.suit-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/* Make Bid Button */
.make-bid {
  text-align: center;
}

.make-bid-button {
  background: linear-gradient(135deg, #28a745, #20c997);
  color: white;
  border: none;
  border-radius: 6px;
  padding: 8px 16px;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
  width: 100%;
}

.make-bid-button:hover {
  background: linear-gradient(135deg, #218838, #1ea085);
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(40, 167, 69, 0.4);
}

.make-bid-button:active {
  transform: translateY(0);
}

.make-bid-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Special Bids */
.special-bids {
  display: flex;
  gap: 4px;
  justify-content: center;
  flex-wrap: wrap;
}

.special-bid-button {
  padding: 6px 12px;
  border: 1px solid;
  background: transparent;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 60px;
  flex: 1;
}

.special-bid-button.pass {
  border-color: #6c757d;
  color: #6c757d;
}

.special-bid-button.pass:hover {
  background: #6c757d;
  color: white;
}

.special-bid-button.double {
  border-color: #dc3545;
  color: #dc3545;
}

.special-bid-button.double:hover {
  background: #dc3545;
  color: white;
}

.special-bid-button.redouble {
  border-color: #fd7e14;
  color: #fd7e14;
}

.special-bid-button.redouble:hover {
  background: #fd7e14;
  color: white;
}

.special-bid-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Waiting Message */
.waiting-message {
  text-align: center;
  padding: 20px 12px;
}

.waiting-message p {
  margin: 0;
  font-size: 12px;
  color: #cccccc;
  font-style: italic;
}

/* Compact design optimized for side panel */
.right-panel-section .bidding-panel {
  max-height: 280px;
  overflow-y: auto;
}

.right-panel-section .bidding-panel::-webkit-scrollbar {
  width: 4px;
}

.right-panel-section .bidding-panel::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 2px;
}

.right-panel-section .bidding-panel::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 2px;
}

.right-panel-section .bidding-panel::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* Accessibility */
.bidding-panel button:focus {
  outline: 2px solid #4a9eff;
  outline-offset: 2px;
}

/* Animations */
@keyframes bidSubmitted {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.make-bid-button.submitted {
  animation: bidSubmitted 0.3s ease;
}

/* Suit colors for make bid button */
.make-bid-button .suit-symbol.red {
  color: #dc2626 !important;
  text-shadow: 0 1px 2px rgba(220, 38, 38, 0.3);
}

.make-bid-button .suit-symbol.black {
  color: #000000 !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.make-bid-button .suit-symbol.blue {
  color: #4a9eff !important;
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .bidding-panel button,
  .level-button,
  .suit-button,
  .make-bid-button,
  .special-bid-button {
    transition: none;
    transform: none;
  }
  
  .make-bid-button.submitted {
    animation: none;
  }
}




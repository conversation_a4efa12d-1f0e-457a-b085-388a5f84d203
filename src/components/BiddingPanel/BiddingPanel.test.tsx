/**
 * Unit tests for BiddingPanel component
 */

import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import BiddingPanel from './BiddingPanel';
import { 
  Position, 
  PlayerType, 
  GamePhase, 
  BidLevel, 
  BidSuit, 
  SpecialBid,
  GameState 
} from '../../types/bridge';

// Mock the bidding utils
jest.mock('../../lib/bridge/biddingUtils', () => ({
  getLastContractBid: jest.fn(),
  isValidBid: jest.fn(() => true),
}));

const mockGetLastContractBid = require('../../lib/bridge/biddingUtils').getLastContractBid;
const mockIsValidBid = require('../../lib/bridge/biddingUtils').isValidBid;

describe('BiddingPanel', () => {
  const mockGameState: GameState = {
    id: 'test-game',
    phase: GamePhase.BIDDING,
    dealer: Position.NORTH,
    currentPlayer: Position.SOUTH,
    players: {
      [Position.NORTH]: {
        id: 'north',
        name: 'North Player',
        position: Position.NORTH,
        type: PlayerType.AI,
        hand: []
      },
      [Position.SOUTH]: {
        id: 'south',
        name: 'You',
        position: Position.SOUTH,
        type: PlayerType.HUMAN,
        hand: []
      },
      [Position.EAST]: {
        id: 'east',
        name: 'East Player',
        position: Position.EAST,
        type: PlayerType.AI,
        hand: []
      },
      [Position.WEST]: {
        id: 'west',
        name: 'West Player',
        position: Position.WEST,
        type: PlayerType.AI,
        hand: []
      }
    },
    auction: [],
    contract: null,
    tricks: [],
    currentTrick: [],
    dummy: null,
    vulnerabilities: { northSouth: false, eastWest: false },
    score: { northSouth: 0, eastWest: 0 },
    gameNumber: 1,
    rubberScore: { northSouth: 0, eastWest: 0 }
  };

  const mockOnBid = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    mockGetLastContractBid.mockReturnValue(null);
    mockIsValidBid.mockReturnValue(true);
  });

  it('renders without crashing', () => {
    render(
      <BiddingPanel
        gameState={mockGameState}
        currentPlayer={Position.SOUTH}
        onBid={mockOnBid}
        isPlayerTurn={true}
      />
    );
    
    expect(screen.getByTestId('bidding-panel')).toBeInTheDocument();
  });

  it('displays bidding header correctly', () => {
    render(
      <BiddingPanel
        gameState={mockGameState}
        currentPlayer={Position.SOUTH}
        onBid={mockOnBid}
        isPlayerTurn={true}
      />
    );
    
    expect(screen.getByText('Bidding')).toBeInTheDocument();
    expect(screen.getByText('Your turn')).toBeInTheDocument();
  });

  it('shows waiting message when not player turn', () => {
    render(
      <BiddingPanel
        gameState={mockGameState}
        currentPlayer={Position.NORTH}
        onBid={mockOnBid}
        isPlayerTurn={false}
      />
    );
    
    expect(screen.getByText('Waiting for North Player to bid...')).toBeInTheDocument();
  });

  it('displays all bid levels when no previous bids', () => {
    render(
      <BiddingPanel
        gameState={mockGameState}
        currentPlayer={Position.SOUTH}
        onBid={mockOnBid}
        isPlayerTurn={true}
      />
    );
    
    // Should show levels 1-7
    expect(screen.getByText('1')).toBeInTheDocument();
    expect(screen.getByText('2')).toBeInTheDocument();
    expect(screen.getByText('3')).toBeInTheDocument();
    expect(screen.getByText('4')).toBeInTheDocument();
    expect(screen.getByText('5')).toBeInTheDocument();
    expect(screen.getByText('6')).toBeInTheDocument();
    expect(screen.getByText('7')).toBeInTheDocument();
  });

  it('allows level selection', () => {
    render(
      <BiddingPanel
        gameState={mockGameState}
        currentPlayer={Position.SOUTH}
        onBid={mockOnBid}
        isPlayerTurn={true}
      />
    );
    const levelButton = screen.getByText('1');    
    fireEvent.click(levelButton);
    
    expect(levelButton).toHaveClass('selected');
  });

  it('shows suit selection after level is selected', () => {
    render(
      <BiddingPanel
        gameState={mockGameState}
        currentPlayer={Position.SOUTH}
        onBid={mockOnBid}
        isPlayerTurn={true}
      />
    );
    
    // Select level 1
    fireEvent.click(screen.getByText('1'));
    
    // Should show all suits
    expect(screen.getByText('♣')).toBeInTheDocument();
    expect(screen.getByText('♦')).toBeInTheDocument();
    expect(screen.getByText('♥')).toBeInTheDocument();
    expect(screen.getByText('♠')).toBeInTheDocument();
    expect(screen.getAllByText('NT')).toHaveLength(2); // Symbol and name
  });

  it('shows make bid button after level and suit selection', () => {
    render(
      <BiddingPanel
        gameState={mockGameState}
        currentPlayer={Position.SOUTH}
        onBid={mockOnBid}
        isPlayerTurn={true}
      />
    );
    
    // Select level 1
    fireEvent.click(screen.getByText('1'));
    
    // Select clubs
    fireEvent.click(screen.getByText('♣'));
    
    // Should show make bid button
    expect(screen.getByText('Bid 1♣')).toBeInTheDocument();
  });

  it('calls onBid when make bid button is clicked', () => {
    render(
      <BiddingPanel
        gameState={mockGameState}
        currentPlayer={Position.SOUTH}
        onBid={mockOnBid}
        isPlayerTurn={true}
      />
    );
    
    // Select level 1
    fireEvent.click(screen.getByText('1'));
    
    // Select clubs
    fireEvent.click(screen.getByText('♣'));
    
    // Click make bid
    fireEvent.click(screen.getByText('Bid 1♣'));
    
    expect(mockOnBid).toHaveBeenCalledWith(expect.objectContaining({
      player: Position.SOUTH,
      value: { level: BidLevel.ONE, suit: BidSuit.CLUBS }
    }));
  });

  it('displays pass button', () => {
    render(
      <BiddingPanel
        gameState={mockGameState}
        currentPlayer={Position.SOUTH}
        onBid={mockOnBid}
        isPlayerTurn={true}
      />
    );
    
    expect(screen.getByText('Pass')).toBeInTheDocument();
  });

  it('calls onBid when pass button is clicked', () => {
    render(
      <BiddingPanel
        gameState={mockGameState}
        currentPlayer={Position.SOUTH}
        onBid={mockOnBid}
        isPlayerTurn={true}
      />
    );
    
    fireEvent.click(screen.getByText('Pass'));
    
    expect(mockOnBid).toHaveBeenCalledWith(expect.objectContaining({
      player: Position.SOUTH,
      value: SpecialBid.PASS
    }));
  });

  it('shows waiting message when disabled prop is true', () => {
    render(
      <BiddingPanel
        gameState={mockGameState}
        currentPlayer={Position.SOUTH}
        onBid={mockOnBid}
        isPlayerTurn={true}
        disabled={true}
      />
    );
    
    expect(screen.getByText('Waiting for You to bid...')).toBeInTheDocument();
    
    expect(screen.queryByText('1')).not.toBeInTheDocument();
    expect(screen.queryByText('Pass')).not.toBeInTheDocument();
  });

  it('applies inactive class when not player turn', () => {
    render(
      <BiddingPanel
        gameState={mockGameState}
        currentPlayer={Position.NORTH}
        onBid={mockOnBid}
        isPlayerTurn={false}
      />
    );
    
    expect(screen.getByTestId('bidding-panel')).toHaveClass('inactive');
  });

  it('resets selection after making a bid', () => {
    render(
      <BiddingPanel
        gameState={mockGameState}
        currentPlayer={Position.SOUTH}
        onBid={mockOnBid}
        isPlayerTurn={true}
      />
    );
    
    // Select level and suit
    fireEvent.click(screen.getByText('1'));
    fireEvent.click(screen.getByText('♣'));
    
    // Make bid
    fireEvent.click(screen.getByText('Bid 1♣'));
    
    // Selection should be reset
    expect(screen.getByText('1')).not.toHaveClass('selected');
    expect(screen.queryByText('Bid 1♣')).not.toBeInTheDocument();
  });
});

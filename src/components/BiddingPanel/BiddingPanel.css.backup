/**
 * BiddingPanel component styles
 */

.bidding-panel {
  background: rgba(0, 0, 0, 0.8);
  border-radius: 12px;
  padding: 20px;
  color: white;
  min-width: 300px;
  max-width: 400px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.bidding-panel.inactive {
  opacity: 0.7;
}

.bidding-header {
  text-align: center;
  margin-bottom: 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding-bottom: 15px;
}

.bidding-header h3 {
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 600;
  color: #ffd700;
}

.current-player {
  font-size: 14px;
  color: #cccccc;
  font-weight: 500;
}

.bidding-controls {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* Level Selection */
.level-selection h4,
.suit-selection h4 {
  margin: 0 0 10px 0;
  font-size: 16px;
  font-weight: 600;
  color: #ffffff;
}

.level-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  justify-content: center;
}

.level-button {
  width: 40px;
  height: 40px;
  border: 2px solid #4a9eff;
  background: rgba(74, 158, 255, 0.1);
  color: #4a9eff;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.level-button:hover {
  background: rgba(74, 158, 255, 0.2);
  transform: translateY(-1px);
}

.level-button.selected {
  background: #4a9eff;
  color: white;
  box-shadow: 0 0 12px rgba(74, 158, 255, 0.5);
}

.level-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/* Suit Selection */
.suit-buttons {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.suit-button {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  border: 2px solid transparent;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 48px;
}

.suit-button:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

.suit-button.selected {
  border-color: #ffd700;
  background: rgba(255, 215, 0, 0.2);
  box-shadow: 0 0 12px rgba(255, 215, 0, 0.3);
}

.suit-button.red {
  color: #ff6b6b;
}

.suit-button.black {
  color: #ffffff;
}

.suit-button.blue {
  color: #4a9eff;
}

.suit-symbol {
  font-size: 24px;
  font-weight: bold;
  min-width: 30px;
  text-align: center;
}

.suit-name {
  font-size: 14px;
  font-weight: 500;
  text-transform: capitalize;
}

.suit-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/* Make Bid Button */
.make-bid {
  text-align: center;
}

.make-bid-button {
  background: linear-gradient(135deg, #28a745, #20c997);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

.make-bid-button:hover {
  background: linear-gradient(135deg, #218838, #1ea085);
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(40, 167, 69, 0.4);
}

.make-bid-button:active {
  transform: translateY(0);
}

.make-bid-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Special Bids */
.special-bids {
  display: flex;
  gap: 10px;
  justify-content: center;
  flex-wrap: wrap;
}

.special-bid-button {
  padding: 10px 16px;
  border: 2px solid;
  background: transparent;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 80px;
}

.special-bid-button.pass {
  border-color: #6c757d;
  color: #6c757d;
}

.special-bid-button.pass:hover {
  background: #6c757d;
  color: white;
}

.special-bid-button.double {
  border-color: #dc3545;
  color: #dc3545;
}

.special-bid-button.double:hover {
  background: #dc3545;
  color: white;
}

.special-bid-button.redouble {
  border-color: #fd7e14;
  color: #fd7e14;
}

.special-bid-button.redouble:hover {
  background: #fd7e14;
  color: white;
}

.special-bid-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Waiting Message */
.waiting-message {
  text-align: center;
  padding: 40px 20px;
}

.waiting-message p {
  margin: 0;
  font-size: 16px;
  color: #cccccc;
  font-style: italic;
}

/* Responsive Design */
@media (max-width: 768px) {
  .bidding-panel {
    min-width: 280px;
    max-width: 320px;
    padding: 16px;
  }
  
  .level-buttons {
    gap: 6px;
  }
  
  .level-button {
    width: 36px;
    height: 36px;
    font-size: 14px;
  }
  
  .suit-button {
    padding: 10px 12px;
    min-height: 44px;
  }
  
  .suit-symbol {
    font-size: 20px;
    min-width: 24px;
  }
  
  .suit-name {
    font-size: 12px;
  }
  
  .special-bids {
    gap: 8px;
  }
  
  .special-bid-button {
    padding: 8px 12px;
    font-size: 12px;
    min-width: 70px;
  }
}

/* Accessibility */
.bidding-panel button:focus {
  outline: 2px solid #4a9eff;
  outline-offset: 2px;
}

/* Animations */
@keyframes bidSubmitted {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.make-bid-button.submitted {
  animation: bidSubmitted 0.3s ease;
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .bidding-panel button,
  .level-button,
  .suit-button,
  .make-bid-button,
  .special-bid-button {
    transition: none;
    transform: none;
  }
  
  .make-bid-button.submitted {
    animation: none;
  }
}

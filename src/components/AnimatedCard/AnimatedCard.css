/**
 * AnimatedCard component styles
 */

.animated-card {
  position: relative;
  display: inline-block;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: center bottom;
  will-change: transform;
}

.animated-card.not-playable {
  cursor: not-allowed;
  opacity: 0.6;
  filter: grayscale(30%);
}

/* Entrance animations */
.animated-card.entering {
  opacity: 0;
}

.animated-card.animate-fade {
  animation: fadeInCard 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.animated-card.animate-slide-up {
  animation: slideUpCard 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.animated-card.animate-slide-down {
  animation: slideDownCard 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.animated-card.animate-slide-left {
  animation: slideLeftCard 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.animated-card.animate-slide-right {
  animation: slideRightCard 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

@keyframes fadeInCard {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slideUpCard {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes slideDownCard {
  from {
    opacity: 0;
    transform: translateY(-30px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes slideLeftCard {
  from {
    opacity: 0;
    transform: translateX(30px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
}

@keyframes slideRightCard {
  from {
    opacity: 0;
    transform: translateX(-30px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
}

/* Hover effects */
.animated-card.hovered {
  transform: translateY(-8px) scale(1.05);
  z-index: 100;
  filter: brightness(1.1);
}

.animated-card.small.hovered {
  transform: translateY(-4px) scale(1.03);
}

.animated-card.large.hovered {
  transform: translateY(-12px) scale(1.08);
}

/* Selection effects */
.animated-card.selected {
  transform: translateY(-12px) scale(1.1);
  z-index: 200;
  filter: brightness(1.15) saturate(1.2);
}

.animated-card.small.selected {
  transform: translateY(-6px) scale(1.05);
}

.animated-card.large.selected {
  transform: translateY(-16px) scale(1.12);
}

.animated-card.selection-animating {
  animation: selectionPulse 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

@keyframes selectionPulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.15);
  }
  100% {
    transform: scale(1.1);
  }
}

/* Highlighted effects */
.animated-card.highlighted {
  animation: highlightPulse 2s ease-in-out infinite;
}

@keyframes highlightPulse {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(74, 158, 255, 0.7);
  }
  50% {
    box-shadow: 0 0 0 8px rgba(74, 158, 255, 0);
  }
}

/* Selection indicator */
.selection-indicator {
  position: absolute;
  top: -8px;
  right: -8px;
  width: 28px;
  height: 28px;
  pointer-events: none;
  z-index: 10;
}

.selection-ring {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 3px solid #28a745;
  border-radius: 50%;
  background: rgba(40, 167, 69, 0.2);
  animation: selectionRingPulse 1.5s ease-in-out infinite;
}

.selection-checkmark {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #28a745;
  font-size: 16px;
  font-weight: bold;
  text-shadow: 0 0 4px rgba(40, 167, 69, 0.8);
  animation: checkmarkBounce 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

@keyframes selectionRingPulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
}

@keyframes checkmarkBounce {
  0% {
    transform: translate(-50%, -50%) scale(0);
  }
  50% {
    transform: translate(-50%, -50%) scale(1.3);
  }
  100% {
    transform: translate(-50%, -50%) scale(1);
  }
}

/* Highlight glow */
.highlight-glow {
  position: absolute;
  top: -4px;
  left: -4px;
  right: -4px;
  bottom: -4px;
  background: linear-gradient(45deg, #4a9eff, #20c997, #4a9eff);
  border-radius: 10px;
  z-index: -1;
  opacity: 0.6;
  animation: glowRotate 3s linear infinite;
}

@keyframes glowRotate {
  0% {
    background: linear-gradient(45deg, #4a9eff, #20c997, #4a9eff);
  }
  33% {
    background: linear-gradient(45deg, #20c997, #ffd700, #20c997);
  }
  66% {
    background: linear-gradient(45deg, #ffd700, #4a9eff, #ffd700);
  }
  100% {
    background: linear-gradient(45deg, #4a9eff, #20c997, #4a9eff);
  }
}

/* Hover shine effect */
.hover-shine {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  border-radius: 6px;
  animation: shineEffect 0.6s ease-out;
  pointer-events: none;
  z-index: 5;
}

@keyframes shineEffect {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* Card group styles */
.animated-card-group {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: flex-end;
}

.animated-card-group .group-card {
  flex-shrink: 0;
}

/* Size-specific adjustments */
.animated-card.small .selection-indicator {
  width: 20px;
  height: 20px;
  top: -6px;
  right: -6px;
}

.animated-card.small .selection-checkmark {
  font-size: 12px;
}

.animated-card.large .selection-indicator {
  width: 32px;
  height: 32px;
  top: -10px;
  right: -10px;
}

.animated-card.large .selection-checkmark {
  font-size: 18px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .animated-card.hovered {
    transform: translateY(-4px) scale(1.03);
  }
  
  .animated-card.selected {
    transform: translateY(-6px) scale(1.05);
  }
  
  .selection-indicator {
    width: 24px;
    height: 24px;
    top: -6px;
    right: -6px;
  }
  
  .selection-checkmark {
    font-size: 14px;
  }
  
  .highlight-glow {
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
  }
}

@media (max-width: 480px) {
  .animated-card.hovered {
    transform: translateY(-2px) scale(1.02);
  }
  
  .animated-card.selected {
    transform: translateY(-4px) scale(1.03);
  }
  
  .selection-indicator {
    width: 20px;
    height: 20px;
    top: -5px;
    right: -5px;
  }
  
  .selection-checkmark {
    font-size: 12px;
  }
}

/* Performance optimizations */
.animated-card {
  backface-visibility: hidden;
  transform-style: preserve-3d;
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .animated-card,
  .selection-ring,
  .selection-checkmark,
  .highlight-glow,
  .hover-shine {
    animation: none !important;
    transition: none !important;
  }
  
  .animated-card.hovered {
    transform: scale(1.02) !important;
  }
  
  .animated-card.selected {
    transform: scale(1.05) !important;
    border: 2px solid #28a745;
  }
  
  .animated-card.highlighted {
    border: 2px solid #4a9eff;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .selection-ring {
    border-color: currentColor;
    background: transparent;
  }
  
  .selection-checkmark {
    color: currentColor;
    text-shadow: none;
  }
  
  .highlight-glow {
    background: currentColor;
    opacity: 0.3;
  }
}

/* Print styles */
@media print {
  .animated-card {
    transform: none !important;
    animation: none !important;
    transition: none !important;
  }
  
  .selection-indicator,
  .highlight-glow,
  .hover-shine {
    display: none !important;
  }
  
  .animated-card.selected {
    border: 2px solid black;
  }
}

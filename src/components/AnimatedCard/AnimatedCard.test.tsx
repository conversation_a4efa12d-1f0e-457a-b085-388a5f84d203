/**
 * Unit tests for AnimatedCard component
 */

import React from 'react';
import { render, screen, fireEvent, act, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import AnimatedCard, { AnimatedCardGroup } from './AnimatedCard';
import { Card, Suit, Rank } from '../../types/bridge';

// Mock the Card component
jest.mock('../Card/Card', () => {
  return function MockCard({ card, size, isPlayable }: any) {
    return (
      <div 
        className={`mock-card ${size} ${isPlayable ? 'playable' : 'not-playable'}`}
        data-testid={`card-${card.rank}-${card.suit}`}
      >
        {card.rank}{card.suit}
      </div>
    );
  };
});

// Mock CardHover component
jest.mock('../CardAnimations/CardAnimations', () => ({
  CardHover: ({ children, isEnabled }: any) => (
    <div data-testid="card-hover" data-enabled={isEnabled}>
      {children}
    </div>
  )
}));

// Mock timers
jest.useFakeTimers();

describe('AnimatedCard', () => {
  const mockCard: Card = { suit: Suit.HEARTS, rank: Rank.ACE };
  const mockOnClick = jest.fn();
  const mockOnHover = jest.fn();

  beforeEach(() => {
    jest.clearAllTimers();
    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.runOnlyPendingTimers();
    jest.useRealTimers();
    jest.useFakeTimers();
  });

  it('renders without crashing', () => {
    render(<AnimatedCard card={mockCard} />);
    expect(screen.getByTestId('animated-card')).toBeInTheDocument();
    expect(screen.getByTestId('card-ACE-HEARTS')).toBeInTheDocument();
  });

  it('applies correct size class', () => {
    render(<AnimatedCard card={mockCard} size="large" />);
    expect(screen.getByTestId('animated-card')).toHaveClass('large');
  });

  it('applies selected class when selected', () => {
    render(<AnimatedCard card={mockCard} isSelected={true} />);
    expect(screen.getByTestId('animated-card')).toHaveClass('selected');
  });

  it('applies highlighted class when highlighted', () => {
    render(<AnimatedCard card={mockCard} isHighlighted={true} />);
    expect(screen.getByTestId('animated-card')).toHaveClass('highlighted');
  });

  it('applies not-playable class when not playable', () => {
    render(<AnimatedCard card={mockCard} isPlayable={false} />);
    expect(screen.getByTestId('animated-card')).toHaveClass('not-playable');
  });

  it('handles click events', () => {
    render(<AnimatedCard card={mockCard} onClick={mockOnClick} />);
    
    fireEvent.click(screen.getByTestId('animated-card'));
    expect(mockOnClick).toHaveBeenCalledWith(mockCard);
  });

  it('does not handle click when not playable', () => {
    render(<AnimatedCard card={mockCard} isPlayable={false} onClick={mockOnClick} />);
    
    fireEvent.click(screen.getByTestId('animated-card'));
    expect(mockOnClick).not.toHaveBeenCalled();
  });

  it('handles hover events', () => {
    render(<AnimatedCard card={mockCard} onHover={mockOnHover} />);
    
    fireEvent.mouseEnter(screen.getByTestId('animated-card'));
    expect(mockOnHover).toHaveBeenCalledWith(mockCard);
    
    fireEvent.mouseLeave(screen.getByTestId('animated-card'));
    expect(mockOnHover).toHaveBeenCalledWith(null);
  });

  it('does not handle hover when not playable', () => {
    render(<AnimatedCard card={mockCard} isPlayable={false} onHover={mockOnHover} />);
    
    fireEvent.mouseEnter(screen.getByTestId('animated-card'));
    expect(mockOnHover).not.toHaveBeenCalled();
  });

  it('applies hover class on mouse enter', () => {
    render(<AnimatedCard card={mockCard} />);
    
    const cardElement = screen.getByTestId('animated-card');
    fireEvent.mouseEnter(cardElement);
    
    expect(cardElement).toHaveClass('hovered');
  });

  it('removes hover class on mouse leave', () => {
    render(<AnimatedCard card={mockCard} />);
    
    const cardElement = screen.getByTestId('animated-card');
    fireEvent.mouseEnter(cardElement);
    expect(cardElement).toHaveClass('hovered');
    
    fireEvent.mouseLeave(cardElement);
    expect(cardElement).not.toHaveClass('hovered');
  });

  it('shows selection indicator when selected', () => {
    render(<AnimatedCard card={mockCard} isSelected={true} />);
    expect(screen.getByText('✓')).toBeInTheDocument();
  });

  it('shows highlight glow when highlighted', () => {
    render(<AnimatedCard card={mockCard} isHighlighted={true} />);
    expect(screen.getByTestId('animated-card')).toContainElement(
      document.querySelector('.highlight-glow')
    );
  });

  it('shows hover shine when hovered', () => {
    render(<AnimatedCard card={mockCard} />);
    
    fireEvent.mouseEnter(screen.getByTestId('animated-card'));
    
    expect(screen.getByTestId('animated-card')).toContainElement(
      document.querySelector('.hover-shine')
    );
  });

  it('applies entrance animation with delay', () => {
    render(
      <AnimatedCard 
        card={mockCard} 
        entranceAnimation="fade" 
        animationDelay={200} 
      />
    );
    
    const cardElement = screen.getByTestId('animated-card');
    expect(cardElement).toHaveClass('entering');
    expect(cardElement).toHaveStyle({ '--animation-delay': '200ms' });
    
    // After delay, should have entered
    act(() => {
      jest.advanceTimersByTime(200);
    });
    
    expect(cardElement).toHaveClass('animate-fade');
  });

  it('skips entrance animation when set to none', () => {
    render(<AnimatedCard card={mockCard} entranceAnimation="none" />);
    
    const cardElement = screen.getByTestId('animated-card');
    expect(cardElement).not.toHaveClass('entering');
  });

  it('applies custom className', () => {
    render(<AnimatedCard card={mockCard} className="custom-class" />);
    expect(screen.getByTestId('animated-card')).toHaveClass('custom-class');
  });

  it('sets correct data attributes', () => {
    render(<AnimatedCard card={mockCard} />);
    expect(screen.getByTestId('animated-card')).toHaveAttribute(
      'data-card', 
      'ACE-HEARTS'
    );
  });
});

describe('AnimatedCardGroup', () => {
  const mockCards: Card[] = [
    { suit: Suit.HEARTS, rank: Rank.ACE },
    { suit: Suit.SPADES, rank: Rank.KING },
    { suit: Suit.DIAMONDS, rank: Rank.QUEEN }
  ];

  const mockOnCardClick = jest.fn();
  const mockOnCardHover = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders without crashing', () => {
    render(<AnimatedCardGroup cards={mockCards} />);
    expect(screen.getByTestId('animated-card-group')).toBeInTheDocument();
  });

  it('renders all cards', () => {
    render(<AnimatedCardGroup cards={mockCards} />);
    
    expect(screen.getByTestId('card-ACE-HEARTS')).toBeInTheDocument();
    expect(screen.getByTestId('card-KING-SPADES')).toBeInTheDocument();
    expect(screen.getByTestId('card-QUEEN-DIAMONDS')).toBeInTheDocument();
  });

  it('applies staggered animation delays', () => {
    render(
      <AnimatedCardGroup 
        cards={mockCards} 
        entranceAnimation="fade"
        staggerDelay={100}
      />
    );
    
    const cardElements = screen.getAllByTestId(/animated-card/);
    
    expect(cardElements[0]).toHaveStyle({ '--animation-delay': '0ms' });
    expect(cardElements[1]).toHaveStyle({ '--animation-delay': '100ms' });
    expect(cardElements[2]).toHaveStyle({ '--animation-delay': '200ms' });
  });

  it('handles card selection correctly', () => {
    const selectedCards = [mockCards[0]];
    
    render(
      <AnimatedCardGroup 
        cards={mockCards} 
        selectedCards={selectedCards}
      />
    );
    
    const cardElements = screen.getAllByTestId(/animated-card/);
    expect(cardElements[0]).toHaveClass('selected');
    expect(cardElements[1]).not.toHaveClass('selected');
    expect(cardElements[2]).not.toHaveClass('selected');
  });

  it('handles card highlighting correctly', () => {
    const highlightedCards = [mockCards[1]];
    
    render(
      <AnimatedCardGroup 
        cards={mockCards} 
        highlightedCards={highlightedCards}
      />
    );
    
    const cardElements = screen.getAllByTestId(/animated-card/);
    expect(cardElements[0]).not.toHaveClass('highlighted');
    expect(cardElements[1]).toHaveClass('highlighted');
    expect(cardElements[2]).not.toHaveClass('highlighted');
  });

  it('forwards click events', () => {
    render(
      <AnimatedCardGroup 
        cards={mockCards} 
        onCardClick={mockOnCardClick}
      />
    );
    
    fireEvent.click(screen.getAllByTestId(/animated-card/)[0]);
    expect(mockOnCardClick).toHaveBeenCalledWith(mockCards[0]);
  });

  it('forwards hover events', () => {
    render(
      <AnimatedCardGroup 
        cards={mockCards} 
        onCardHover={mockOnCardHover}
      />
    );
    
    fireEvent.mouseEnter(screen.getAllByTestId(/animated-card/)[0]);
    expect(mockOnCardHover).toHaveBeenCalledWith(mockCards[0]);
  });

  it('applies custom className', () => {
    render(<AnimatedCardGroup cards={mockCards} className="custom-group" />);
    expect(screen.getByTestId('animated-card-group')).toHaveClass('custom-group');
  });

  it('handles empty cards array', () => {
    render(<AnimatedCardGroup cards={[]} />);
    expect(screen.getByTestId('animated-card-group')).toBeInTheDocument();
    expect(screen.queryByTestId(/animated-card/)).not.toBeInTheDocument();
  });
});

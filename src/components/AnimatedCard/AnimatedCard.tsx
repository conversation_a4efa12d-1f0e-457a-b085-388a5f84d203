/**
 * AnimatedCard component that wraps <PERSON> with smooth animations
 * Provides hover effects, selection animations, and transition states
 */

import React, { useState, useEffect, useRef } from 'react';
import { Card } from '../../types/bridge';
import CardComponent from '../Card/Card';
import { CardHover } from '../CardAnimations/CardAnimations';
import './AnimatedCard.css';

interface AnimatedCardProps {
  card: Card;
  size?: 'small' | 'medium' | 'large';
  isPlayable?: boolean;
  isSelected?: boolean;
  isHighlighted?: boolean;
  isHovered?: boolean;
  onClick?: (card: Card) => void;
  onHover?: (card: Card | null) => void;
  animationDelay?: number;
  entranceAnimation?: 'fade' | 'slide-up' | 'slide-down' | 'slide-left' | 'slide-right' | 'none';
  hoverEnabled?: boolean;
  selectionAnimation?: boolean;
  className?: string;
}

const AnimatedCard: React.FC<AnimatedCardProps> = ({
  card,
  size = 'medium',
  isPlayable = true,
  isSelected = false,
  isHighlighted = false,
  isHovered = false,
  onClick,
  onHover,
  animationDelay = 0,
  entranceAnimation = 'fade',
  hoverEnabled = true,
  selectionAnimation = true,
  className = ''
}) => {
  const [hasEntered, setHasEntered] = useState(false);
  const [isInternalHovered, setIsInternalHovered] = useState(false);
  const [isAnimatingSelection, setIsAnimatingSelection] = useState(false);
  const cardRef = useRef<HTMLDivElement>(null);
  const previousSelected = useRef(isSelected);

  // Handle entrance animation
  useEffect(() => {
    if (entranceAnimation !== 'none') {
      const timer = setTimeout(() => {
        setHasEntered(true);
      }, animationDelay);

      return () => clearTimeout(timer);
    } else {
      setHasEntered(true);
    }
  }, [animationDelay, entranceAnimation]);

  // Handle selection animation
  useEffect(() => {
    if (selectionAnimation && previousSelected.current !== isSelected) {
      setIsAnimatingSelection(true);
      const timer = setTimeout(() => {
        setIsAnimatingSelection(false);
      }, 300);

      previousSelected.current = isSelected;
      return () => clearTimeout(timer);
    }
  }, [isSelected, selectionAnimation]);

  const handleClick = () => {
    if (isPlayable && onClick) {
      onClick(card);
    }
  };

  const handleMouseEnter = () => {
    if (hoverEnabled && isPlayable) {
      setIsInternalHovered(true);
      onHover?.(card);
    }
  };

  const handleMouseLeave = () => {
    if (hoverEnabled) {
      setIsInternalHovered(false);
      onHover?.(null);
    }
  };

  const getCardClasses = () => {
    const classes = [
      'animated-card',
      size,
      className
    ];

    if (!hasEntered && entranceAnimation !== 'none') {
      classes.push('entering');
    }

    if (hasEntered && entranceAnimation !== 'none') {
      classes.push(`animate-${entranceAnimation}`);
    }

    if (isSelected) {
      classes.push('selected');
    }

    if (isHighlighted) {
      classes.push('highlighted');
    }

    if (isInternalHovered || isHovered) {
      classes.push('hovered');
    }

    if (!isPlayable) {
      classes.push('not-playable');
    }

    if (isAnimatingSelection) {
      classes.push('selection-animating');
    }

    return classes.filter(Boolean).join(' ');
  };

  const getAnimationStyle = (): React.CSSProperties => {
    return {
      '--animation-delay': `${animationDelay}ms`,
      animationDelay: `${animationDelay}ms`
    } as React.CSSProperties;
  };

  return (
    <div
      ref={cardRef}
      className={getCardClasses()}
      style={getAnimationStyle()}
      onClick={handleClick}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      data-testid="animated-card"
      data-card={`${card.rank}-${card.suit}`}
    >
      <CardHover
        isEnabled={hoverEnabled && isPlayable}
        hoverScale={size === 'large' ? 1.08 : size === 'small' ? 1.03 : 1.05}
        hoverRotation={size === 'large' ? 3 : 2}
      >
        <CardComponent
          card={card}
          size={size}
          isPlayable={isPlayable}
        />
      </CardHover>

      {/* Selection indicator */}
      {isSelected && (
        <div className="selection-indicator">
          <div className="selection-ring" />
          <div className="selection-checkmark">✓</div>
        </div>
      )}

      {/* Highlight glow */}
      {isHighlighted && (
        <div className="highlight-glow" />
      )}

      {/* Hover shine effect */}
      {(isInternalHovered || isHovered) && hoverEnabled && isPlayable && (
        <div className="hover-shine" />
      )}
    </div>
  );
};

// Animated card group for handling multiple cards with staggered animations
interface AnimatedCardGroupProps {
  cards: Card[];
  size?: 'small' | 'medium' | 'large';
  isPlayable?: boolean;
  selectedCards?: Card[];
  highlightedCards?: Card[];
  onCardClick?: (card: Card) => void;
  onCardHover?: (card: Card | null) => void;
  entranceAnimation?: 'fade' | 'slide-up' | 'slide-down' | 'slide-left' | 'slide-right' | 'none';
  staggerDelay?: number;
  hoverEnabled?: boolean;
  className?: string;
}

export const AnimatedCardGroup: React.FC<AnimatedCardGroupProps> = ({
  cards,
  size = 'medium',
  isPlayable = true,
  selectedCards = [],
  highlightedCards = [],
  onCardClick,
  onCardHover,
  entranceAnimation = 'fade',
  staggerDelay = 100,
  hoverEnabled = true,
  className = ''
}) => {
  const isCardSelected = (card: Card): boolean => {
    return selectedCards.some(c => c.rank === card.rank && c.suit === card.suit);
  };

  const isCardHighlighted = (card: Card): boolean => {
    return highlightedCards.some(c => c.rank === card.rank && c.suit === card.suit);
  };

  return (
    <div className={`animated-card-group ${className}`} data-testid="animated-card-group">
      {cards.map((card, index) => (
        <AnimatedCard
          key={`${card.suit}-${card.rank}-${index}`}
          card={card}
          size={size}
          isPlayable={isPlayable}
          isSelected={isCardSelected(card)}
          isHighlighted={isCardHighlighted(card)}
          onClick={onCardClick}
          onHover={onCardHover}
          animationDelay={index * staggerDelay}
          entranceAnimation={entranceAnimation}
          hoverEnabled={hoverEnabled}
          className="group-card"
        />
      ))}
    </div>
  );
};

export default AnimatedCard;

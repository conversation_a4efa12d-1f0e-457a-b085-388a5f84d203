/**
 * Main GameTable component displaying the bridge game table with four player positions
 * This is the central component that orchestrates the entire game UI
 * OPTIMIZED FOR FULL BROWSER VISIBILITY - All hands visible, status doesn't block content
 */

import React, { useState, useEffect } from 'react';
import { Position, GameState, PlayerType, GamePhase, Bid, BidLevel, BidSuit, SpecialBid } from '../../types/bridge';
import { createNewGame, createPlayers } from '../../lib/bridge/gameStateUtils';
import PlayerPosition from './PlayerPosition';
import CardTable from '../CardTable/CardTable';

import BiddingPanel from '../BiddingPanel/BiddingPanel';
import PlayingArea from '../PlayingArea/PlayingArea';
import './GameTable.css';
import ScoreBoard from '../ScoreBoard/ScoreBoard';

import GameAnimationManager from '../GameAnimationManager/GameAnimationManager';
import SimpleCoach from '../SimpleCoach/SimpleCoach';
import BiddingStatus from '../BiddingStatus/BiddingStatus';
import AuctionHistory from './AuctionHistory';
import GameSettings, { GameSettings as GameSettingsType, DEFAULT_SETTINGS } from '../GameSettings/GameSettings';
import TrickWinnerDisplay from '../TrickWinnerDisplay/TrickWinnerDisplay';
interface GameTableProps {
  /** Optional initial game state for testing */
  initialGameState?: GameState;
  /** Human player position (defaults to South) */
  humanPosition?: Position;
}

const GameTable: React.FC<GameTableProps> = ({ 
  initialGameState,
  humanPosition = Position.SOUTH 
}) => {
  const [gameState, setGameState] = useState<GameState | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isAnimationEnabled, setIsAnimationEnabled] = useState(true);
  const [currentAnimation, setCurrentAnimation] = useState<string | null>(null);

  // Game settings state
  const [gameSettings, setGameSettings] = useState<GameSettingsType>(DEFAULT_SETTINGS);
  const [showSettings, setShowSettings] = useState(false);

  // Trick winner display state
  const [showTrickWinner, setShowTrickWinner] = useState(false);
  const [completedTrick, setCompletedTrick] = useState<any[]>([]);

  // Complete Game History Tracking - All Players
  const [gameHistory, setGameHistory] = useState<Array<{
    id: number;
    timestamp: Date;
    action: {
      type: 'card-play' | 'bid' | 'game-event';
      position: Position;
      player: {
        name: string;
        type: 'HUMAN' | 'AI';
      };
      details: {
        // For card plays
        card?: string;
        cardIndex?: number;
        method?: 'double-click' | 'drag-drop' | 'single-click' | 'coach-recommendation' | 'ai-decision';
        // For bids
        bid?: string;
        // For game events
        event?: string;
      };
    };
    result: {
      success: boolean;
      error?: string;
      gameStateChanged: boolean;
      debugMode: boolean;
    };
    gameState: {
      phase: string;
      currentPlayer: Position;
      currentTrickLength: number;
      tricksCompleted: number;
      handSizes: Record<Position, number>;
      contract?: string;
    };
  }>>([]);

  // Counter for unique IDs to prevent duplicate keys
  const [historyIdCounter, setHistoryIdCounter] = useState(1);

  // Handle animation completion
  const handleAnimationComplete = (animationType: string) => {
    setCurrentAnimation(null);
    console.log(`Animation completed: ${animationType}`);
  };

  // Log any game action to complete history
  const logGameAction = (
    actionType: 'card-play' | 'bid' | 'game-event',
    position: Position,
    details: {
      card?: string;
      cardIndex?: number;
      method?: 'double-click' | 'drag-drop' | 'single-click' | 'coach-recommendation' | 'ai-decision';
      bid?: string;
      event?: string;
    },
    success: boolean = true,
    error?: string,
    gameStateChanged: boolean = false
  ) => {
    if (!gameState) return;

    const player = gameState.players[position];
    const isDebugMode = !!(gameState.contract && gameState.auction.length < 4);

    // Use counter for unique ID to prevent duplicate keys
    const currentId = historyIdCounter;
    setHistoryIdCounter(prev => prev + 1);

    const historyEntry = {
      id: currentId,
      timestamp: new Date(),
      action: {
        type: actionType,
        position,
        player: {
          name: player?.name || `Unknown ${position}`,
          type: player?.type === PlayerType.HUMAN ? 'HUMAN' as const : 'AI' as const
        },
        details
      },
      result: {
        success,
        error,
        gameStateChanged,
        debugMode: isDebugMode
      },
      gameState: {
        phase: gameState.phase,
        currentPlayer: gameState.currentPlayer,
        currentTrickLength: gameState.currentTrick.length,
        tricksCompleted: gameState.tricks.length,
        handSizes: {
          [Position.NORTH]: gameState.players[Position.NORTH]?.hand?.length || 0,
          [Position.EAST]: gameState.players[Position.EAST]?.hand?.length || 0,
          [Position.SOUTH]: gameState.players[Position.SOUTH]?.hand?.length || 0,
          [Position.WEST]: gameState.players[Position.WEST]?.hand?.length || 0,
        } as Record<Position, number>,
        contract: gameState.contract ? `${gameState.contract.level}${gameState.contract.suit}` : undefined
      }
    };

    setGameHistory(prev => [...prev, historyEntry]);

    // Also log to console for immediate feedback
    console.log('📝 GAME HISTORY ENTRY:', historyEntry);
  };

  // Legacy function for backward compatibility
  const logPlayAttempt = (
    position: Position,
    cardIndex: number,
    card: string,
    method: 'double-click' | 'drag-drop' | 'single-click' | 'coach-recommendation',
    success: boolean,
    error?: string,
    gameStateChanged: boolean = false
  ) => {
    logGameAction('card-play', position, { card, cardIndex, method }, success, error, gameStateChanged);
  };

  // Monitor game state changes to detect AI actions
  const previousGameStateRef = React.useRef<GameState | null>(null);

  React.useEffect(() => {
    if (!gameState || !previousGameStateRef.current) {
      previousGameStateRef.current = gameState;
      return;
    }

    const prevState = previousGameStateRef.current;
    const currentState = gameState;

    // Detect new cards played (AI actions)
    if (currentState.currentTrick.length > prevState.currentTrick.length) {
      const newCard = currentState.currentTrick[currentState.currentTrick.length - 1];
      const player = currentState.players[newCard.player];

      // Only log if this was an AI player action (not already logged by human interaction)
      if (player?.type !== PlayerType.HUMAN) {
        logGameAction('card-play', newCard.player, {
          card: `${newCard.card.rank}${newCard.card.suit}`,
          method: 'ai-decision'
        }, true, undefined, true);
      }
    }

    // Detect new bids (AI bids)
    if (currentState.auction.length > prevState.auction.length) {
      const newBid = currentState.auction[currentState.auction.length - 1];
      const player = currentState.players[newBid.player];

      // Only log if this was an AI player action
      if (player?.type !== PlayerType.HUMAN) {
        const bidString = newBid.value === SpecialBid.PASS ? 'PASS' :
                         newBid.value === SpecialBid.DOUBLE ? 'DOUBLE' :
                         newBid.value === SpecialBid.REDOUBLE ? 'REDOUBLE' :
                         typeof newBid.value === 'object' ? `${newBid.value.level}${newBid.value.suit}` : 'UNKNOWN';

        logGameAction('bid', newBid.player, {
          bid: bidString,
          method: 'ai-decision'
        }, true, undefined, true);
      }
    }

    // Detect phase changes
    if (currentState.phase !== prevState.phase) {
      logGameAction('game-event', currentState.currentPlayer, {
        event: `Phase changed from ${prevState.phase} to ${currentState.phase}`
      }, true, undefined, true);
    }

    previousGameStateRef.current = currentState;
  }, [gameState]);

  // Monitor for trick completion
  useEffect(() => {
    if (gameState && gameState.currentTrick.length === 4) {
      console.log('🎯 TRICK COMPLETION DETECTED:', {
        currentTrickLength: gameState.currentTrick.length,
        currentTrick: gameState.currentTrick.map(pc => `${pc.card.rank}${pc.card.suit} (${pc.player})`),
        showTrickWinner,
        completedTrick: completedTrick.map(pc => `${pc.card.rank}${pc.card.suit} (${pc.player})`)
      });

      // Trick is complete - show winner display
      setCompletedTrick([...gameState.currentTrick]);
      setShowTrickWinner(true);
    }
  }, [gameState?.currentTrick]);

  // Handle trick winner display completion
  const handleTrickWinnerComplete = async () => {
    console.log('🏆 handleTrickWinnerComplete called', {
      currentTrickLength: gameState?.currentTrick.length,
      showTrickWinner
    });

    setShowTrickWinner(false);
    setCompletedTrick([]);

    // Complete the trick after the display
    if (gameState && gameState.currentTrick.length >= 4) {
      try {
        const { completeTrick } = await import('../../lib/bridge/gameStateUtils');
        console.log('🏆 Calling completeTrick...');
        const newGameState = completeTrick(gameState);
        console.log('🏆 Trick completed successfully, updating game state', {
          newPhase: newGameState.phase,
          tricksCompleted: newGameState.tricks.length
        });
        setGameState(newGameState);

        // Check if hand is complete (moved to SCORING phase)
        if (newGameState.phase === 'scoring') {
          console.log('🏆 Hand complete! Moving to scoring phase...');
          // Auto-process scoring after a brief delay
          setTimeout(async () => {
            try {
              const { processScoring, autoTransitionPhase } = await import('../../lib/bridge/gameStateUtils');
              console.log('🏆 Processing scoring...');
              const scoredState = processScoring(newGameState);
              console.log('🏆 Scoring complete, auto-transitioning...');
              const finalState = autoTransitionPhase(scoredState);
              console.log('🏆 Final state:', { phase: finalState.phase });
              setGameState(finalState);

              // Start new hand after another delay
              setTimeout(() => {
                console.log('🏆 Starting new hand...');
                startNewHand();
              }, 2000);
            } catch (error) {
              console.error('Error processing scoring:', error);
            }
          }, 1000);
        }
      } catch (error) {
        console.error('Error completing trick:', error);
      }
    } else {
      console.warn('🏆 Cannot complete trick - not enough cards:', gameState?.currentTrick.length);
    }
  };

  // Start a new hand after the previous one is complete
  const startNewHand = async () => {
    try {
      console.log('🆕 Starting new hand...');
      const { createPlayers, createNewGame } = await import('../../lib/bridge/gameStateUtils');

      // Create new game with same human position
      const players = createPlayers(humanPosition, 'Human Player');
      const newGameState = createNewGame('game-' + Date.now(), players, Position.SOUTH);

      console.log('🆕 New hand created:', {
        gameId: newGameState.id,
        phase: newGameState.phase,
        dealer: newGameState.dealer,
        currentPlayer: newGameState.currentPlayer
      });

      setGameState(newGameState);

      // Reset UI state
      setCompletedTrick([]);
      setShowTrickWinner(false);

    } catch (error) {
      console.error('Error starting new hand:', error);
    }
  };

  const handleBid = (bid: Bid) => {
    if (!gameState) return;

    try {
      import('../../lib/bridge/gameStateUtils').then(({ processBid, validateGameState }) => {
        // Log the bid attempt
        const bidString = bid.value === SpecialBid.PASS ? 'PASS' :
                         bid.value === SpecialBid.DOUBLE ? 'DOUBLE' :
                         bid.value === SpecialBid.REDOUBLE ? 'REDOUBLE' :
                         typeof bid.value === 'object' ? `${bid.value.level}${bid.value.suit}` : 'UNKNOWN';

        const player = gameState.players[bid.player];
        const method = player?.type === PlayerType.HUMAN ? 'coach-recommendation' : 'ai-decision';

        logGameAction('bid', bid.player, { bid: bidString, method }, true, undefined, true);

        const newGameState = processBid(gameState, bid);

        // Validate the new game state
        const validation = validateGameState(newGameState);
        if (!validation.isValid) {
          console.error('Invalid game state after bid:', validation.errors);
          logGameAction('bid', bid.player, { bid: bidString, method }, false, validation.errors.join(', '), false);
          return;
        }

        setGameState(newGameState);
      });
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : 'Unknown error';
      console.error('Error processing bid:', error);
      logGameAction('bid', bid.player, { bid: 'ERROR' }, false, errorMsg, false);
    }
  };

  // Initialize game state
  useEffect(() => {
    const initializeGame = async () => {
      try {
        setIsLoading(true);
        
        if (initialGameState) {
          setGameState(initialGameState);
        } else {
          const players = createPlayers(humanPosition, 'Human Player');
          const newGameState = createNewGame('game-' + Date.now(), players, Position.SOUTH);
          setGameState(newGameState);
        }
      } catch (error) {
        console.error('Failed to initialize game:', error);
      } finally {
        setIsLoading(false);
      }
    };

    initializeGame();
  }, [initialGameState, humanPosition]);

  // AI automation - handle AI player turns
  useEffect(() => {
    if (!gameState || isLoading) return;
    
    const currentPlayer = gameState.currentPlayer;
    const currentPlayerType = gameState.players[currentPlayer].type;
    
    // Only automate AI players
    if (currentPlayerType === PlayerType.AI) {
      const aiDelay = 1500; // 1.5 second delay for AI moves
      
      const timer = setTimeout(async () => {
        try {
          const { AIPlayer } = await import("../../lib/bridge/aiPlayer");
          
          if (gameState.phase === GamePhase.BIDDING) {
            // AI makes a bid
            const aiPlayer = new AIPlayer(currentPlayer);
            const aiBid = aiPlayer.makeBid(gameState.players[currentPlayer].hand, gameState.auction);
            if (aiBid) {
              const fullBid: Bid = {
                player: currentPlayer,
                value: aiBid,
                timestamp: new Date()
              };
              handleBid(fullBid);
            }
          } else if (gameState.phase === GamePhase.PLAYING) {
            // AI plays a card
            const aiPlayer = new AIPlayer(currentPlayer);
            const aiCard = aiPlayer.playCard(gameState);
            if (aiCard) {
              // Import and use playCard function
              const { playCard } = await import("../../lib/bridge/gameStateUtils");
              const newGameState = playCard(gameState, aiCard, currentPlayer);
              setGameState(newGameState);
            }
          }
        } catch (error) {
          console.error(`AI ${currentPlayer} error:`, error);
        }
      }, aiDelay);
      
      return () => clearTimeout(timer);
    }
  }, [gameState, isLoading]);
  // Loading state
  if (isLoading) {
    return (
      <div className="game-table loading">
        <div className="loading-spinner">
          <div className="spinner"></div>
          <p>Setting up the bridge table...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (!gameState) {
    return (
      <div className="game-table error">
        <div className="error-message">
          <h2>Failed to load game</h2>
          <p>There was an error setting up the bridge table. Please refresh the page.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="game-table" data-testid="game-table">

      {/* Simplified Card Table - Replaces complex center area and individual player positions */}
      <div className="center-area">
        <CardTable
          gameState={gameState}
          openHandedMode={gameSettings.openHandedPlay}
          onLogPlayAttempt={logPlayAttempt}
          onCardPlay={async (position, cardIndex) => {
            // Use proper card play logic from gameStateUtils
            const player = gameState.players[position];
            const cardToPlay = player?.hand?.[cardIndex];
            const cardString = cardToPlay ? `${cardToPlay.rank}${cardToPlay.suit}` : 'INVALID INDEX';

            console.log('🎯 CARD PLAY ATTEMPT:', {
              position,
              cardIndex,
              playerHand: player?.hand?.map(c => `${c.rank}${c.suit}`),
              cardToPlay: cardString
            });

            try {
              if (player?.hand && cardIndex < player.hand.length) {
                const playedCard = player.hand[cardIndex];
                console.log('✅ Playing card:', `${playedCard.rank}${playedCard.suit}`, 'at index:', cardIndex);

                // In debug mode, use simplified card play
                const isDebugMode = gameState.contract && gameState.auction.length < 4;
                if (isDebugMode) {
                  console.log('🎯 DEBUG MODE: Using simplified card play');
                  // Create a simplified card play for debug mode
                  const newGameState = { ...gameState };

                  // Deep clone players to avoid mutation
                  newGameState.players = { ...gameState.players };
                  newGameState.players[position] = {
                    ...player,
                    hand: player.hand.filter((_, index) => index !== cardIndex)
                  };

                  // Add to current trick
                  newGameState.currentTrick = [
                    ...gameState.currentTrick,
                    { card: playedCard, player: position }
                  ];

                  console.log('✅ DEBUG MODE: Card played successfully, new trick:',
                    newGameState.currentTrick.map(pc => `${pc.card.rank}${pc.card.suit} (${pc.player})`));

                  // Check if trick is complete in debug mode
                  if (newGameState.currentTrick.length === 4) {
                    console.log('🎯 DEBUG MODE: Trick complete, showing winner display');
                    setCompletedTrick(newGameState.currentTrick);
                    setShowTrickWinner(true);
                  }

                  // Log successful play
                  logPlayAttempt(position, cardIndex, cardString, 'drag-drop', true, undefined, true);
                  setGameState(newGameState);
                  return;
                }

                // Import and use the proper playCard function
                const { playCard, validateGameState } = await import('../../lib/bridge/gameStateUtils');
                const newGameState = playCard(gameState, playedCard, position);

                // Validate the new game state
                const validation = validateGameState(newGameState);
                if (!validation.isValid) {
                  console.error('Invalid game state after card play:', validation.errors);
                  logPlayAttempt(position, cardIndex, cardString, 'drag-drop', false, 'Invalid game state after play', false);
                  return; // Don't update state if invalid
                }

                // Check if trick is complete (4 cards) - show winner before completing
                if (newGameState.currentTrick.length === 4) {
                  setCompletedTrick(newGameState.currentTrick);
                  setShowTrickWinner(true);
                }

                // Log successful play
                logPlayAttempt(position, cardIndex, cardString, 'drag-drop', true, undefined, true);
                setGameState(newGameState);
              } else {
                const errorMsg = `Invalid card index ${cardIndex} for hand size ${player?.hand?.length || 0}`;
                console.error('❌ Invalid card play attempt:', {
                  hasPlayer: !!player,
                  hasHand: !!player?.hand,
                  handLength: player?.hand?.length || 0,
                  cardIndex,
                  isValidIndex: cardIndex < (player?.hand?.length || 0)
                });
                logPlayAttempt(position, cardIndex, cardString, 'drag-drop', false, errorMsg, false);
              }
            } catch (error) {
              const errorMsg = error instanceof Error ? error.message : 'Unknown error';
              console.error('❌ Error playing card:', error);
              logPlayAttempt(position, cardIndex, cardString, 'drag-drop', false, errorMsg, false);
            }
          }}
          humanPosition={humanPosition}
        />
      </div>

      {/* Left Panel - Auction History and Bidding Status */}
      <div className="left-panel">
        {/* Auction History Section */}
        <div className="left-panel-section auction-history">
          <AuctionHistory
            auction={gameState.auction}
            currentPlayer={gameState.currentPlayer}
          />
        </div>

        {/* Bidding Status Section */}
        <div className="left-panel-section bidding-status">
          <BiddingStatus gameState={gameState} />
        </div>
      </div>

      {/* Right Panel - Bidding Controls and Coach */}
      <div className="right-panel">
        {/* Bidding Panel Section - Only show during bidding phase */}
        {gameState.phase === GamePhase.BIDDING && (
          <div className="right-panel-section bidding-panel">
            <BiddingPanel
              gameState={gameState}
              currentPlayer={gameState.currentPlayer}
              onBid={handleBid}
              isPlayerTurn={gameState.players[gameState.currentPlayer].type === PlayerType.HUMAN}
            />
          </div>
        )}

        {/* Bridge Coach Section */}
        <div className="right-panel-section coach">
          <SimpleCoach
            onGameStateChange={setGameState}
            gameState={gameState}
            humanPosition={humanPosition}
          />
        </div>
      </div>

      {/* Game Animation Manager */}
      <GameAnimationManager
        gameState={gameState}
        onAnimationComplete={handleAnimationComplete}
        isAnimationEnabled={isAnimationEnabled}
      />

      {/* Debug Info */}
      <div
        style={{
          position: 'fixed',
          top: '10px',
          left: '10px',
          zIndex: 1000,
          background: 'rgba(0,0,0,0.8)',
          color: 'white',
          padding: '8px',
          borderRadius: '4px',
          fontSize: '11px',
          fontFamily: 'monospace'
        }}
      >
        Phase: {gameState.phase}<br/>
        Current: {gameState.currentPlayer}<br/>
        Contract: {gameState.contract ? `${gameState.contract.level}${gameState.contract.suit}` : 'None'}<br/>
        Dummy: {gameState.dummy || 'None'}<br/>
        Game Actions: {gameHistory.length}
      </div>

      {/* Complete Game History Display */}
      {gameHistory.length > 0 && (
        <div
          style={{
            position: 'fixed',
            bottom: '10px',
            left: '10px',
            zIndex: 1000,
            background: 'rgba(0,0,0,0.9)',
            color: 'white',
            padding: '12px',
            borderRadius: '6px',
            fontSize: '10px',
            fontFamily: 'monospace',
            maxWidth: '500px',
            maxHeight: '300px',
            overflowY: 'auto'
          }}
        >
          <div style={{ fontWeight: 'bold', marginBottom: '8px' }}>
            📝 Complete Game History ({gameHistory.length} actions)
          </div>
          {gameHistory.slice(-8).map((entry) => (
            <div key={entry.id} style={{
              marginBottom: '4px',
              padding: '4px',
              background: entry.result.success ? 'rgba(0,255,0,0.1)' : 'rgba(255,0,0,0.1)',
              borderRadius: '3px',
              borderLeft: `3px solid ${entry.action.player.type === 'HUMAN' ? '#4CAF50' : '#2196F3'}`
            }}>
              <div>
                #{entry.id} {entry.timestamp.toLocaleTimeString()} -
                {entry.result.success ? ' ✅' : ' ❌'}
                <span style={{
                  color: entry.action.player.type === 'HUMAN' ? '#4CAF50' : '#2196F3',
                  fontWeight: 'bold'
                }}>
                  {entry.action.player.name}
                </span>
                {entry.action.type === 'card-play' && (
                  <span> played {entry.action.details.card} ({entry.action.details.method})</span>
                )}
                {entry.action.type === 'bid' && (
                  <span> bid {entry.action.details.bid}</span>
                )}
                {entry.action.type === 'game-event' && (
                  <span> {entry.action.details.event}</span>
                )}
              </div>
              {entry.result.error && (
                <div style={{ color: '#ff6b6b', fontSize: '9px' }}>
                  Error: {entry.result.error}
                </div>
              )}
              <div style={{ fontSize: '9px', color: '#aaa' }}>
                {entry.result.debugMode ? 'DEBUG' : 'NORMAL'} |
                {entry.action.type === 'card-play' && `Trick: ${entry.gameState.currentTrickLength} cards | `}
                Phase: {entry.gameState.phase} |
                Hands: N:{entry.gameState.handSizes[Position.NORTH]} E:{entry.gameState.handSizes[Position.EAST]} S:{entry.gameState.handSizes[Position.SOUTH]} W:{entry.gameState.handSizes[Position.WEST]}
              </div>
            </div>
          ))}
          {gameHistory.length > 8 && (
            <div style={{ fontSize: '9px', color: '#888', textAlign: 'center' }}>
              ... showing last 8 of {gameHistory.length} actions
            </div>
          )}
        </div>
      )}

      {/* Debug: Skip to Playing Phase Button - Only show in development */}
      {process.env.NODE_ENV === 'development' && gameState.phase === GamePhase.BIDDING && (
        <button
          className="debug-button"
          onClick={() => {
            // Force transition to playing phase for testing
            const newGameState = { ...gameState };
            newGameState.phase = GamePhase.PLAYING;
            newGameState.contract = {
              level: BidLevel.THREE,
              suit: BidSuit.NO_TRUMP,
              declarer: Position.SOUTH,
              doubled: 'none'
            };
            newGameState.dummy = Position.NORTH;
            newGameState.currentPlayer = Position.WEST; // Opening leader (left of declarer)
            setGameState(newGameState);
          }}
          style={{
            position: 'fixed',
            top: '10px',
            right: '60px',
            zIndex: 1000,
            background: '#ff6b6b',
            color: 'white',
            border: 'none',
            padding: '8px 12px',
            borderRadius: '4px',
            fontSize: '12px'
          }}
          title="Skip bidding and start playing (DEBUG)"
        >
          🎯 Skip to Play
        </button>
      )}

      {/* Settings Button */}
      <button
        className="settings-button"
        onClick={() => setShowSettings(true)}
        title="Game Settings"
        aria-label="Open game settings"
      >
        ⚙️
      </button>

      {/* Game Settings Modal */}
      <GameSettings
        settings={gameSettings}
        onSettingsChange={setGameSettings}
        onClose={() => setShowSettings(false)}
        isOpen={showSettings}
      />

      {/* Debug: Manual trick completion button */}
      {process.env.NODE_ENV === 'development' && gameState && gameState.currentTrick.length >= 4 && (
        <div style={{ position: 'fixed', top: '10px', right: '10px', zIndex: 2000 }}>
          <button
            onClick={handleTrickWinnerComplete}
            style={{
              background: 'red',
              color: 'white',
              padding: '10px',
              border: 'none',
              borderRadius: '5px',
              cursor: 'pointer',
              fontSize: '12px'
            }}
          >
            🚨 Complete Trick ({gameState.currentTrick.length} cards)
          </button>
        </div>
      )}

      {/* Debug: Manual new hand button */}
      {process.env.NODE_ENV === 'development' && gameState && (gameState.phase === 'scoring' || gameState.phase === 'complete') && (
        <div style={{ position: 'fixed', top: '60px', right: '10px', zIndex: 2000 }}>
          <button
            onClick={startNewHand}
            style={{
              background: 'green',
              color: 'white',
              padding: '10px',
              border: 'none',
              borderRadius: '5px',
              cursor: 'pointer',
              fontSize: '12px'
            }}
          >
            🆕 Start New Hand (Debug)
          </button>
        </div>
      )}

      {/* Score Board - positioned to not block Coach */}
      {gameState && (
        <div style={{ position: 'fixed', bottom: '20px', left: '20px', zIndex: 900 }}>
          <ScoreBoard
            gameState={gameState}
            humanPosition={humanPosition}
          />
        </div>
      )}

      {/* Trick Winner Display */}
      <TrickWinnerDisplay
        trick={completedTrick}
        contract={gameState?.contract || null}
        onComplete={handleTrickWinnerComplete}
        visible={showTrickWinner}
      />
    </div>
  );
};

export default GameTable;

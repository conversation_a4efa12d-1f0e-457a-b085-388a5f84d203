/**
 * Tests for AuctionHistory component
 */

import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import AuctionHistory from './AuctionHistory';
import { Position, BidLevel, BidSuit, SpecialBid } from '../../types/bridge';

describe('AuctionHistory', () => {
  it('renders auction header and position labels', () => {
    render(<AuctionHistory auction={[]} currentPlayer={Position.SOUTH} />);

    expect(screen.getByText('Auction')).toBeInTheDocument();
    expect(screen.getAllByText('N')).toHaveLength(1);
    expect(screen.getAllByText('E')).toHaveLength(1);
    expect(screen.getAllByText('S')).toHaveLength(3); // Header, no-bids, summary
    expect(screen.getAllByText('W')).toHaveLength(1);
  });

  it('shows starting message when no bids', () => {
    render(<AuctionHistory auction={[]} currentPlayer={Position.SOUTH} />);

    expect(screen.getByText('🎯 Auction starting...')).toBeInTheDocument();
    expect(screen.getByText('Current bidder:')).toBeInTheDocument();
    expect(screen.getAllByText('S')).toHaveLength(3); // Header, no-bids, summary
  });

  it('shows auction summary with correct information', () => {
    render(<AuctionHistory auction={[]} currentPlayer={Position.NORTH} />);

    expect(screen.getByText('Total Bids:')).toBeInTheDocument();
    expect(screen.getByText('0')).toBeInTheDocument();
    expect(screen.getByText('Current Turn:')).toBeInTheDocument();
    expect(screen.getAllByText('N')).toHaveLength(3); // Header, no-bids, summary
  });

  it('displays bids with proper suit symbols', () => {
    const auction = [
      {
        player: Position.NORTH,
        value: { level: BidLevel.ONE, suit: BidSuit.CLUBS },
        timestamp: new Date()
      },
      {
        player: Position.EAST,
        value: SpecialBid.PASS,
        timestamp: new Date()
      },
      {
        player: Position.SOUTH,
        value: { level: BidLevel.ONE, suit: BidSuit.HEARTS },
        timestamp: new Date()
      }
    ];

    render(<AuctionHistory auction={auction} currentPlayer={Position.WEST} />);

    expect(screen.getAllByText('1♣')).toHaveLength(1);
    expect(screen.getAllByText('Pass')).toHaveLength(1);
    expect(screen.getAllByText('1♥')).toHaveLength(2); // In table and summary (last bid)
  });

  it('shows last bid in summary when bids exist', () => {
    const auction = [
      {
        player: Position.NORTH,
        value: { level: BidLevel.TWO, suit: BidSuit.SPADES },
        timestamp: new Date()
      }
    ];

    render(<AuctionHistory auction={auction} currentPlayer={Position.EAST} />);

    expect(screen.getByText('Last Bid:')).toBeInTheDocument();
    expect(screen.getAllByText('2♠')).toHaveLength(2); // In table and summary
    expect(screen.getByText('1')).toBeInTheDocument(); // Total bids
  });

  it('highlights current player position', () => {
    render(<AuctionHistory auction={[]} currentPlayer={Position.EAST} />);

    const eastHeaders = screen.getAllByText('E');
    expect(eastHeaders[0]).toHaveClass('current'); // The position header
  });

  it('handles special bids correctly', () => {
    const auction = [
      {
        player: Position.NORTH,
        value: SpecialBid.DOUBLE,
        timestamp: new Date()
      },
      {
        player: Position.EAST,
        value: SpecialBid.REDOUBLE,
        timestamp: new Date()
      }
    ];

    render(<AuctionHistory auction={auction} currentPlayer={Position.SOUTH} />);

    expect(screen.getAllByText('X')).toHaveLength(1); // Double
    expect(screen.getAllByText('XX')).toHaveLength(2); // Redouble (table + summary)
  });

  it('shows no trump bids correctly', () => {
    const auction = [
      {
        player: Position.SOUTH,
        value: { level: BidLevel.THREE, suit: BidSuit.NO_TRUMP },
        timestamp: new Date()
      }
    ];

    render(<AuctionHistory auction={auction} currentPlayer={Position.WEST} />);

    expect(screen.getAllByText('3NT')).toHaveLength(2); // In table and summary
  });
});

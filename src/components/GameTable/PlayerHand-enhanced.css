/**
 * Enhanced PlayerHand component styles
 * Optimized card layout and spacing for all positions
 */

/* Base player hand container */
.player-hand {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  padding: 4px;
  box-sizing: border-box;
  overflow: visible;
}

/* Horizontal hands (North and South) */
.player-hand.horizontal {
  flex-direction: row;
  align-items: center;
  justify-content: center;
  min-height: 80px;
  max-height: 120px;
  margin: auto; /* Center the container within its grid area */
}

/* South hand specific styling */
.player-hand.horizontal.south {
  min-height: 140px; /* Extra height for human player hand */
  max-height: 160px;
  padding: 10px 0;
}

/* Vertical hands (East and West) - use two rows layout */
.player-hand.vertical {
  display: flex;
  flex-direction: column; /* Stack rows vertically */
  align-items: center;
  justify-content: center;
  gap: 8px; /* Space between rows */
  transform-origin: center center; /* Explicit center point for rotation */
  height: 200px; /* Reduced height for two-row layout */
  width: 180px; /* Increased width to accommodate two rows */
  overflow: visible; /* Ensure cards are visible */
  position: relative; /* For better positioning control */
  margin: auto; /* Center the container within its grid area */
}

/* East hand should be rotated 90 degrees clockwise */
.player-hand.vertical.east {
  transform: rotate(90deg); /* 90 degrees clockwise for East */
  transform-origin: center center;
}

/* West hand should be rotated 270 degrees (or -90 degrees) */
.player-hand.vertical.west {
  transform: rotate(-90deg); /* 270 degrees clockwise = -90 degrees for West */
  transform-origin: center center;
}

/* Card rows for vertical hands */
.player-hand.vertical .card-row {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}

/* Cards in vertical hands use horizontal spacing within each row */
.player-hand.vertical .card-container {
  margin-left: calc(-1 * clamp(8px, 1.5vw, 12px)); /* Reduced overlap for two-row layout */
}

.player-hand.vertical .card-container:first-child {
  margin-left: 0;
}

/* Card container with dynamic spacing */
.card-container {
  position: relative;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 1;
}

/* Horizontal card spacing - overlapping for space efficiency */
.player-hand.horizontal .card-container {
  margin-left: calc(-1 * clamp(12px, 2.5vw, 18px)); /* Consistent with vertical hands */
}

.player-hand.horizontal .card-container:first-child {
  margin-left: 0;
}

/* Remove old vertical spacing rule - now using horizontal spacing for vertical hands */

/* Interactive states */
.card-container.interactive {
  cursor: pointer;
}

.card-container.playable {
  z-index: 10;
}

.card-container.playable:hover {
  z-index: 100;
}

/* Hover effects for horizontal hands */
.player-hand.horizontal .card-container.playable:hover {
  transform: translateY(-8px) scale(1.05);
}

/* Hover effects for vertical hands - since the whole container is rotated, use Y translation */
.player-hand.vertical .card-container.playable:hover {
  transform: translateY(-8px) scale(1.05);
}

/* Selected card states */
.card-container.selected {
  z-index: 50;
}

.player-hand.horizontal .card-container.selected {
  transform: translateY(-12px) scale(1.08);
}

.player-hand.vertical .card-container.selected {
  transform: translateX(-12px) scale(1.08);
}

/* Individual card transforms removed - entire container is rotated */

/* Hidden hands (card backs) */
.player-hand.hidden {
  opacity: 0.8;
}

.player-hand.hidden .card-back {
  cursor: default;
}

/* Dynamic spacing based on number of cards */
.player-hand[data-card-count="13"] .card-container {
  /* Full hand - maximum overlap */
}

.player-hand[data-card-count="12"] .card-container,
.player-hand[data-card-count="11"] .card-container,
.player-hand[data-card-count="10"] .card-container {
  /* Slightly less overlap as cards are played */
}

.player-hand.horizontal[data-card-count="9"] .card-container,
.player-hand.horizontal[data-card-count="8"] .card-container,
.player-hand.horizontal[data-card-count="7"] .card-container {
  margin-left: calc(-1 * clamp(12px, 2.5vw, 20px));
}

.player-hand.horizontal[data-card-count="6"] .card-container,
.player-hand.horizontal[data-card-count="5"] .card-container,
.player-hand.horizontal[data-card-count="4"] .card-container {
  margin-left: calc(-1 * clamp(8px, 2vw, 15px));
}

.player-hand.horizontal[data-card-count="3"] .card-container,
.player-hand.horizontal[data-card-count="2"] .card-container,
.player-hand.horizontal[data-card-count="1"] .card-container {
  margin-left: calc(-1 * clamp(4px, 1vw, 8px));
}

/* Vertical spacing adjustments for different card counts */
.player-hand.vertical[data-card-count="9"] .card-container,
.player-hand.vertical[data-card-count="8"] .card-container,
.player-hand.vertical[data-card-count="7"] .card-container {
  margin-top: calc(-1 * clamp(30px, 5vw, 45px));
}

.player-hand.vertical[data-card-count="6"] .card-container,
.player-hand.vertical[data-card-count="5"] .card-container,
.player-hand.vertical[data-card-count="4"] .card-container {
  margin-top: calc(-1 * clamp(25px, 4vw, 35px));
}

.player-hand.vertical[data-card-count="3"] .card-container,
.player-hand.vertical[data-card-count="2"] .card-container,
.player-hand.vertical[data-card-count="1"] .card-container {
  margin-top: calc(-1 * clamp(20px, 3vw, 25px));
}

/* Position-specific optimizations */
.player-position.north .player-hand {
  justify-content: center;
  align-items: flex-end;
}

.player-position.south .player-hand {
  justify-content: center;
  align-items: flex-start;
}

.player-position.east .player-hand {
  justify-content: center; /* Center the rotated hand */
  align-items: center;
}

.player-position.west .player-hand {
  justify-content: center; /* Center the rotated hand */
  align-items: center;
}

/* Card back styling for hidden hands */
.card-back {
  position: relative;
  width: 60px;
  height: 84px;
  background: linear-gradient(145deg, #1a4c96 0%, #0f3a7a 100%);
  border: 2px solid #0a2d5c;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow:
    0 4px 12px rgba(0, 0, 0, 0.25),
    0 2px 4px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  transition: all 0.2s ease;
  margin: 2px;
  cursor: pointer;
  min-width: 60px;
  min-height: 84px;
}

/* Card back sizing for different positions */
.player-hand.horizontal .card-back {
  width: 50px;
  height: 70px;
}

.player-hand.vertical .card-back {
  width: 45px;
  height: 63px;
}

/* South position (your hand) gets larger card backs when visible */
.player-position-south .card-back {
  width: 55px;
  height: 77px;
}

.card-back-design {
  color: #ffd700;
  font-size: clamp(18px, 3.5vw, 24px);
  text-shadow:
    0 2px 4px rgba(0, 0, 0, 0.8),
    0 0 8px rgba(255, 215, 0, 0.6);
  user-select: none;
  font-weight: bold;
  text-align: center;
  line-height: 1;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
}

/* Card back hover effect */
.card-back:hover {
  transform: translateY(-2px);
  box-shadow:
    0 4px 8px rgba(0, 0, 0, 0.4),
    0 2px 4px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

/* Ensure card backs are visible in hidden hands */
.player-hand.hidden {
  display: flex;
  padding: 8px;
  min-height: 100px;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 8px;
}

.player-hand.hidden.horizontal {
  flex-direction: row;
  flex-wrap: nowrap;
  justify-content: center;
  overflow-x: auto;
}

.player-hand.hidden.vertical {
  flex-direction: column; /* Stack rows vertically like visible hands */
  align-items: center;
  justify-content: center;
  gap: 8px; /* Space between rows */
  transform-origin: center center; /* Explicit center point for rotation */
  height: 200px; /* Match visible hands */
  width: 180px; /* Match visible hands */
  overflow: visible;
  position: relative; /* For better positioning control */
}

/* East hidden hand should be rotated 90 degrees clockwise */
.player-hand.hidden.vertical.east {
  transform: rotate(90deg); /* 90 degrees clockwise for East */
  transform-origin: center center;
}

/* West hidden hand should be rotated 270 degrees (or -90 degrees) */
.player-hand.hidden.vertical.west {
  transform: rotate(-90deg); /* 270 degrees clockwise = -90 degrees for West */
  transform-origin: center center;
}

/* Override spacing for hidden hands to show overlapping cards */
.player-hand.hidden.horizontal .card-back {
  margin-left: calc(-1 * clamp(15px, 3vw, 25px));
}

.player-hand.hidden.horizontal .card-back:first-child {
  margin-left: 0;
}

/* Card rows for hidden vertical hands */
.player-hand.hidden.vertical .card-row {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}

/* Hidden vertical hands use horizontal spacing like visible hands */
.player-hand.hidden.vertical .card-back {
  margin-left: calc(-1 * clamp(8px, 1.5vw, 12px)); /* Match visible hands spacing */
}

.player-hand.hidden.vertical .card-back:first-child {
  margin-left: 0;
}

/* West hidden hand should be rotated 270 degrees */
.player-hand.hidden.vertical.west {
  transform: rotate(-90deg); /* 270 degrees clockwise = -90 degrees */
}

/* Responsive design */
@media (max-width: 1024px) {
  .player-hand.horizontal {
    min-height: 70px;
    max-height: 100px;
  }
  
  .player-hand.vertical {
    height: 180px; /* Adjust height for smaller screens */
    width: 160px;
  }
  
  .player-hand.horizontal .card-container {
    margin-left: calc(-1 * clamp(12px, 2.5vw, 20px));
  }
  
  .player-hand.vertical .card-container {
    margin-left: calc(-1 * clamp(12px, 2.5vw, 20px)); /* Use horizontal spacing for rotated container */
  }
}

@media (max-width: 768px) {
  .player-hand.horizontal {
    min-height: 60px;
    max-height: 80px;
  }
  
  .player-hand.vertical {
    height: 160px; /* Adjust height for tablet screens */
    width: 140px;
  }
  
  .player-hand.horizontal .card-container {
    margin-left: calc(-1 * clamp(10px, 2vw, 15px));
  }
  
  .player-hand.vertical .card-container {
    margin-left: calc(-1 * clamp(10px, 2vw, 15px)); /* Use horizontal spacing for rotated container */
  }
  
  .player-hand.horizontal .card-container.playable:hover {
    transform: translateY(-6px) scale(1.03);
  }
  
  .player-hand.vertical .card-container.playable:hover {
    transform: translateY(-6px) scale(1.03);
  }
}

@media (max-width: 480px) {
  .player-hand.horizontal {
    min-height: 50px;
    max-height: 70px;
  }
  
  .player-hand.vertical {
    height: 140px; /* Adjust height for mobile screens */
    width: 120px;
  }
  
  .player-hand.horizontal .card-container {
    margin-left: calc(-1 * clamp(8px, 1.5vw, 12px));
  }
  
  .player-hand.vertical .card-container {
    margin-left: calc(-1 * clamp(8px, 1.5vw, 12px)); /* Use horizontal spacing for rotated container */
  }
  
  .player-hand.horizontal .card-container.playable:hover {
    transform: translateY(-4px) scale(1.02);
  }
  
  .player-hand.vertical .card-container.playable:hover {
    transform: translateY(-4px) scale(1.02);
  }
}

/* Animation for card dealing */
@keyframes dealToPosition {
  from {
    opacity: 0;
    transform: scale(0.8) rotate(-5deg);
  }
  to {
    opacity: 1;
    transform: scale(1) rotate(0deg);
  }
}

.card-container.dealing {
  animation: dealToPosition 0.4s ease-out;
}

/* Accessibility improvements */
.card-container:focus-within {
  outline: 2px solid #007bff;
  outline-offset: 2px;
  border-radius: 4px;
}

.card-container.playable:focus-within {
  outline-color: #28a745;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .card-back {
    border-width: 2px;
    border-color: #ffffff;
  }
  
  .card-back-design {
    color: #ffffff;
    text-shadow: 0 0 2px #000000;
  }
}

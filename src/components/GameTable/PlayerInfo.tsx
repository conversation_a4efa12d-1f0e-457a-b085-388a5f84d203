/**
 * PlayerInfo component displaying player name, type, and status
 */

import React from 'react';
import { Player, Position, PlayerType, GamePhase } from '../../types/bridge';

interface PlayerInfoProps {
  player: Player;
  position: Position;
  isCurrentPlayer: boolean;
  isDummy: boolean;
  gamePhase: GamePhase;
}

const PlayerInfo: React.FC<PlayerInfoProps> = ({
  player,
  position,
  isCurrentPlayer,
  isDummy,
  gamePhase
}) => {
  const getPlayerTypeIcon = (type: PlayerType) => {
    switch (type) {
      case PlayerType.HUMAN:
        return '👤';
      case PlayerType.AI:
        return '🤖';
      default:
        return '❓';
    }
  };

  const getPositionLabel = (pos: Position) => {
    switch (pos) {
      case Position.NORTH:
        return 'N';
      case Position.SOUTH:
        return 'S';
      case Position.EAST:
        return 'E';
      case Position.WEST:
        return 'W';
      default:
        return '?';
    }
  };

  return (
    <div className="player-info" data-testid={`player-info-${position.toLowerCase()}`}>
      <div className="player-header">
        <div className="position-indicator">
          {getPositionLabel(position)}
        </div>
        <div className="player-name">
          {player.name}
        </div>
        <div className="player-type">
          {getPlayerTypeIcon(player.type)}
        </div>
      </div>
      
      <div className="player-status">
        {isCurrentPlayer && (
          <span className="status-badge current">
            {gamePhase === GamePhase.BIDDING ? 'Bidding' : 'Playing'}
          </span>
        )}
        {isDummy && (
          <span className="status-badge dummy">
            Dummy
          </span>
        )}
      </div>

      <div className="card-count">
        {player.hand.length} cards
      </div>
    </div>
  );
};

export default PlayerInfo;

/**
 * Unit tests for GameTable component
 */

import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import GameTable from './GameTable';
import { Position, PlayerType, GamePhase } from '../../types/bridge';
import { createNewGame, createPlayers } from '../../lib/bridge/gameStateUtils';

// Mock the game state utils to avoid complex setup
jest.mock('../../lib/bridge/gameStateUtils', () => ({
  createNewGame: jest.fn(),
  createPlayers: jest.fn(),
  canSeeCards: jest.fn(),
  getValidCards: jest.fn(() => []),
}));

const mockCreateNewGame = createNewGame as jest.MockedFunction<typeof createNewGame>;
const mockCreatePlayers = createPlayers as jest.MockedFunction<typeof createPlayers>;

describe('GameTable', () => {
  const mockGameState = {
    id: 'test-game',
    phase: GamePhase.BIDDING,
    dealer: Position.NORTH,
    currentPlayer: Position.NORTH,
    players: {
      [Position.NORTH]: {
        id: 'north',
        name: 'North Player',
        position: Position.NORTH,
        type: PlayerType.AI,
        hand: []
      },
      [Position.SOUTH]: {
        id: 'south',
        name: 'You',
        position: Position.SOUTH,
        type: PlayerType.HUMAN,
        hand: []
      },
      [Position.EAST]: {
        id: 'east',
        name: 'East Player',
        position: Position.EAST,
        type: PlayerType.AI,
        hand: []
      },
      [Position.WEST]: {
        id: 'west',
        name: 'West Player',
        position: Position.WEST,
        type: PlayerType.AI,
        hand: []
      }
    },
    auction: [],
    contract: null,
    tricks: [],
    currentTrick: [],
    dummy: null,
    vulnerabilities: { northSouth: false, eastWest: false },
    score: { northSouth: 0, eastWest: 0 },
    gameNumber: 1,
    rubberScore: { northSouth: 0, eastWest: 0 }
  };

  beforeEach(() => {
    mockCreatePlayers.mockReturnValue({
      [Position.NORTH]: { name: 'AI North', type: PlayerType.AI },
      [Position.SOUTH]: { name: 'You', type: PlayerType.HUMAN },
      [Position.EAST]: { name: 'AI East', type: PlayerType.AI },
      [Position.WEST]: { name: 'AI West', type: PlayerType.AI }
    });
    
    mockCreateNewGame.mockReturnValue(mockGameState);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders without crashing', async () => {
    render(<GameTable />);
    
    await waitFor(() => {
      expect(screen.getByTestId('game-table')).toBeInTheDocument();
    });
  });

  it('displays loading state initially', () => {
    render(<GameTable />);
    
    expect(screen.queryByText('Setting up the bridge table...')).not.toBeInTheDocument();
  });

  it('renders all four player positions', async () => {
    render(<GameTable />);
    
    await waitFor(() => {
      expect(screen.getByTestId('player-n')).toBeInTheDocument();
      expect(screen.getByTestId('player-s')).toBeInTheDocument();
      expect(screen.getByTestId('player-e')).toBeInTheDocument();
      expect(screen.getByTestId('player-w')).toBeInTheDocument();
    });
  });

  it('renders center area', async () => {
    render(<GameTable />);
    
    await waitFor(() => {
      expect(screen.getByTestId('center-area')).toBeInTheDocument();
    });
  });

  it('displays game status information', async () => {
    render(<GameTable />);
    
    await waitFor(() => {
      expect(screen.getByText('Phase:')).toBeInTheDocument();
      expect(screen.getByText('Current:')).toBeInTheDocument();
    });
  });

  it('accepts initial game state prop', async () => {
    const customGameState = {
      ...mockGameState,
      currentPlayer: Position.SOUTH
    };

    render(<GameTable initialGameState={customGameState} />);
    
    await waitFor(() => {
      expect(screen.getByText('Current:')).toBeInTheDocument();
    });
  });

  it('accepts custom human position', async () => {
    render(<GameTable humanPosition={Position.EAST} />);
    
    await waitFor(() => {
      expect(mockCreatePlayers).toHaveBeenCalledWith(Position.EAST, 'You');
    });
  });

  it('handles game initialization errors gracefully', async () => {
    mockCreateNewGame.mockImplementation(() => {
      throw new Error('Test error');
    });

    render(<GameTable />);
    
    await waitFor(() => {
      expect(screen.getByText('Failed to load game')).toBeInTheDocument();
      expect(screen.getByText('There was an error setting up the bridge table. Please refresh the page.')).toBeInTheDocument();
    });
  });

  it('calls game state creation functions with correct parameters', async () => {
    render(<GameTable humanPosition={Position.WEST} />);
    
    await waitFor(() => {
      expect(mockCreatePlayers).toHaveBeenCalledWith(Position.WEST, 'You');
      expect(mockCreateNewGame).toHaveBeenCalledWith(
        'game-1',
        expect.any(Object),
        Position.NORTH
      );
    });
  });
});

/**
 * PlayerHand component tests
 */

import React from 'react';
import { render, screen } from '@testing-library/react';
import PlayerHand from './PlayerHand';
import { Position, Suit, Rank } from '../../types/bridge';

// Mock game state
const mockGameState = {
  players: {},
  currentPlayer: Position.NORTH,
  phase: 'bidding' as const,
  dealer: Position.NORTH,
  vulnerability: { northSouth: false, eastWest: false },
  bids: [],
  tricks: [],
  currentTrick: null
};

// Sample cards for testing
const sampleCards = [
  { suit: Suit.SPADES, rank: Rank.ACE },
  { suit: Suit.HEARTS, rank: Rank.KING },
  { suit: Suit.DIAMONDS, rank: Rank.QUEEN },
  { suit: Suit.CLUBS, rank: Rank.JACK },
  { suit: Suit.SPADES, rank: Rank.TEN }
];

describe('PlayerHand', () => {
  it('shows face-down cards when shouldShowCards is false', () => {
    render(
      <PlayerHand
        cards={sampleCards}
        position={Position.NORTH}
        shouldShowCards={false}
        canPlayCards={false}
        onCardPlay={() => {}}
        gameState={mockGameState}
      />
    );
    
    // Should show card backs, not actual cards
    const cardBacks = document.querySelectorAll('.card-back');
    expect(cardBacks).toHaveLength(5);
    
    // Should not show actual card content
    expect(screen.queryByText('A')).not.toBeInTheDocument();
    expect(screen.queryByText('K')).not.toBeInTheDocument();
  });

  it('shows actual cards when shouldShowCards is true', () => {
    render(
      <PlayerHand
        cards={sampleCards}
        position={Position.SOUTH}
        shouldShowCards={true}
        canPlayCards={false}
        onCardPlay={() => {}}
        gameState={mockGameState}
      />
    );
    
    // Should show actual card content (cards show rank in multiple places)
    expect(screen.getAllByText('A').length).toBeGreaterThan(0);
    expect(screen.getAllByText('K').length).toBeGreaterThan(0);
    expect(screen.getAllByText('Q').length).toBeGreaterThan(0);
    expect(screen.getAllByText('J').length).toBeGreaterThan(0);
  });

  it('uses horizontal orientation for North and South positions', () => {
    const { container } = render(
      <PlayerHand
        cards={sampleCards}
        position={Position.NORTH}
        shouldShowCards={false}
        canPlayCards={false}
        onCardPlay={() => {}}
        gameState={mockGameState}
      />
    );
    
    const handElement = container.querySelector('.player-hand');
    expect(handElement).toHaveClass('horizontal');
  });

  it('uses vertical orientation for East and West positions', () => {
    const { container } = render(
      <PlayerHand
        cards={sampleCards}
        position={Position.EAST}
        shouldShowCards={false}
        canPlayCards={false}
        onCardPlay={() => {}}
        gameState={mockGameState}
      />
    );
    
    const handElement = container.querySelector('.player-hand');
    expect(handElement).toHaveClass('vertical');
  });
});

/**
 * PlayerPosition component tests
 */

import React from 'react';
import { render, screen } from '@testing-library/react';
import PlayerPosition from './PlayerPosition';
import { Position, PlayerType } from '../../types/bridge';

// Mock game state
const mockGameState = {
  players: {
    [Position.SOUTH]: {
      id: 'human-player',
      name: 'Human Player',
      type: PlayerType.HUMAN,
      hand: []
    },
    [Position.NORTH]: {
      id: 'ai-north',
      name: 'North Player',
      type: PlayerType.AI,
      hand: []
    }
  },
  currentPlayer: Position.NORTH,
  phase: 'bidding' as const,
  dealer: Position.NORTH,
  vulnerability: { northSouth: false, eastWest: false },
  bids: [],
  tricks: [],
  currentTrick: null
};

describe('PlayerPosition', () => {
  it('displays "Your Hand" for human player in South position', () => {
    render(
      <PlayerPosition
        position={Position.SOUTH}
        player={mockGameState.players[Position.SOUTH]}
        gameState={mockGameState}
        isCurrentPlayer={false}
        onGameStateChange={() => {}}
      />
    );

    expect(screen.getByText('Your Hand')).toBeInTheDocument();
    expect(screen.queryByText('Human Player')).not.toBeInTheDocument();
    expect(screen.queryByText('Human')).not.toBeInTheDocument();
  });

  it('displays simplified AI player name for non-human players', () => {
    render(
      <PlayerPosition
        position={Position.NORTH}
        player={mockGameState.players[Position.NORTH]}
        gameState={mockGameState}
        isCurrentPlayer={false}
        onGameStateChange={() => {}}
      />
    );

    expect(screen.getByText('AI N')).toBeInTheDocument();
    expect(screen.queryByText('North Player')).not.toBeInTheDocument();
    expect(screen.queryByText('AI')).not.toBeInTheDocument();
  });

  it('does not show player type for human players', () => {
    render(
      <PlayerPosition
        position={Position.SOUTH}
        player={mockGameState.players[Position.SOUTH]}
        gameState={mockGameState}
        isCurrentPlayer={false}
        onGameStateChange={() => {}}
      />
    );

    expect(screen.queryByText('Human')).not.toBeInTheDocument();
    expect(screen.queryByText('AI')).not.toBeInTheDocument();
  });
});

/**
 * AuctionHistory component styles
 * Clean, professional auction table display
 */

.auction-history {
  width: 100%;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  overflow: hidden;
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', sans-serif;
}

.auction-header {
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  padding: 12px 16px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.auction-header h3 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
  color: #ecf0f1;
  text-align: center;
}

.position-headers {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  overflow: hidden;
}

.position-header {
  background: rgba(255, 255, 255, 0.05);
  padding: 6px 4px;
  text-align: center;
  font-size: 11px;
  font-weight: 600;
  color: #bdc3c7;
  transition: all 0.2s ease;
}

.position-header.current {
  background: rgba(52, 152, 219, 0.3);
  color: #3498db;
  font-weight: 700;
}

.auction-table {
  padding: 8px;
  max-height: 200px;
  overflow-y: auto;
}

.auction-round {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1px;
  margin-bottom: 1px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 3px;
  overflow: hidden;
}

.auction-round.current {
  background: rgba(52, 152, 219, 0.1);
  border: 1px solid rgba(52, 152, 219, 0.3);
}

.auction-cell {
  background: rgba(255, 255, 255, 0.02);
  padding: 8px 4px;
  text-align: center;
  font-size: 12px;
  min-height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.auction-cell.has-bid {
  background: rgba(255, 255, 255, 0.08);
}

.auction-cell.current-player {
  background: rgba(52, 152, 219, 0.2);
  border: 1px solid rgba(52, 152, 219, 0.5);
}

.bid-value {
  color: #ecf0f1;
  font-weight: 600;
  font-size: 13px;
}

.empty-bid {
  color: #7f8c8d;
  font-weight: 300;
}

.thinking {
  color: #f39c12;
  font-weight: 600;
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 0.5; }
  50% { opacity: 1; }
}

.no-bids {
  padding: 16px;
  text-align: center;
  color: #95a5a6;
}

.no-bids p {
  margin: 4px 0;
  font-size: 12px;
}

.no-bids strong {
  color: #3498db;
  font-weight: 600;
}

/* Auction summary when no bids */
.auction-summary {
  padding: 12px 16px;
  background: rgba(255, 255, 255, 0.03);
  border-top: 1px solid rgba(255, 255, 255, 0.05);
}

.auction-summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 0;
  font-size: 11px;
  color: #bdc3c7;
}

.auction-summary-value {
  color: #ecf0f1;
  font-weight: 600;
}

/* Responsive design */
@media (max-width: 768px) {
  .auction-header h3 {
    font-size: 13px;
  }
  
  .position-header {
    font-size: 10px;
    padding: 4px 2px;
  }
  
  .auction-cell {
    padding: 6px 2px;
    font-size: 11px;
  }
  
  .bid-value {
    font-size: 12px;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .auction-history {
    background: white;
    border: 2px solid black;
  }
  
  .auction-header {
    background: black;
  }
  
  .auction-header h3 {
    color: white;
  }
  
  .position-header {
    color: #333;
    background: #f0f0f0;
  }
  
  .position-header.current {
    background: #0066cc;
    color: white;
  }
  
  .bid-value {
    color: black;
  }
  
  .auction-cell.current-player {
    background: #cce6ff;
    border-color: #0066cc;
  }
}

/* Scrollbar styling */
.auction-table::-webkit-scrollbar {
  width: 4px;
}

.auction-table::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 2px;
}

.auction-table::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 2px;
}

.auction-table::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* Suit colors for bid values */
.bid-value.red-suit,
.auction-summary-value.red-suit {
  color: #dc2626 !important; /* Red for Hearts and Diamonds */
  font-weight: bold;
  text-shadow: 0 1px 2px rgba(220, 38, 38, 0.3);
}

.bid-value.black-suit,
.auction-summary-value.black-suit {
  color: #ffffff !important; /* White for Spades and Clubs (better contrast on dark background) */
  font-weight: bold;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

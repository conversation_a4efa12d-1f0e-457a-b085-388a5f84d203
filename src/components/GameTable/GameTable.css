/**
 * GameTable component styles
 * Creates the bridge table layout with four player positions
 */

/* Ensure no body/html interference with viewport */
html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  overflow: hidden;
}

.game-table {
  position: relative;
  width: 100vw;
  height: 100vh;
  min-height: 600px;
  max-width: 100%;
  display: grid;
  grid-template-areas:
    "left-panel center right-panel";
  grid-template-columns: 280px 1fr 320px;
  grid-template-rows: 1fr;
  gap: 3px;
  padding: 3px;
  overflow: visible;
  background: radial-gradient(ellipse at center, #1a5c4a 0%, #0f4c3a 100%);
  box-sizing: border-box;
}



/* Left panel for auction history and bidding status */
.left-panel {
  grid-area: left-panel;
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 40px 8px 8px 8px; /* Added extra top padding to avoid header overlap */
  background: rgba(0, 0, 0, 0.3);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  overflow-y: auto;
  max-height: 100%;
}

/* Right panel for bidding controls and coach */
.right-panel {
  grid-area: right-panel;
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 40px 8px 8px 8px; /* Added extra top padding to avoid header overlap */
  background: rgba(0, 0, 0, 0.3);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  overflow-y: auto;
  max-height: 100%;
}

/* Panel section styling for both left and right panels */
.left-panel-section,
.right-panel-section {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  overflow: hidden;
}

/* Left panel sections */
.left-panel-section.auction-history {
  flex: 1;
  min-height: 300px;
  max-height: 400px;
}

.left-panel-section.bidding-status {
  flex: 0 0 auto;
  max-height: 150px;
}

/* Right panel sections */
.right-panel-section.bidding-panel {
  flex: 0 0 auto;
  max-height: 300px;
  overflow-y: auto;
}

.right-panel-section.coach {
  flex: 1;
  min-height: 400px;
}

/* Player positions */
.player-position {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Grid area assignments */
.north {
  grid-area: north;
}

.south {
  grid-area: south;
}

.east {
  grid-area: east;
}

.west {
  grid-area: west;
}

.center {
  grid-area: center;
}

.player-position.south {
  grid-area: south;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  min-height: 150px !important;
}

.player-position.east {
  grid-area: east;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.player-position.west {
  grid-area: west;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* Center area - Now contains CardTable */
.center-area {
  grid-area: center;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px;
  max-width: 100%;
  overflow: visible;
  position: relative;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(5px);
}


.status-info {
  display: flex;
  flex-direction: column;
  gap: 6px;
  font-size: 12px;
  color: #cccccc;
}

.status-info span {
  display: block;
}

.status-info .phase {
  color: #4a9eff;
  font-weight: 600;
}

.status-info .current-player {
  color: #ffd700;
}

.status-info .contract {
  color: #ff6b6b;
}

/* Loading state */
.game-table.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  grid-template-areas: none;
  grid-template-columns: none;
  grid-template-rows: none;
}

.loading-spinner {
  text-align: center;
}

.loading-spinner p {
  margin-bottom: 20px;
  color: #cccccc;
  font-size: 16px;
}

/* Error state */
.game-table.error {
  display: flex;
  align-items: center;
  justify-content: center;
  grid-template-areas: none;
  grid-template-columns: none;
  grid-template-rows: none;
}

/* Player position containers */
.player-position-container {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 100%;
  min-height: 120px;
  gap: 8px;
  padding: 12px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
  box-sizing: border-box;
  overflow: visible; /* Allow rotated hands to be visible */
}

.player-position-container.current {
  background: rgba(255, 215, 0, 0.1);
  border-color: rgba(255, 215, 0, 0.3);
  box-shadow: 0 0 20px rgba(255, 215, 0, 0.2);
}

.player-position-container.dummy {
  background: rgba(74, 158, 255, 0.1);
  border-color: rgba(74, 158, 255, 0.3);
}

/* Ensure south position (player's hand) has adequate space and is always visible */
.player-position.south {
  min-height: 200px; /* Increased minimum height */
  z-index: 10;
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  justify-content: center !important;
}

.player-position.south .player-position-container {
  min-height: 140px;
  background: rgba(255, 255, 255, 0.12) !important;
  border: 2px solid rgba(255, 215, 0, 0.5) !important;
  box-shadow: 0 0 10px rgba(255, 215, 0, 0.3) !important;
}

/* Current player indicator */
.current-player-indicator {
  position: absolute;
  top: -5px;
  right: -5px;
  width: 20px;
  height: 20px;
  z-index: 5;
}

.indicator-pulse {
  width: 100%;
  height: 100%;
  background: #ffd700;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.7;
  }
  100% {
    transform: scale(0.8);
    opacity: 1;
  }
}

/* Responsive design */
@media (max-width: 1024px) {
  .game-table {
    max-width: 900px;
    max-height: 600px;
    gap: 15px;
    padding: 15px;
  }
  
  .player-position-container {
    padding: 10px;
    gap: 8px;
  }
}

@media (max-width: 768px) {
  .game-table {
    max-width: 100%;
    max-height: 500px;
    gap: 10px;
    padding: 10px;
    grid-template-columns: 0.8fr 2fr 0.8fr;
  }
  
  .center-area {
    padding: 15px;
  }
  
  .game-status {
    top: 5px;
    right: 5px;
    padding: 8px;
  }
  
  .status-info {
    font-size: 11px;
    gap: 4px;
  }
}

@media (max-width: 480px) {
  .game-table {
    border-radius: 10px;
    gap: 8px;
    padding: 8px;
  }
  
  .player-position-container {
    padding: 8px;
    gap: 6px;
  }
  
  .center-area {
    padding: 10px;
    border-radius: 10px;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .player-position-container {
    transition: none;
  }
  
  .indicator-pulse {
    animation: none;
    opacity: 1;
  }
}


/* Score Board Container */
.score-board-container {
  position: absolute;
  bottom: 20px;




/* Tablet responsive - stack panels below game */
@media (max-width: 768px) {
  .game-table {
    grid-template-areas:
      ". north ."
      "west center east"
      ". south ."
      "left-panel left-panel left-panel"
      "right-panel right-panel right-panel";
    grid-template-columns: 1fr 1fr 1fr;
    grid-template-rows: 0.8fr 1fr 1.2fr auto auto;
    gap: 4px;
    padding: 4px;
  }

  .left-panel,
  .right-panel {
    flex-direction: row;
    overflow-x: auto;
    overflow-y: hidden;
    max-height: 200px;
    gap: 6px;
  }

  .left-panel-section,
  .right-panel-section {
    flex: 0 0 250px;
    max-height: 180px;
  }

  .player-position.south .player-position-container {
    min-height: 100px;
  }
}

/* Mobile responsive - compact layout */
@media (max-width: 480px) {
  .game-table {
    grid-template-rows: 0.8fr 1fr 1.2fr auto auto;
    gap: 3px;
    padding: 3px;
  }

  .left-panel,
  .right-panel {
    gap: 4px;
    padding: 4px;
    max-height: 180px;
  }

  .left-panel-section,
  .right-panel-section {
    flex: 0 0 220px;
    max-height: 160px;
  }

  .player-position.south .player-position-container {
    min-height: 80px;
  }

  .player-position-container {
    padding: 6px;
    gap: 4px;
    min-height: 80px;
  }
}

/* Bidding Panel Overlay - positioned to not interfere with grid layout */
.bidding-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 100;
  pointer-events: none;
}

.bidding-overlay .bidding-panel {
  pointer-events: all;
  transform: scale(0.8);
  min-width: 240px;
  max-width: 280px;
  background: rgba(0, 0, 0, 0.95);
  border: 2px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.6);
}





/* Center content styling */
.center-content {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  padding: 20px;
  text-align: center;
  color: white;
}

.center-content h3 {
  margin: 0 0 10px 0;
  color: #ffd700;
}

.center-content p {
  margin: 5px 0;
  font-size: 14px;
  opacity: 0.9;
}

.bidding-area {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  justify-content: center;
  margin-top: 15px;
}

.bid-button {
  background: #4a8c6a;
  color: white;
  border: 1px solid #6aa88a;
  border-radius: 6px;
  padding: 8px 12px;
  font-size: 12px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.2s ease;
}

.bid-button:hover {
  background: #5aa87a;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.bid-button:active {
  transform: translateY(0);
}


/* Horizontal status info layout */
.status-info {
  display: flex;
  align-items: center;
  gap: 12px;
  color: white;
  font-size: 13px;
}

.status-info > div {
  display: flex;
  align-items: center;
  gap: 4px;
}

.status-info .status-label {
  opacity: 0.8;
  font-size: 12px;
}

.status-info .status-value {
  font-weight: bold;
  color: #ffd700;
}

/* Compact horizontal game status */
.game-status-horizontal {
  background: rgba(0, 0, 0, 0.8);
  border-radius: 6px;
  padding: 6px 12px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  gap: 16px;
  color: white;
  font-size: 13px;
  white-space: nowrap;
}



  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
}

/* Settings Button */
.settings-button {
  position: fixed;
  top: 20px;
  right: 20px;
  width: 50px;
  height: 50px;
  background: linear-gradient(145deg, #ffd700 0%, #ffed4e 100%);
  border: none;
  border-radius: 50%;
  font-size: 20px;
  cursor: pointer;
  box-shadow:
    0 4px 12px rgba(255, 215, 0, 0.3),
    0 2px 4px rgba(0, 0, 0, 0.2);
  transition: all 0.2s ease;
  z-index: 100;
  display: flex;
  align-items: center;
  justify-content: center;
}

.settings-button:hover {
  transform: translateY(-2px) scale(1.05);
  box-shadow:
    0 6px 16px rgba(255, 215, 0, 0.4),
    0 4px 8px rgba(0, 0, 0, 0.3);
}

.settings-button:active {
  transform: translateY(0) scale(0.95);
}

@media (max-width: 768px) {
  .settings-button {
    top: 10px;
    right: 10px;
    width: 40px;
    height: 40px;
    font-size: 16px;
  }
}


/**
 * PlayerHand component displaying a player's cards
 * Handles card visibility, selection, and play interactions
 */

import React from 'react';
import { Card, Position, GameState } from '../../types/bridge';
import { getValidCardsForPlayer } from '../../lib/bridge/gameStateUtils';
import { sortHand } from '../../lib/bridge/cardUtils';
import CardComponent from '../Card/Card';
import './PlayerHand-enhanced.css';
interface PlayerHandProps {
  cards: Card[];
  position: Position;
  shouldShowCards: boolean;
  canPlayCards: boolean;
  onCardPlay: (cardIndex: number) => void;
  onCardDragStart?: (position: Position, cardIndex: number, card: Card) => (e: React.DragEvent) => void;
  onCardDragEnd?: (e: React.DragEvent) => void;
  gameState: GameState;
  onLogPlayAttempt?: (
    position: Position,
    cardIndex: number,
    card: string,
    method: 'double-click' | 'drag-drop' | 'single-click' | 'coach-recommendation',
    success: boolean,
    error?: string
  ) => void;
}

const PlayerHand: React.FC<PlayerHandProps> = ({
  cards,
  position,
  shouldShowCards,
  canPlayCards,
  onCardPlay,
  onCardDragStart,
  onCardDragEnd,
  gameState,
  onLogPlayAttempt
}) => {
  console.log('PlayerHand rendering:', { position, shouldShowCards, canPlayCards, cardsCount: cards.length });

  // Debug: Check for duplicate cards
  const cardStrings = cards.map(c => `${c.rank}${c.suit}`);
  const uniqueCardStrings = [...new Set(cardStrings)];
  if (cardStrings.length !== uniqueCardStrings.length) {
    console.error('🚨 DUPLICATE CARDS DETECTED in PlayerHand:', {
      position,
      totalCards: cardStrings.length,
      uniqueCards: uniqueCardStrings.length,
      allCards: cardStrings,
      duplicates: cardStrings.filter((card, index) => cardStrings.indexOf(card) !== index)
    });
  }

  // Sort cards by suit and rank (standard bridge sorting)
  const sortedCards = sortHand(cards);

  // Get valid cards that can be played for this specific position
  const validCards = canPlayCards ? getValidCardsForPlayer(gameState, position) : [];

  // Always log this for South position
  if (position === 'S') {
    console.log('SOUTH PlayerHand debug:', {
      position,
      canPlayCards,
      validCardsCount: validCards.length,
      gamePhase: gameState.phase,
      currentPlayer: gameState.currentPlayer,
      validCards: validCards.map(c => `${c.rank}${c.suit}`),
      playerType: gameState.players[position]?.type
    });
  }

  console.log('PlayerHand debug:', {
    position,
    canPlayCards,
    validCardsCount: validCards.length,
    gamePhase: gameState.phase,
    currentPlayer: gameState.currentPlayer,
    validCards: validCards.map(c => `${c.rank}${c.suit}`)
  });

  const isCardPlayable = (card: Card): boolean => {
    console.log(`Checking if card ${card.rank}${card.suit} is playable:`, {
      canPlayCards,
      validCardsCount: validCards.length,
      validCards: validCards.map(c => `${c.rank}${c.suit}`)
    });

    if (!canPlayCards) {
      console.log(`Card ${card.rank}${card.suit} not playable: canPlayCards is false`);
      return false;
    }

    // In debug mode (when we have a contract but minimal auction), allow any card
    const isDebugMode = gameState.contract && gameState.auction.length < 4;
    if (isDebugMode) {
      console.log(`Debug mode: allowing card ${card.rank}${card.suit} to be played`);
      return true;
    }

    const playable = validCards.some(validCard =>
      validCard.rank === card.rank && validCard.suit === card.suit
    );
    console.log(`Card ${card.rank}${card.suit} playable:`, playable);
    return playable;
  };

  const handleCardClick = (cardIndex: number) => {
    const card = sortedCards[cardIndex];
    const cardString = `${card.rank}${card.suit}`;

    console.log('Card click handler called:', { cardIndex, canPlayCards, position, card: cardString });

    if (!canPlayCards) {
      console.log('Cannot play cards - not player turn or wrong phase');
      onLogPlayAttempt?.(position, cardIndex, cardString, 'double-click', false, 'Cannot play cards - not player turn or wrong phase');
      return;
    }

    console.log('Attempting to play card:', card);
    console.log('Valid cards for this position:', validCards);
    console.log('Is card in valid cards list?', validCards.some(vc => vc.suit === card.suit && vc.rank === card.rank));

    if (isCardPlayable(card)) {
      // Find the original index of this card in the unsorted array
      const originalIndex = cards.findIndex(c => c.suit === card.suit && c.rank === card.rank);
      console.log('Playing card at original index:', originalIndex);
      onLogPlayAttempt?.(position, originalIndex, cardString, 'double-click', true);
      onCardPlay(originalIndex);
    } else {
      const errorMsg = 'Card not playable according to validation rules';
      console.log('Card is not playable:', card);
      console.log('Reason: Card not in valid cards list or other validation failed');
      onLogPlayAttempt?.(position, cardIndex, cardString, 'double-click', false, errorMsg);
    }
  };

  const getHandOrientation = (pos: Position): string => {
    switch (pos) {
      case Position.NORTH:
        return 'horizontal';
      case Position.SOUTH:
        return 'horizontal';
      case Position.EAST:
        return 'vertical';
      case Position.WEST:
        return 'vertical';
      default:
        return 'horizontal';
    }
  };

  // Split cards into two rows for vertical hands
  const splitCardsIntoRows = (cards: Card[]): [Card[], Card[]] => {
    const midpoint = Math.ceil(cards.length / 2);
    const topRow = cards.slice(0, midpoint);
    const bottomRow = cards.slice(midpoint);
    return [topRow, bottomRow];
  };

  const orientation = getHandOrientation(position);
  const positionClass = position.toLowerCase(); // Add position-specific class

  if (!shouldShowCards) {
    // Show card backs for hidden hands
    const cardCount = sortedCards.length > 0 ? sortedCards.length : 13; // Default to 13 cards

    if (orientation === 'vertical') {
      // Two rows for vertical hands
      const [topRowCount, bottomRowCount] = [Math.ceil(cardCount / 2), Math.floor(cardCount / 2)];
      return (
        <div className={`player-hand hidden ${orientation} ${positionClass}`} data-card-count={cardCount} data-testid={`hand-${position.toLowerCase()}`}>
          <div className="card-row top-row">
            {Array.from({ length: topRowCount }, (_, index) => (
              <div key={`${position}-hidden-top-${index}`} className="card-back">
                <div className="card-back-design">🂠</div>
              </div>
            ))}
          </div>
          <div className="card-row bottom-row">
            {Array.from({ length: bottomRowCount }, (_, index) => (
              <div key={`${position}-hidden-bottom-${index}`} className="card-back">
                <div className="card-back-design">🂠</div>
              </div>
            ))}
          </div>
        </div>
      );
    } else {
      // Single row for horizontal hands
      return (
        <div className={`player-hand hidden ${orientation} ${positionClass}`} data-card-count={cardCount} data-testid={`hand-${position.toLowerCase()}`}>
          {Array.from({ length: cardCount }, (_, index) => (
            <div key={`${position}-hidden-${index}`} className="card-back">
              <div className="card-back-design">🂠</div>
            </div>
          ))}
        </div>
      );
    }
  }

  // Visible cards
  if (orientation === 'vertical') {
    // Two rows for vertical hands
    const [topRow, bottomRow] = splitCardsIntoRows(sortedCards);
    let cardIndex = 0;

    return (
      <div className={`player-hand visible ${orientation} ${positionClass}`} data-card-count={sortedCards.length} data-testid={`hand-${position.toLowerCase()}`}>
        <div className="card-row top-row">
          {topRow.map((card, rowIndex) => {
            const playable = isCardPlayable(card);
            const currentIndex = cardIndex++;
            return (
              <div
                key={`${position}-top-${currentIndex}-${card.suit}-${card.rank}-${rowIndex}`}
                className={`card-container ${playable ? 'playable' : ''} ${canPlayCards ? 'interactive' : ''}`}
                onClick={() => handleCardClick(currentIndex)}
              >
                <CardComponent
                  card={card}
                  isPlayable={playable}
                  isSelected={false}
                  size="medium"
                  onDoubleClick={playable ? () => handleCardClick(currentIndex) : undefined}
                  onDragStart={playable && onCardDragStart ? onCardDragStart(position, cards.findIndex(c => c.suit === card.suit && c.rank === card.rank), card) : undefined}
                  onDragEnd={onCardDragEnd}
                  draggable={playable && canPlayCards}
                />
              </div>
            );
          })}
        </div>
        <div className="card-row bottom-row">
          {bottomRow.map((card, rowIndex) => {
            const playable = isCardPlayable(card);
            const currentIndex = cardIndex++;
            return (
              <div
                key={`${position}-bottom-${currentIndex}-${card.suit}-${card.rank}-${rowIndex}`}
                className={`card-container ${playable ? 'playable' : ''} ${canPlayCards ? 'interactive' : ''}`}
                onClick={() => handleCardClick(currentIndex)}
              >
                <CardComponent
                  card={card}
                  isPlayable={playable}
                  isSelected={false}
                  size="medium"
                  onDoubleClick={playable ? () => handleCardClick(currentIndex) : undefined}
                  onDragStart={playable && onCardDragStart ? onCardDragStart(position, cards.findIndex(c => c.suit === card.suit && c.rank === card.rank), card) : undefined}
                  onDragEnd={onCardDragEnd}
                  draggable={playable && canPlayCards}
                />
              </div>
            );
          })}
        </div>
      </div>
    );
  } else {
    // Single row for horizontal hands
    return (
      <div className={`player-hand visible ${orientation} ${positionClass}`} data-card-count={sortedCards.length} data-testid={`hand-${position.toLowerCase()}`}>
        {sortedCards.map((card, index) => {
          const playable = isCardPlayable(card);
          return (
            <div
              key={`${position}-horizontal-${index}-${card.suit}-${card.rank}`}
              className={`card-container ${playable ? 'playable' : ''} ${canPlayCards ? 'interactive' : ''}`}
              onClick={() => handleCardClick(index)}
            >
              <CardComponent
                card={card}
                isPlayable={playable}
                isSelected={false}
                size={position === Position.SOUTH ? 'large' : 'medium'}
                onDoubleClick={playable ? () => handleCardClick(index) : undefined}
                onDragStart={playable && onCardDragStart ? onCardDragStart(position, cards.findIndex(c => c.suit === card.suit && c.rank === card.rank), card) : undefined}
                onDragEnd={onCardDragEnd}
                draggable={playable && canPlayCards}
              />
            </div>
          );
        })}
      </div>
    );
  }
};

export default PlayerHand;

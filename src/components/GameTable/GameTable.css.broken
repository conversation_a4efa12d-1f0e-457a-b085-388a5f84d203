/**
 * GameTable component styles - OPTIMIZED FOR FULL BROWSER VISIBILITY
 * Creates the bridge table layout with four player positions
 * Ensures all hands are visible and status doesn't block content
 */

/* Main game table container - full viewport with left and right panels */
.game-table {
  position: relative;
  width: 100vw;
  height: 100vh;
  max-width: 100%;
  max-height: 100%;
  display: grid;
  grid-template-areas:
    "left-panel status status right-panel"
    "left-panel . north . right-panel"
    "left-panel west center east right-panel"
    "left-panel . south . right-panel";
  grid-template-columns: 280px 1fr 1fr 1fr 320px;
  grid-template-rows: 80px 1fr 1fr 1fr;
  gap: 8px;
  padding: 8px;
  overflow: hidden;
  background: radial-gradient(ellipse at center, #1a5c4a 0%, #0f4c3a 100%);
  box-sizing: border-box;
}

/* Status area - dedicated grid area at top */
.game-status-container {
  grid-area: status;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 8px 12px;
  z-index: 100;
  height: 100%;
  box-sizing: border-box;
  overflow: visible;
}

/* Left panel for auction history and bidding status */
.left-panel {
  grid-area: left-panel;
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 8px;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  overflow-y: auto;
  max-height: 100%;
}

/* Right panel for bidding controls and coach */
.right-panel {
  grid-area: right-panel;
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 8px;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  overflow-y: auto;
  max-height: 100%;
}

/* Panel section styling for both left and right panels */
.left-panel-section,
.right-panel-section {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  overflow: hidden;
}

/* Left panel sections */
.left-panel-section.auction-history {
  flex: 1;
  min-height: 300px;
  max-height: 400px;
}

.left-panel-section.bidding-status {
  flex: 0 0 auto;
  max-height: 150px;
}

/* Right panel sections */
.right-panel-section.bidding-panel {
  flex: 0 0 auto;
  max-height: 300px;
  overflow-y: auto;
}

.right-panel-section.coach {
  flex: 1;
  min-height: 400px;
}

/* Player positions */
.player-position {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 0;
  min-width: 0;
}

/* Grid area assignments */
.player-position.north {
  grid-area: north;
}

.player-position.south {
  grid-area: south;
}

.player-position.east {
  grid-area: east;
  writing-mode: vertical-lr;
  text-orientation: mixed;
}

.player-position.west {
  grid-area: west;
  writing-mode: vertical-rl;
  text-orientation: mixed;
}

/* Center area */
.center-area {
  grid-area: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 15px;
  padding: 10px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(5px);
  min-height: 0;
  overflow: hidden;
}

/* Player position containers */
.player-position-container {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
  padding: 8px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
  width: 100%;
  min-height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  box-sizing: border-box;
  overflow: hidden;
}

.player-position-container.current {
  background: rgba(255, 215, 0, 0.1);
  border-color: rgba(255, 215, 0, 0.3);
  box-shadow: 0 0 10px rgba(255, 215, 0, 0.2);
}

.player-position-container.dummy {
  background: rgba(74, 158, 255, 0.1);
  border-color: rgba(74, 158, 255, 0.3);
}

/* Player info */
.player-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
  font-size: 12px;
  color: white;
  text-align: center;
  flex-shrink: 0;
}

.player-name {
  font-weight: bold;
  color: #ffd700;
}

.player-type {
  font-size: 10px;
  opacity: 0.8;
}

.dummy-indicator {
  background: rgba(74, 158, 255, 0.8);
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 10px;
  font-weight: bold;
}

.current-player-indicator {
  background: rgba(255, 215, 0, 0.8);
  color: black;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 10px;
  font-weight: bold;
}

/* Card count display */
.card-count {
  color: #cccccc;
  font-size: 11px;
  text-align: center;
  margin-top: 5px;
}

/* Game status indicator - horizontal compact layout */
.game-status-horizontal {
  background: rgba(0, 0, 0, 0.85);
  border-radius: 6px;
  padding: 6px 12px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  display: flex;
  align-items: center;
  gap: 16px;
  color: white;
  font-size: 12px;
  white-space: nowrap;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.status-info {
  display: flex;
  align-items: center;
  gap: 12px;
  color: white;
  font-size: 12px;
}

.status-info > div {
  display: flex;
  align-items: center;
  gap: 4px;
}

.status-info .status-label {
  opacity: 0.8;
  font-size: 11px;
}

.status-info .status-value {
  font-weight: bold;
  color: #ffd700;
}

/* Loading and error states */
.game-table.loading,
.game-table.error {
  display: flex;
  align-items: center;
  justify-content: center;
  grid-template-areas: none;
  grid-template-columns: none;
  grid-template-rows: none;
}

.loading-spinner,
.error-message {
  text-align: center;
  color: white;
}

.loading-spinner p {
  margin-bottom: 20px;
  color: #cccccc;
  font-size: 16px;
}

/* Responsive design for smaller screens */
@media (max-width: 1024px) {
  .game-table {
    grid-template-rows: 45px 1fr 1fr 1fr;
    gap: 6px;
    padding: 6px;
  }
  
  .player-position-container {
    padding: 6px;
    gap: 4px;
  }
  
  .player-info {
    font-size: 11px;
  }
}

/* Medium screens - smaller panels */
@media (max-width: 1400px) {
  .game-table {
    grid-template-columns: 240px 1fr 1fr 1fr 280px;
  }

  .left-panel,
  .right-panel {
    gap: 8px;
    padding: 6px;
  }

  .left-panel-section.auction-history {
    min-height: 250px;
    max-height: 350px;
  }

  .right-panel-section.bidding-panel {
    max-height: 250px;
  }

  .right-panel-section.coach {
    min-height: 300px;
  }
}

@media (max-width: 768px) {
  .game-table {
    grid-template-areas:
      "status status status"
      ". north ."
      "west center east"
      ". south ."
      "left-panel left-panel left-panel"
      "right-panel right-panel right-panel";
    grid-template-columns: 1fr 1fr 1fr;
    grid-template-rows: 40px 1fr 1fr 1fr auto auto;
    gap: 4px;
    padding: 4px;
  }

  .left-panel,
  .right-panel {
    flex-direction: row;
    overflow-x: auto;
    overflow-y: hidden;
    max-height: 250px;
    gap: 8px;
  }

  .left-panel-section,
  .right-panel-section {
    flex: 0 0 280px;
    max-height: 230px;
  }

  .left-panel-section.auction-history {
    flex: 0 0 320px;
    min-height: auto;
    max-height: 230px;
  }

  .right-panel-section.bidding-panel {
    flex: 0 0 300px;
  }

  .right-panel-section.coach {
    flex: 0 0 350px;
    min-height: auto;
  }

  .player-position-container {
    padding: 4px;
    gap: 3px;
  }

  .player-info {
    font-size: 10px;
  }

  .game-status-horizontal {
    padding: 4px 8px;
    font-size: 11px;
    gap: 12px;
  }
}

@media (max-width: 480px) {
  .game-table {
    grid-template-rows: 35px 1fr 1fr 1fr auto auto;
    gap: 3px;
    padding: 3px;
  }

  .left-panel,
  .right-panel {
    gap: 6px;
    padding: 4px;
    max-height: 220px;
  }

  .left-panel-section,
  .right-panel-section {
    flex: 0 0 220px;
    max-height: 200px;
  }

  .left-panel-section.auction-history {
    flex: 0 0 280px;
  }

  .right-panel-section.bidding-panel {
    flex: 0 0 260px;
  }

  .right-panel-section.coach {
    flex: 0 0 300px;
  }

  .player-position-container {
    padding: 3px;
    gap: 2px;
  }

  .player-info {
    font-size: 9px;
  }

  .game-status-horizontal {
    padding: 3px 6px;
    font-size: 10px;
    gap: 8px;
  }
}

/* Ensure hands are always visible */
.player-position-container .hand-display {
  flex: 1;
  width: 100%;
  height: 100%;
  min-height: 0;
  overflow: visible;
}

/* Prevent any absolute positioning from blocking content */
.game-table * {
  position: relative;
}

.game-table .game-status-container {
  position: static !important;
}

/* Bidding panel overlay positioning */
.bidding-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 200;
  pointer-events: none;
}

.bidding-overlay .bidding-panel {
  pointer-events: all;
  background: rgba(0, 0, 0, 0.95);
  border: 2px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.6);
  border-radius: 8px;
  padding: 15px;
}

/* Ensure hands use full available space in each player area */
.player-position-container .hand-display {
  flex: 1;
  width: 100%;
  min-height: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

/* Specific adjustments for each position */
.player-position.north .player-position-container {
  justify-content: flex-start;
}

.player-position.south .player-position-container {
  justify-content: flex-end;
}

.player-position.east .player-position-container,
.player-position.west .player-position-container {
  justify-content: center;
  align-items: center;
}

/* Ensure center area doesn't dominate */
.center-area {
  max-height: 100%;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Status area improvements */
.game-status-horizontal {
  max-width: 100%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

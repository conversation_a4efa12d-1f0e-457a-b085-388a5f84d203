/**
 * AuctionHistory component displaying the bidding sequence
 */

import React from 'react';
import { Auction, Position, SpecialBid, Bid } from '../../types/bridge';
import './AuctionHistory.css';

interface AuctionHistoryProps {
  auction: Auction;
  currentPlayer: Position;
}

const AuctionHistory: React.FC<AuctionHistoryProps> = ({
  auction,
  currentPlayer
}) => {
  const formatBidValue = (bidValue: any): string => {
    if (typeof bidValue === 'string') {
      switch (bidValue) {
        case SpecialBid.PASS:
          return 'Pass';
        case SpecialBid.DOUBLE:
          return 'X';
        case SpecialBid.REDOUBLE:
          return 'XX';
        default:
          return bidValue;
      }
    }

    if (bidValue && typeof bidValue === 'object' && 'level' in bidValue && 'suit' in bidValue) {
      const suitSymbol = bidValue.suit === 'C' ? '♣' :
                        bidValue.suit === 'D' ? '♦' :
                        bidValue.suit === 'H' ? '♥' :
                        bidValue.suit === 'S' ? '♠' :
                        bidValue.suit === 'NT' ? 'NT' : bidValue.suit;
      return `${bidValue.level}${suitSymbol}`;
    }

    return 'Unknown';
  };

  const getSuitColorClass = (bidValue: any): string => {
    if (bidValue && typeof bidValue === 'object' && 'suit' in bidValue) {
      return (bidValue.suit === 'H' || bidValue.suit === 'D') ? 'red-suit' : 'black-suit';
    }
    return '';
  };

  const getPositionOrder = (): Position[] => {
    return [Position.NORTH, Position.EAST, Position.SOUTH, Position.WEST];
  };

  const formatPosition = (position: Position): string => {
    const names = {
      [Position.NORTH]: 'N',
      [Position.EAST]: 'E',
      [Position.SOUTH]: 'S',
      [Position.WEST]: 'W'
    };
    return names[position];
  };

  const positionOrder = getPositionOrder();

  // Calculate how many complete rounds we have and create display rows
  const totalRounds = Math.ceil(Math.max(auction.length, 1) / 4);

  return (
    <div className="auction-history" data-testid="auction-history">
      <div className="auction-header">
        <h3>Auction</h3>
        <div className="position-headers">
          {positionOrder.map(position => (
            <div
              key={position}
              className={`position-header ${position === currentPlayer ? 'current' : ''}`}
            >
              {formatPosition(position)}
            </div>
          ))}
        </div>
      </div>

      <div className="auction-table">
        {Array.from({ length: totalRounds }, (_, roundIndex) => (
          <div key={roundIndex} className={`auction-round ${roundIndex === totalRounds - 1 ? 'current' : ''}`}>
            {positionOrder.map((position, posIndex) => {
              const bidIndex = roundIndex * 4 + posIndex;
              const bid = auction[bidIndex];
              const isCurrentPlayerTurn = position === currentPlayer && bidIndex === auction.length;

              return (
                <div
                  key={`${roundIndex}-${position}`}
                  className={`auction-cell ${bid ? 'has-bid' : 'empty'} ${
                    isCurrentPlayerTurn ? 'current-player' : ''
                  }`}
                >
                  {bid ? (
                    <span className={`bid-value ${getSuitColorClass(bid.value)}`}>
                      {formatBidValue(bid.value)}
                    </span>
                  ) : isCurrentPlayerTurn ? (
                    <span className="thinking">...</span>
                  ) : (
                    <span className="empty-bid">-</span>
                  )}
                </div>
              );
            })}
          </div>
        ))}
      </div>

      {auction.length === 0 && (
        <div className="no-bids">
          <p>🎯 Auction starting...</p>
          <p>Current bidder: <strong>{currentPlayer}</strong></p>
        </div>
      )}

      {/* Show auction summary */}
      <div className="auction-summary">
        <div className="auction-summary-item">
          <span>Total Bids:</span>
          <span className="auction-summary-value">{auction.length}</span>
        </div>
        <div className="auction-summary-item">
          <span>Current Turn:</span>
          <span className="auction-summary-value">{formatPosition(currentPlayer)}</span>
        </div>
        {auction.length > 0 && (
          <div className="auction-summary-item">
            <span>Last Bid:</span>
            <span className={`auction-summary-value ${getSuitColorClass(auction[auction.length - 1].value)}`}>
              {formatBidValue(auction[auction.length - 1].value)}
            </span>
          </div>
        )}
      </div>
    </div>
  );
};

export default AuctionHistory;

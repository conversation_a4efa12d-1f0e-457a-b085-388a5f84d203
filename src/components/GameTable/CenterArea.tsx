/**
 * CenterArea component displaying the center of the bridge table
 * Shows current trick, auction history, and game status
 */

import React from 'react';
import { GameState, GamePhase } from '../../types/bridge';
import CurrentTrick from './CurrentTrick';
import AuctionHistory from './AuctionHistory';
import GameStatus from './GameStatus';

interface CenterAreaProps {
  gameState: GameState;
  onGameStateChange: (newGameState: GameState) => void;
}

const CenterArea: React.FC<CenterAreaProps> = ({
  gameState,
  onGameStateChange
}) => {
  return (
    <div className="center-area" data-testid="center-area">
      {/* Game Status */}
      <div className="status-section">
        <GameStatus gameState={gameState} />
      </div>

      {/* Main Content Area */}
      <div className="main-content">
        {gameState.phase === GamePhase.BIDDING ? (
          // Show auction history during bidding
          <AuctionHistory 
            auction={gameState.auction}
            currentPlayer={gameState.currentPlayer}
          />
        ) : (
          // Show current trick during playing
          <CurrentTrick 
            currentTrick={gameState.currentTrick}
            completedTricks={gameState.tricks}
            contract={gameState.contract}
          />
        )}
      </div>

      {/* Score Display */}
      <div className="score-section">
        <div className="score-display">
          <div className="score-item">
            <span className="score-label">We</span>
            <span className="score-value">{gameState.score.northSouth}</span>
          </div>
          <div className="score-divider">-</div>
          <div className="score-item">
            <span className="score-label">They</span>
            <span className="score-value">{gameState.score.eastWest}</span>
          </div>
        </div>
        
        {gameState.rubberScore.northSouth > 0 || gameState.rubberScore.eastWest > 0 ? (
          <div className="rubber-score">
            <div className="rubber-item">
              <span className="rubber-label">Games</span>
              <span className="rubber-value">
                {gameState.rubberScore.northSouth} - {gameState.rubberScore.eastWest}
              </span>
            </div>
          </div>
        ) : null}
      </div>
    </div>
  );
};

export default CenterArea;

/**
 * GameStatus component displaying current game phase and status information
 */

import React from 'react';
import { GameState, GamePhase } from '../../types/bridge';

interface GameStatusProps {
  gameState: GameState;
}

const GameStatus: React.FC<GameStatusProps> = ({ gameState }) => {
  const getPhaseDisplay = (phase: GamePhase): string => {
    switch (phase) {
      case GamePhase.BIDDING:
        return 'Bidding';
      case GamePhase.PLAYING:
        return 'Playing';
      case GamePhase.SCORING:
        return 'Scoring';
      case GamePhase.FINISHED:
        return 'Finished';
      default:
        return 'Unknown';
    }
  };

  const getVulnerabilityDisplay = () => {
    const { northSouth, eastWest } = gameState.vulnerabilities;
    
    if (!northSouth && !eastWest) {
      return 'None vulnerable';
    } else if (northSouth && eastWest) {
      return 'Both vulnerable';
    } else if (northSouth) {
      return 'N-S vulnerable';
    } else {
      return 'E-W vulnerable';
    }
  };

  const getCurrentPlayerName = () => {
    return gameState.players[gameState.currentPlayer].name;
  };

  return (
    <div className="game-status" data-testid="game-status">
      <div className="status-row primary">
        <div className="status-item phase">
          <span className="label">Phase:</span>
          <span className="value">{getPhaseDisplay(gameState.phase)}</span>
        </div>
        
        <div className="status-item current-player">
          <span className="label">Current:</span>
          <span className="value">{getCurrentPlayerName()}</span>
        </div>
      </div>

      <div className="status-row secondary">
        <div className="status-item dealer">
          <span className="label">Dealer:</span>
          <span className="value">{gameState.dealer}</span>
        </div>
        
        <div className="status-item vulnerability">
          <span className="label">Vulnerability:</span>
          <span className="value">{getVulnerabilityDisplay()}</span>
        </div>
      </div>

      {gameState.contract && (
        <div className="status-row contract">
          <div className="status-item contract-info">
            <span className="label">Contract:</span>
            <span className="value contract-display">
              {gameState.contract.level}{gameState.contract.suit}
              {gameState.contract.doubled !== 'none' && (
                <span className="doubled">
                  {gameState.contract.doubled === 'doubled' ? ' X' : ' XX'}
                </span>
              )}
            </span>
          </div>
          
          <div className="status-item declarer">
            <span className="label">Declarer:</span>
            <span className="value">{gameState.players[gameState.contract.declarer].name}</span>
          </div>
        </div>
      )}

      {gameState.dummy && (
        <div className="status-row dummy">
          <div className="status-item dummy-info">
            <span className="label">Dummy:</span>
            <span className="value">{gameState.players[gameState.dummy].name}</span>
          </div>
        </div>
      )}

      <div className="status-row game-info">
        <div className="status-item game-number">
          <span className="label">Game:</span>
          <span className="value">{gameState.gameNumber}</span>
        </div>
        
        <div className="status-item game-id">
          <span className="label">ID:</span>
          <span className="value">{gameState.id}</span>
        </div>
      </div>
    </div>
  );
};

export default GameStatus;

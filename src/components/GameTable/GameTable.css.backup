/**
 * GameTable component styles
 * Creates the bridge table layout with four player positions
 */

.game-table {
  position: relative;
  width: 100%;
  max-width: 1200px;
  height: 100%;
  max-height: 800px;
  aspect-ratio: 3/2;
  display: grid;
  grid-template-areas:
    ". north ."
    "west center east"
    ". south .";
  grid-template-columns: 1fr 2fr 1fr;
  grid-template-rows: 1fr 2fr 1fr;
  gap: 20px;
  padding: 15px;
  max-width: 100%;
  overflow: visible;
  position: relative;
  background: radial-gradient(ellipse at center, #1a5c4a 0%, #0f4c3a 100%);
  border-radius: 20px;
  box-shadow: 
    0 20px 40px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
/* Player positions */
.player-position {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Grid area assignments */
.north {
  grid-area: north;
}

.south {
  grid-area: south;
}

.east {
  grid-area: east;
}

.west {
  grid-area: west;
}

.center {
  grid-area: center;
}  grid-area: south;
}

.player-position.east {
  grid-area: east;
}

.player-position.west {
  grid-area: west;
}

/* Center area */
.center-area {
  grid-area: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 15px;
  padding: 15px;
  max-width: 100%;
  overflow: visible;
  position: relative;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(5px);
}

/* Game status overlay - moved to upper right, horizontal layout */
.game-status {
  position: absolute;
  top: 10px;
  right: 10px;
  background: rgba(0, 0, 0, 0.8);
  border-radius: 8px;
  padding: 8px 16px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  z-index: 10;
  display: flex;
  align-items: center;
  gap: 16px;
  color: white;
  font-size: 14px;
  max-width: 400px;
}
.status-info {
  display: flex;
  flex-direction: column;
  gap: 6px;
  font-size: 12px;
  color: #cccccc;
}

.status-info span {
  display: block;
}

.status-info .phase {
  color: #4a9eff;
  font-weight: 600;
}

.status-info .current-player {
  color: #ffd700;
}

.status-info .contract {
  color: #ff6b6b;
}

/* Loading state */
.game-table.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  grid-template-areas: none;
  grid-template-columns: none;
  grid-template-rows: none;
}

.loading-spinner {
  text-align: center;
}

.loading-spinner p {
  margin-bottom: 20px;
  color: #cccccc;
  font-size: 16px;
}

/* Error state */
.game-table.error {
  display: flex;
  align-items: center;
  justify-content: center;
  grid-template-areas: none;
  grid-template-columns: none;
  grid-template-rows: none;
}

/* Player position containers */
.player-position-container {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  padding: 15px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.player-position-container.current {
  background: rgba(255, 215, 0, 0.1);
  border-color: rgba(255, 215, 0, 0.3);
  box-shadow: 0 0 20px rgba(255, 215, 0, 0.2);
}

.player-position-container.dummy {
  background: rgba(74, 158, 255, 0.1);
  border-color: rgba(74, 158, 255, 0.3);
}

/* Current player indicator */
.current-player-indicator {
  position: absolute;
  top: -5px;
  right: -5px;
  width: 20px;
  height: 20px;
  z-index: 5;
}

.indicator-pulse {
  width: 100%;
  height: 100%;
  background: #ffd700;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.7;
  }
  100% {
    transform: scale(0.8);
    opacity: 1;
  }
}

/* Responsive design */
@media (max-width: 1024px) {
  .game-table {
    max-width: 900px;
    max-height: 600px;
    gap: 15px;
    padding: 15px;
  }
  
  .player-position-container {
    padding: 10px;
    gap: 8px;
  }
}

@media (max-width: 768px) {
  .game-table {
    max-width: 100%;
    max-height: 500px;
    gap: 10px;
    padding: 10px;
    grid-template-columns: 0.8fr 2fr 0.8fr;
  }
  
  .center-area {
    padding: 15px;
  }
  
  .game-status {
    top: 5px;
    right: 5px;
    padding: 8px;
  }
  
  .status-info {
    font-size: 11px;
    gap: 4px;
  }
}

@media (max-width: 480px) {
  .game-table {
    border-radius: 10px;
    gap: 8px;
    padding: 8px;
  }
  
  .player-position-container {
    padding: 8px;
    gap: 6px;
  }
  
  .center-area {
    padding: 10px;
    border-radius: 10px;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .player-position-container {
    transition: none;
  }
  
  .indicator-pulse {
    animation: none;
    opacity: 1;
  }
}


/* Score Board Container */
.score-board-container {
  position: absolute;
  bottom: 20px;
/* Game Status Container - moved to upper right */
.game-status-container {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 100;
  display: flex;
  align-items: center;
  gap: 12px;
}  position: absolute;
  bottom: 20px;
  right: 20px;
  z-index: 100;
}


/* Responsive Design Enhancements */
@media (max-width: 1024px) {
  .game-table {
    max-width: 900px;
    max-height: 600px;
    gap: var(--spacing-sm, 8px);
  }
  
  .player-position {
    padding: var(--spacing-sm, 8px);
  }
}

@media (max-width: 768px) {
  .game-table {
    max-width: 700px;
    max-height: 500px;
    gap: var(--spacing-xs, 4px);
    grid-template-areas:
      "north north north"
      "west center east"
      "south south south";
    grid-template-columns: 1fr 2fr 1fr;
    grid-template-rows: 1fr 2fr 1fr;
  }
  
  .player-position {
    padding: var(--spacing-xs, 4px);
    font-size: var(--font-size-sm, 14px);
  }
  
  .score-board-container,
  .game-status-container {
    position: static;
    width: 100%;
    margin: var(--spacing-sm, 8px) 0;
  }
}

@media (max-width: 480px) {
  .game-table {
    max-width: 100%;
    max-height: 400px;
    padding: var(--spacing-xs, 4px);
  }
  
  .player-position {
    min-height: 60px;
    font-size: var(--font-size-xs, 12px);
  }
  
  .center-area {
    padding: var(--spacing-xs, 4px);
  }
}

/* Landscape orientation on mobile */
@media (max-width: 768px) and (orientation: landscape) {
  .game-table {
    max-height: 350px;
    grid-template-areas:
      ". north ."
      "west center east"
      ". south .";
    grid-template-columns: 1fr 3fr 1fr;
    grid-template-rows: 1fr 2fr 1fr;
  }
}

/* Bidding Panel Overlay - positioned to not interfere with grid layout */
.bidding-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 100;
  pointer-events: none;
}

.bidding-overlay .bidding-panel {
  pointer-events: all;
  transform: scale(0.8);
  min-width: 240px;
  max-width: 280px;
  background: rgba(0, 0, 0, 0.95);
  border: 2px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.6);
}





/* Center content styling */
.center-content {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  padding: 20px;
  text-align: center;
  color: white;
}

.center-content h3 {
  margin: 0 0 10px 0;
  color: #ffd700;
}

.center-content p {
  margin: 5px 0;
  font-size: 14px;
  opacity: 0.9;
}

.bidding-area {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  justify-content: center;
  margin-top: 15px;
}

.bid-button {
  background: #4a8c6a;
  color: white;
  border: 1px solid #6aa88a;
  border-radius: 6px;
  padding: 8px 12px;
  font-size: 12px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.2s ease;
}

.bid-button:hover {
  background: #5aa87a;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.bid-button:active {
  transform: translateY(0);
}


/* Horizontal status info layout */
.status-info {
  display: flex;
  align-items: center;
  gap: 12px;
  color: white;
  font-size: 13px;
}

.status-info > div {
  display: flex;
  align-items: center;
  gap: 4px;
}

.status-info .status-label {
  opacity: 0.8;
  font-size: 12px;
}

.status-info .status-value {
  font-weight: bold;
  color: #ffd700;
}

/* Compact horizontal game status */
.game-status-horizontal {
  background: rgba(0, 0, 0, 0.8);
  border-radius: 6px;
  padding: 6px 12px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  gap: 16px;
  color: white;
  font-size: 13px;
  white-space: nowrap;
}


/* Force status to upper right - override any conflicting styles */
.game-table-container .game-status-container {
  position: absolute !important;
  top: 15px !important;
  right: 15px !important;
  left: auto !important;
  bottom: auto !important;
  z-index: 1000 !important;
}

.game-table-container .game-status-indicator.compact {
  background: rgba(0, 0, 0, 0.85) !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
}


/**
 * Face-down cards integration test
 */

import React from 'react';
import { render, screen } from '@testing-library/react';
import PlayerPosition from './PlayerPosition';
import { Position, PlayerType, GamePhase, Suit, Rank } from '../../types/bridge';

// Create a complete game state with AI players having cards
const createTestGameState = () => {
  const sampleCards = [
    { suit: Suit.SPADES, rank: Rank.ACE },
    { suit: Suit.HEARTS, rank: Rank.KING },
    { suit: Suit.DIAMONDS, rank: Rank.QUEEN },
    { suit: Suit.CLUBS, rank: Rank.JACK },
    { suit: Suit.SPADES, rank: Rank.TEN },
    { suit: Suit.HEARTS, rank: Rank.NINE },
    { suit: Suit.DIAMONDS, rank: Rank.EIGHT },
    { suit: Suit.CLUBS, rank: Rank.SEVEN },
    { suit: Suit.SPADES, rank: Rank.SIX },
    { suit: Suit.HEARTS, rank: Rank.FIVE },
    { suit: Suit.DIAMONDS, rank: Rank.FOUR },
    { suit: Suit.CLUBS, rank: Rank.THREE },
    { suit: Suit.SPADES, rank: Rank.TWO }
  ];

  return {
    id: 'test-game',
    phase: GamePhase.BIDDING,
    dealer: Position.NORTH,
    currentPlayer: Position.NORTH,
    players: {
      [Position.NORTH]: {
        id: 'ai-north',
        name: 'AI North',
        position: Position.NORTH,
        type: PlayerType.AI,
        hand: [...sampleCards]
      },
      [Position.EAST]: {
        id: 'ai-east',
        name: 'AI East',
        position: Position.EAST,
        type: PlayerType.AI,
        hand: [...sampleCards]
      },
      [Position.SOUTH]: {
        id: 'human-south',
        name: 'Human Player',
        position: Position.SOUTH,
        type: PlayerType.HUMAN,
        hand: [...sampleCards]
      },
      [Position.WEST]: {
        id: 'ai-west',
        name: 'AI West',
        position: Position.WEST,
        type: PlayerType.AI,
        hand: [...sampleCards]
      }
    },
    auction: [],
    contract: null,
    tricks: [],
    currentTrick: [],
    dummy: null,
    vulnerabilities: { northSouth: false, eastWest: false },
    score: { northSouth: 0, eastWest: 0 },
    gameNumber: 1,
    rubberScore: { northSouth: 0, eastWest: 0 }
  };
};

describe('Face-Down Cards Integration', () => {
  it('should render face-down cards for AI North player', () => {
    const gameState = createTestGameState();
    
    render(
      <PlayerPosition
        position={Position.NORTH}
        player={gameState.players[Position.NORTH]}
        gameState={gameState}
        isCurrentPlayer={false}
        onGameStateChange={() => {}}
      />
    );
    
    // Should show AI player name
    expect(screen.getByText('AI N')).toBeInTheDocument();
    
    // Should show face-down cards (card backs)
    const cardBacks = document.querySelectorAll('.card-back');
    expect(cardBacks.length).toBe(13);
    
    // Should show card back design
    const cardBackDesigns = document.querySelectorAll('.card-back-design');
    expect(cardBackDesigns.length).toBe(13);
    
    // Should not show actual card content
    expect(screen.queryByText('A')).not.toBeInTheDocument();
    expect(screen.queryByText('♠')).not.toBeInTheDocument();
  });

  it('should render face-down cards for AI East player with vertical orientation', () => {
    const gameState = createTestGameState();
    
    const { container } = render(
      <PlayerPosition
        position={Position.EAST}
        player={gameState.players[Position.EAST]}
        gameState={gameState}
        isCurrentPlayer={false}
        onGameStateChange={() => {}}
      />
    );
    
    // Should show AI player name
    expect(screen.getByText('AI E')).toBeInTheDocument();
    
    // Should have vertical orientation
    const handElement = container.querySelector('.player-hand');
    expect(handElement).toHaveClass('vertical');
    
    // Should show face-down cards
    const cardBacks = document.querySelectorAll('.card-back');
    expect(cardBacks.length).toBe(13);
  });

  it('should render face-up cards for human South player', () => {
    const gameState = createTestGameState();
    
    render(
      <PlayerPosition
        position={Position.SOUTH}
        player={gameState.players[Position.SOUTH]}
        gameState={gameState}
        isCurrentPlayer={false}
        onGameStateChange={() => {}}
      />
    );
    
    // Should show human player name
    expect(screen.getByText('Your Hand')).toBeInTheDocument();
    
    // Should show actual card content (not card backs)
    expect(screen.getAllByText('A').length).toBeGreaterThan(0);
    expect(screen.getAllByText('♠').length).toBeGreaterThan(0);
    
    // Should not show card backs
    const cardBacks = document.querySelectorAll('.card-back');
    expect(cardBacks.length).toBe(0);
  });
});

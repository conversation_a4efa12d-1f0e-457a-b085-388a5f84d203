/**
 * Main GameTable component displaying the bridge game table with four player positions
 * This is the central component that orchestrates the entire game UI
 */

import React, { useState, useEffect } from 'react';
import { Position, GameState, PlayerType, GamePhase, Bid } from '../../types/bridge';
import { createNewGame, createPlayers } from '../../lib/bridge/gameStateUtils';
import { AIPlayer } from '../../lib/bridge/aiPlayer';import PlayerPosition from './PlayerPosition';
import CenterArea from './CenterArea';
import BiddingPanel from '../BiddingPanel/BiddingPanel';
import PlayingArea from '../PlayingArea/PlayingArea';import './GameTable.css';
import ScoreBoard from '../ScoreBoard/ScoreBoard';
import GameStatusIndicator from '../GameStatusIndicator/GameStatusIndicator';interface GameTableProps {
  /** Optional initial game state for testing */
  initialGameState?: GameState;
  /** Human player position (defaults to South) */
  humanPosition?: Position;
}

const GameTable: React.FC<GameTableProps> = ({ 
  initialGameState,
  humanPosition = Position.SOUTH 
}) => {
  const [gameState, setGameState] = useState<GameState | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const handleBid = (bid: Bid) => {
    if (!gameState) return;
    
    try {
      import('../../lib/bridge/gameStateUtils').then(({ processBid }) => {
        const newGameState = processBid(gameState, bid);
        setGameState(newGameState);
      });
    } catch (error) {
      console.error('Error processing bid:', error);
    }
  };

  // Initialize game state
  useEffect(() => {
    const initializeGame = () => {
      try {
        if (initialGameState) {
          setGameState(initialGameState);
        } else {
          // Create a new game with AI players
          const players = createPlayers(humanPosition, 'You');
          const newGame = createNewGame('game-1', players, Position.NORTH);
          setGameState(newGame);
        }
      } catch (error) {
        console.error('Failed to initialize game:', error);
      } finally {
        setIsLoading(false);
      }
    };

    initializeGame();
  }, [initialGameState, humanPosition]);

  // AI Turn Automation
  useEffect(() => {
    if (!gameState || isLoading) return;
    
    const currentPlayer = gameState.currentPlayer;
    const currentPlayerData = gameState.players[currentPlayer];
    
    // Check if it's an AI player's turn
    if (currentPlayerData.type === PlayerType.AI) {
      const aiDelay = 1000 + Math.random() * 1000; // 1-2 second delay for realism
      
      const timer = setTimeout(() => {
        try {
          if (gameState.phase === GamePhase.BIDDING) {
            // AI makes a bid
            const aiPlayer = new AIPlayer(currentPlayer);
            const aiBid = aiPlayer.makeBid(gameState.hands[currentPlayer], gameState.auction);
            if (aiBid) {
              const fullBid: Bid = {
                player: currentPlayer,
                value: aiBid,
                timestamp: new Date()
              };
              handleBid(fullBid);
            }
          } else if (gameState.phase === GamePhase.PLAYING) {
            // AI plays a card
            const aiPlayer = new AIPlayer(currentPlayer);
            const aiCard = aiPlayer.playCard(gameState);
            if (aiCard) {
              // Handle AI card play (you'll need to implement this)
              console.log(`AI ${currentPlayer} plays:`, aiCard);
            }
          }
        } catch (error) {
          console.error(`AI ${currentPlayer} error:`, error);
        }
      }, aiDelay);
      
      return () => clearTimeout(timer);
    }
  }, [gameState?.currentPlayer, gameState?.phase, isLoading]);
  if (isLoading) {
    return (
      <div className="game-table loading">
        <div className="loading-spinner">
          <div className="spinner"></div>
          <p>Setting up the bridge table...</p>
        </div>
      </div>
    );
  }

  if (!gameState) {
    return (
      <div className="game-table error">
        <div className="error-message">
          <h2>Failed to load game</h2>
          <p>There was an error setting up the bridge table. Please refresh the page.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="game-table" data-testid="game-table">
      {/* North Player */}
      <div className="player-position north">
        <PlayerPosition
          position={Position.NORTH}
          player={gameState.players[Position.NORTH]}
          gameState={gameState}
          isCurrentPlayer={gameState.currentPlayer === Position.NORTH}
          onGameStateChange={setGameState}
        />
      </div>

      {/* West Player */}
      <div className="player-position west">
        <PlayerPosition
          position={Position.WEST}
          player={gameState.players[Position.WEST]}
          gameState={gameState}
          isCurrentPlayer={gameState.currentPlayer === Position.WEST}
          onGameStateChange={setGameState}
        />
      </div>

      {/* Center Area - Bidding Panel, Playing Area, or Game Center */}
      <div className="center-area">
        {gameState.phase === GamePhase.BIDDING ? (
          <BiddingPanel
            gameState={gameState}
            currentPlayer={gameState.currentPlayer}
            onBid={handleBid}
                    isPlayerTurn={gameState.players[gameState.currentPlayer].type === PlayerType.HUMAN}
        />
  ) : gameState.phase === GamePhase.PLAYING ? (
    <PlayingArea
      gameState={gameState}
      onGameStateChange={setGameState}
      humanPosition={humanPosition}
    />
  ) : (
    <CenterArea
      gameState={gameState}
      onGameStateChange={setGameState}
    />        )}
      </div>

      {/* East Player */}
      <div className="player-position east">
        <PlayerPosition
          position={Position.EAST}
          player={gameState.players[Position.EAST]}
          gameState={gameState}
          isCurrentPlayer={gameState.currentPlayer === Position.EAST}
          onGameStateChange={setGameState}
        />
      </div>

      {/* South Player (Human) */}
      <div className="player-position south">
        <PlayerPosition
          position={Position.SOUTH}
          player={gameState.players[Position.SOUTH]}
          gameState={gameState}
          isCurrentPlayer={gameState.currentPlayer === Position.SOUTH}
          onGameStateChange={setGameState}
        />
      </div>

      {/* Game Status Indicator */}
      <div className="game-status-container">
        <GameStatusIndicator
          gameState={gameState}
          humanPosition={humanPosition}
          compact={false}
        />
      </div>
    </div>
  );
};

export default GameTable;

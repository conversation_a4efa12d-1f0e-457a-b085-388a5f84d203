/**
 * Apple-Inspired GameTable Layout
 * Clean, elegant bridge table with subtle depth and smooth interactions
 */

@import '../../styles/apple-design-system.css';

/* Apple-style Game Table */
.game-table {
  width: 100%;
  height: 100%;
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  grid-template-rows: 80px 1fr 1fr 1fr;
  grid-template-areas:
    "north-info north-hand north-info"
    "west-hand center east-hand"
    "west-info center east-info"
    "south-info south-hand south-info";
  gap: var(--spacing-md);
  padding: var(--spacing-2xl);
  box-sizing: border-box;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: var(--radius-3xl);
  position: relative;
  overflow: hidden;
  box-shadow: 
    inset 0 1px 0 rgba(255, 255, 255, 0.5),
    inset 0 -1px 0 rgba(0, 0, 0, 0.05);
}

/* Apple-style table surface effect */
.game-table::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 70% 70%, rgba(0, 0, 0, 0.02) 0%, transparent 50%);
  pointer-events: none;
  z-index: 1;
}

/* Player Position Areas */
.player-position {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.4);
  backdrop-filter: var(--blur-xs);
  -webkit-backdrop-filter: var(--blur-xs);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-xs);
  transition: all var(--transition-normal);
  z-index: 2;
}

.player-position:hover {
  background: rgba(255, 255, 255, 0.5);
  box-shadow: var(--shadow-sm);
}

/* Position-specific styling */
.player-position.north {
  grid-area: north-hand;
  align-items: flex-end;
}

.player-position.south {
  grid-area: south-hand;
  align-items: flex-start;
}

.player-position.east {
  grid-area: east-hand;
  justify-content: flex-start;
}

.player-position.west {
  grid-area: west-hand;
  justify-content: flex-end;
}

/* Apple-style Center Area */
.center-area {
  grid-area: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: var(--blur-sm);
  -webkit-backdrop-filter: var(--blur-sm);
  border: 1px solid rgba(255, 255, 255, 0.4);
  border-radius: var(--radius-2xl);
  box-shadow: 
    var(--shadow-md),
    inset 0 1px 0 rgba(255, 255, 255, 0.7);
  position: relative;
  z-index: 2;
}

.center-area::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at center, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  border-radius: inherit;
  pointer-events: none;
}

/* Apple-style Player Info Areas */
.player-info {
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.3);
  backdrop-filter: var(--blur-xs);
  -webkit-backdrop-filter: var(--blur-xs);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-xs);
  font-family: var(--font-family-system);
  font-size: var(--font-size-footnote);
  font-weight: var(--font-weight-medium);
  color: var(--text-secondary);
  z-index: 2;
  position: relative;
}

.player-info.north-info {
  grid-area: north-info;
}

.player-info.south-info {
  grid-area: south-info;
}

.player-info.east-info {
  grid-area: east-info;
}

.player-info.west-info {
  grid-area: west-info;
}

/* Apple-style Status Container */
.game-status-container {
  position: absolute;
  top: var(--spacing-lg);
  right: var(--spacing-lg);
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: var(--blur-md);
  -webkit-backdrop-filter: var(--blur-md);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: var(--radius-xl);
  padding: var(--spacing-md) var(--spacing-lg);
  box-shadow: var(--shadow-lg);
  z-index: var(--z-floating);
  min-width: 200px;
  max-width: 300px;
}

.game-status-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.6), transparent);
}

/* Apple-style Status Content */
.status-content {
  font-family: var(--font-family-system);
  font-size: var(--font-size-subheadline);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  line-height: 1.4;
}

.status-title {
  font-size: var(--font-size-headline);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.status-detail {
  font-size: var(--font-size-footnote);
  color: var(--text-secondary);
  margin-top: var(--spacing-xs);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .game-table {
    grid-template-rows: 70px 1fr 1fr 1fr;
    gap: var(--spacing-sm);
    padding: var(--spacing-xl);
    border-radius: var(--radius-2xl);
  }
  
  .game-status-container {
    top: var(--spacing-md);
    right: var(--spacing-md);
    min-width: 180px;
    padding: var(--spacing-sm) var(--spacing-md);
  }
}

@media (max-width: 768px) {
  .game-table {
    grid-template-rows: 60px 1fr 1fr 1fr;
    gap: var(--spacing-sm);
    padding: var(--spacing-lg);
    border-radius: var(--radius-xl);
  }
  
  .player-position,
  .center-area {
    border-radius: var(--radius-lg);
  }
  
  .player-info {
    border-radius: var(--radius-md);
    font-size: var(--font-size-caption1);
  }
  
  .game-status-container {
    position: relative;
    top: auto;
    right: auto;
    margin-top: var(--spacing-md);
    min-width: auto;
    max-width: none;
  }
}

@media (max-width: 480px) {
  .game-table {
    grid-template-rows: 50px 1fr 1fr 1fr;
    gap: var(--spacing-xs);
    padding: var(--spacing-md);
    border-radius: var(--radius-lg);
  }
  
  .player-position,
  .center-area {
    border-radius: var(--radius-md);
  }
  
  .player-info {
    border-radius: var(--radius-sm);
    font-size: var(--font-size-caption2);
  }
}

/* Dark Mode Adaptations */
@media (prefers-color-scheme: dark) {
  .game-table {
    background: linear-gradient(135deg, #1c1c1e 0%, #2c2c2e 100%);
    box-shadow: 
      inset 0 1px 0 rgba(255, 255, 255, 0.1),
      inset 0 -1px 0 rgba(0, 0, 0, 0.2);
  }
  
  .player-position {
    background: rgba(44, 44, 46, 0.6);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  .player-position:hover {
    background: rgba(44, 44, 46, 0.8);
  }
  
  .center-area {
    background: rgba(44, 44, 46, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.15);
    box-shadow: 
      var(--shadow-md),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
  }
  
  .player-info {
    background: rgba(28, 28, 30, 0.6);
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: var(--apple-gray-300);
  }
  
  .game-status-container {
    background: rgba(28, 28, 30, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .game-table {
    background: white;
    border: 2px solid black;
  }
  
  .player-position,
  .center-area,
  .player-info,
  .game-status-container {
    background: white;
    border: 2px solid black;
    backdrop-filter: none;
  }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  .player-position {
    transition: none;
  }
  
  .player-position:hover {
    transform: none;
  }
}

/* Print Styles */
@media print {
  .game-table {
    background: white;
    border: 1px solid black;
  }
  
  .player-position,
  .center-area,
  .player-info {
    background: white;
    border: 1px solid black;
    backdrop-filter: none;
    box-shadow: none;
  }
  
  .game-status-container {
    position: static;
    background: white;
    border: 1px solid black;
    backdrop-filter: none;
    box-shadow: none;
  }
}

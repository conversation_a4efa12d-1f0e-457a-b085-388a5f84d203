/**
 * Main GameTable component with AI automation
 * This version includes automatic AI player moves
 */

import React, { useState, useEffect } from 'react';
import { Position, GameState, PlayerType, GamePhase, Bid } from '../../types/bridge';
import { createNewGame, createPlayers } from '../../lib/bridge/gameStateUtils';
import PlayerPosition from './PlayerPosition';
import CenterArea from './CenterArea';
import BiddingPanel from '../BiddingPanel/BiddingPanel';
import PlayingArea from '../PlayingArea/PlayingArea';
import './GameTable.css';
import ScoreBoard from '../ScoreBoard/ScoreBoard';
import GameStatusIndicator from '../GameStatusIndicator/GameStatusIndicator';
import GameAnimationManager from '../GameAnimationManager/GameAnimationManager';

interface GameTableProps {
  /** Optional initial game state for testing */
  initialGameState?: GameState;
  /** Human player position (defaults to South) */
  humanPosition?: Position;
}

const GameTable: React.FC<GameTableProps> = ({ 
  initialGameState,
  humanPosition = Position.SOUTH 
}) => {
  const [gameState, setGameState] = useState<GameState | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isAnimationEnabled, setIsAnimationEnabled] = useState(true);
  const [currentAnimation, setCurrentAnimation] = useState<string | null>(null);
  
  // AI automation settings
  const aiDelay = 1500; // 1.5 seconds for AI moves

  // Handle animation completion
  const handleAnimationComplete = (animationType: string) => {
    setCurrentAnimation(null);
    console.log(`Animation completed: ${animationType}`);
  };

  const handleBid = (bid: Bid) => {
    if (!gameState) return;
    
    try {
      import('../../lib/bridge/gameStateUtils').then(({ processBid }) => {
        const newGameState = processBid(gameState, bid);
        setGameState(newGameState);
      });
    } catch (error) {
      console.error('Error processing bid:', error);
    }
  };

  // AI automation - handle AI player turns
  useEffect(() => {
    if (!gameState || isLoading) return;
    
    const currentPlayer = gameState.currentPlayer;
    const currentPlayerType = gameState.players[currentPlayer].type;
    
    // Only automate AI players
    if (currentPlayerType === PlayerType.AI) {
      const timer = setTimeout(async () => {
        try {
          const { AIPlayer } = await import('../../lib/bridge/aiPlayer');
          
          if (gameState.phase === GamePhase.BIDDING) {
            // AI makes a bid
            const aiPlayer = new AIPlayer(currentPlayer);
            const aiBid = aiPlayer.makeBid(gameState.players[currentPlayer].hand, gameState.auction);
            if (aiBid) {
              const fullBid: Bid = {
                player: currentPlayer,
                value: aiBid,
                timestamp: new Date()
              };
              handleBid(fullBid);
            }
          } else if (gameState.phase === GamePhase.PLAYING) {
            // AI plays a card
            const aiPlayer = new AIPlayer(currentPlayer);
            const aiCard = aiPlayer.playCard(gameState);
            if (aiCard) {
              // Import and use playCard function
              const { playCard } = await import('../../lib/bridge/gameStateUtils');
              const newGameState = playCard(gameState, aiCard, currentPlayer);
              setGameState(newGameState);
            }
          }
        } catch (error) {
          console.error(`AI ${currentPlayer} error:`, error);
        }
      }, aiDelay);
      
      return () => clearTimeout(timer);
    }
  }, [gameState, isLoading, aiDelay]);

  // Initialize game state
  useEffect(() => {
    const initializeGame = async () => {
      try {
        setIsLoading(true);
        
        if (initialGameState) {
          setGameState(initialGameState);
        } else {
          const players = createPlayers(humanPosition, 'Human Player');
          const newGameState = createNewGame('game-' + Date.now(), players, Position.SOUTH);
          setGameState(newGameState);
        }
      } catch (error) {
        console.error('Failed to initialize game:', error);
      } finally {
        setIsLoading(false);
      }
    };

    initializeGame();
  }, [initialGameState, humanPosition]);

  // Loading state
  if (isLoading) {
    return (
      <div className="game-table loading">
        <div className="loading-spinner">
          <div className="spinner"></div>
          <p>Setting up the bridge table...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (!gameState) {
    return (
      <div className="game-table error">
        <div className="error-message">
          <h2>Failed to load game</h2>
          <p>There was an error setting up the bridge table. Please refresh the page.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="game-table" data-testid="game-table">
      {/* Game Status - Dedicated grid area at top */}
      <div className="game-status-container">
        <GameStatusIndicator
          gameState={gameState}
          humanPosition={humanPosition}
          compact={true}
        />
      </div>

      {/* North Player */}
      <div className="player-position north">
        <PlayerPosition
          position={Position.NORTH}
          player={gameState.players[Position.NORTH]}
          gameState={gameState}
          isCurrentPlayer={gameState.currentPlayer === Position.NORTH}
          onGameStateChange={setGameState}
        />
      </div>

      {/* West Player */}
      <div className="player-position west">
        <PlayerPosition
          position={Position.WEST}
          player={gameState.players[Position.WEST]}
          gameState={gameState}
          isCurrentPlayer={gameState.currentPlayer === Position.WEST}
          onGameStateChange={setGameState}
        />
      </div>

      {/* Center Area - Bidding Panel, Playing Area, or Game Center */}
      <div className="center-area">
        {gameState.phase === GamePhase.BIDDING ? (
          <BiddingPanel
            gameState={gameState}
            currentPlayer={gameState.currentPlayer}
            onBid={handleBid}
            isPlayerTurn={gameState.players[gameState.currentPlayer].type === PlayerType.HUMAN}
          />
        ) : gameState.phase === GamePhase.PLAYING ? (
          <PlayingArea
            gameState={gameState}
            onGameStateChange={setGameState}
            humanPosition={humanPosition}
          />
        ) : (
          <CenterArea
            gameState={gameState}
            onGameStateChange={setGameState}
          />
        )}
      </div>

      {/* East Player */}
      <div className="player-position east">
        <PlayerPosition
          position={Position.EAST}
          player={gameState.players[Position.EAST]}
          gameState={gameState}
          isCurrentPlayer={gameState.currentPlayer === Position.EAST}
          onGameStateChange={setGameState}
        />
      </div>

      {/* South Player (Human) */}
      <div className="player-position south">
        <PlayerPosition
          position={Position.SOUTH}
          player={gameState.players[Position.SOUTH]}
          gameState={gameState}
          isCurrentPlayer={gameState.currentPlayer === Position.SOUTH}
          onGameStateChange={setGameState}
        />
      </div>

      {/* Game Animation Manager */}
      <GameAnimationManager
        gameState={gameState}
        onAnimationComplete={handleAnimationComplete}
        isAnimationEnabled={isAnimationEnabled}
      />
    </div>
  );
};

export default GameTable;

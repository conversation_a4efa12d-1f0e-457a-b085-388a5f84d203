/**
 * PlayerPosition component representing a single player's area on the bridge table
 * Shows player name, cards, and handles player interactions
 */

import React from 'react';
import { Position, Player, GameState, PlayerType, GamePhase } from '../../types/bridge';
import { canSeeCards } from '../../lib/bridge/gameStateUtils';
import PlayerHand from './PlayerHand';

interface PlayerPositionProps {
  position: Position;
  player: Player;
  gameState: GameState;
  isCurrentPlayer: boolean;
  onGameStateChange: (newGameState: GameState) => void;
  showCards?: boolean; // Override for open-handed play
}

const PlayerPosition: React.FC<PlayerPositionProps> = ({
  position,
  player,
  gameState,
  isCurrentPlayer,
  onGameStateChange,
  showCards = false
}) => {
  // Determine if cards should be visible
  const humanPosition = Position.SOUTH; // Human player is always South
  const shouldShowCards = showCards ||
                         canSeeCards(gameState, humanPosition, position) ||
                         (position === Position.SOUTH && player?.type === PlayerType.HUMAN); // Always show human player's own cards

  // For AI players, we want to show face-down cards (unless they're dummy and should be visible)
  const shouldShowFaceDownCards = player?.type === PlayerType.AI && !shouldShowCards;
  
  // Determine if this is the dummy position
  const isDummy = gameState.dummy === position;
  
  // Handle card play using proper game state utilities
  const handleCardPlay = async (cardIndex: number) => {
    if (!player?.hand || cardIndex >= player.hand.length) {
      return;
    }

    try {
      const playedCard = player.hand[cardIndex];

      // Import and use the proper playCard function
      const { playCard } = await import('../../lib/bridge/gameStateUtils');
      const newGameState = playCard(gameState, playedCard, position);

      // Update game state using proper deep cloning
      onGameStateChange(newGameState);
    } catch (error) {
      console.error('Error playing card:', error);
    }
  };

  // Determine if player can play cards
  const canPlayCards = isCurrentPlayer && 
                      gameState.phase === GamePhase.PLAYING &&
                      player?.type === PlayerType.HUMAN;

  // Get position-specific styling
  const getPositionClass = () => {
    switch (position) {
      case Position.NORTH:
        return 'player-position-north';
      case Position.SOUTH:
        return 'player-position-south';
      case Position.EAST:
        return 'player-position-east';
      case Position.WEST:
        return 'player-position-west';
      default:
        return 'player-position-default';
    }
  };

  return (
    <div className={`player-position-container ${getPositionClass()}`}>
      {/* Player Info */}
      <div className="player-info">
        <div className="player-name">
          {position === Position.SOUTH && player?.type === PlayerType.HUMAN
            ? "Your Hand"
            : `AI ${position}${isDummy ? ' (Dummy)' : ''}`}
        </div>

        {/* Current Player Indicator - shortened */}
        {isCurrentPlayer && (
          <div className="current-player-indicator">
            CURRENT
          </div>
        )}
      </div>

      {/* Player Hand - Always render if player exists, even with empty hand */}
      {player && (
        <PlayerHand
          cards={player.hand || []}
          position={position}
          shouldShowCards={shouldShowCards}
          canPlayCards={canPlayCards}
          onCardPlay={handleCardPlay}
          gameState={gameState}
        />
      )}

      {/* Card Count (only when no cards are shown at all) */}
      {!shouldShowCards && !shouldShowFaceDownCards && player?.hand && (
        <div className="card-count">
          {player.hand.length} cards
        </div>
      )}
    </div>
  );
};

export default PlayerPosition;

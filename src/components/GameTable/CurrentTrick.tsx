/**
 * CurrentTrick component displaying the cards played in the current trick
 */

import React from 'react';
import { PlayedCard, Trick, Contract, Position } from '../../types/bridge';
import CardComponent from '../Card/Card';

interface CurrentTrickProps {
  currentTrick: PlayedCard[];
  completedTricks: Trick[];
  contract: Contract | null;
}

const CurrentTrick: React.FC<CurrentTrickProps> = ({
  currentTrick,
  completedTricks,
  contract
}) => {
  const getCardPosition = (playerPosition: Position): string => {
    switch (playerPosition) {
      case Position.NORTH:
        return 'north';
      case Position.SOUTH:
        return 'south';
      case Position.EAST:
        return 'east';
      case Position.WEST:
        return 'west';
      default:
        return 'center';
    }
  };

  return (
    <div className="current-trick" data-testid="current-trick">
      <div className="trick-area">
        {/* Current trick cards */}
        {currentTrick.map((playedCard, index) => (
          <div
            key={`${playedCard.player}-${index}`}
            className={`trick-card ${getCardPosition(playedCard.player)}`}
          >
            <CardComponent
              card={playedCard.card}
              size="medium"
            />
            <div className="player-label">
              {playedCard.player}
            </div>
          </div>
        ))}

        {/* Empty positions for remaining cards */}
        {Array.from({ length: 4 - currentTrick.length }).map((_, index) => (
          <div
            key={`empty-${index}`}
            className="trick-card empty"
          >
            <div className="empty-card-placeholder">
              <div className="placeholder-content">?</div>
            </div>
          </div>
        ))}
      </div>

      {/* Trick information */}
      <div className="trick-info">
        <div className="trick-count">
          Trick {completedTricks.length + 1} of 13
        </div>
        
        {contract && (
          <div className="contract-info">
            <span className="contract-display">
              {contract.level}{contract.suit}
            </span>
            <span className="declarer">
              by {contract.declarer}
            </span>
            {contract.doubled !== 'none' && (
              <span className="doubled">
                {contract.doubled === 'doubled' ? 'X' : 'XX'}
              </span>
            )}
          </div>
        )}

        {completedTricks.length > 0 && (
          <div className="last-trick">
            Last trick won by: {completedTricks[completedTricks.length - 1].winner}
          </div>
        )}
      </div>
    </div>
  );
};

export default CurrentTrick;

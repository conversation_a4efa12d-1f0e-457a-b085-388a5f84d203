/**
 * CardAnimations component for smooth card transitions and effects
 * Provides dealing, playing, and collection animations
 */

import React, { useState, useEffect, useRef } from 'react';
import { Card, Position } from '../../types/bridge';
import CardComponent from '../Card/Card';
import './CardAnimations.css';

interface CardAnimationProps {
  card: Card;
  startPosition: { x: number; y: number };
  endPosition: { x: number; y: number };
  animationType: 'deal' | 'play' | 'collect' | 'flip' | 'hover';
  duration?: number;
  delay?: number;
  onComplete?: () => void;
  size?: 'small' | 'medium' | 'large';
}

const CardAnimation: React.FC<CardAnimationProps> = ({
  card,
  startPosition,
  endPosition,
  animationType,
  duration = 800,
  delay = 0,
  onComplete,
  size = 'medium'
}) => {
  const [isAnimating, setIsAnimating] = useState(false);
  const [isComplete, setIsComplete] = useState(false);
  const cardRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsAnimating(true);
    }, delay);

    return () => clearTimeout(timer);
  }, [delay]);

  useEffect(() => {
    if (isAnimating) {
      const timer = setTimeout(() => {
        setIsComplete(true);
        onComplete?.();
      }, duration);

      return () => clearTimeout(timer);
    }
  }, [isAnimating, duration, onComplete]);

  const getAnimationClass = () => {
    const baseClass = `card-animation ${animationType}`;
    if (isAnimating) return `${baseClass} animating`;
    if (isComplete) return `${baseClass} complete`;
    return baseClass;
  };

  const getAnimationStyle = (): React.CSSProperties => {
    const deltaX = endPosition.x - startPosition.x;
    const deltaY = endPosition.y - startPosition.y;

    return {
      '--start-x': `${startPosition.x}px`,
      '--start-y': `${startPosition.y}px`,
      '--end-x': `${endPosition.x}px`,
      '--end-y': `${endPosition.y}px`,
      '--delta-x': `${deltaX}px`,
      '--delta-y': `${deltaY}px`,
      '--duration': `${duration}ms`,
      '--delay': `${delay}ms`,
      transform: isAnimating 
        ? `translate(${endPosition.x}px, ${endPosition.y}px)` 
        : `translate(${startPosition.x}px, ${startPosition.y}px)`,
      transition: isAnimating ? `transform ${duration}ms cubic-bezier(0.4, 0, 0.2, 1)` : 'none'
    } as React.CSSProperties;
  };

  return (
    <div
      ref={cardRef}
      className={getAnimationClass()}
      style={getAnimationStyle()}
      data-testid="card-animation"
    >
      <CardComponent
        card={card}
        size={size}
        isPlayable={false}
      />
    </div>
  );
};

// Dealing animation component
interface DealingAnimationProps {
  cards: Card[];
  targetPositions: Position[];
  deckPosition: { x: number; y: number };
  playerPositions: Record<Position, { x: number; y: number }>;
  onDealComplete?: () => void;
  dealingSpeed?: number;
}

export const DealingAnimation: React.FC<DealingAnimationProps> = ({
  cards,
  targetPositions,
  deckPosition,
  playerPositions,
  onDealComplete,
  dealingSpeed = 100
}) => {
  const [currentCardIndex, setCurrentCardIndex] = useState(0);
  const [completedCards, setCompletedCards] = useState<Set<number>>(new Set());

  useEffect(() => {
    if (currentCardIndex < cards.length) {
      const timer = setTimeout(() => {
        setCurrentCardIndex(prev => prev + 1);
      }, dealingSpeed);

      return () => clearTimeout(timer);
    }
  }, [currentCardIndex, cards.length, dealingSpeed]);

  useEffect(() => {
    if (completedCards.size === cards.length && cards.length > 0) {
      onDealComplete?.();
    }
  }, [completedCards.size, cards.length, onDealComplete]);

  const handleCardComplete = (index: number) => {
    setCompletedCards(prev => new Set([...prev, index]));
  };

  return (
    <div className="dealing-animation" data-testid="dealing-animation">
      {cards.slice(0, currentCardIndex + 1).map((card, index) => {
        const targetPosition = targetPositions[index % targetPositions.length];
        const endPos = playerPositions[targetPosition];
        
        return (
          <CardAnimation
            key={`${card.suit}-${card.rank}-${index}`}
            card={card}
            startPosition={deckPosition}
            endPosition={endPos}
            animationType="deal"
            duration={600}
            delay={index * dealingSpeed}
            onComplete={() => handleCardComplete(index)}
            size="small"
          />
        );
      })}
    </div>
  );
};

// Playing animation component
interface PlayingAnimationProps {
  card: Card;
  fromPosition: { x: number; y: number };
  toPosition: { x: number; y: number };
  onPlayComplete?: () => void;
}

export const PlayingAnimation: React.FC<PlayingAnimationProps> = ({
  card,
  fromPosition,
  toPosition,
  onPlayComplete
}) => {
  return (
    <CardAnimation
      card={card}
      startPosition={fromPosition}
      endPosition={toPosition}
      animationType="play"
      duration={500}
      onComplete={onPlayComplete}
      size="medium"
    />
  );
};

// Trick collection animation
interface TrickCollectionProps {
  cards: Card[];
  cardPositions: { x: number; y: number }[];
  winnerPosition: { x: number; y: number };
  onCollectionComplete?: () => void;
}

export const TrickCollection: React.FC<TrickCollectionProps> = ({
  cards,
  cardPositions,
  winnerPosition,
  onCollectionComplete
}) => {
  const [completedCards, setCompletedCards] = useState<Set<number>>(new Set());

  useEffect(() => {
    if (completedCards.size === cards.length && cards.length > 0) {
      const timer = setTimeout(() => {
        onCollectionComplete?.();
      }, 300);

      return () => clearTimeout(timer);
    }
  }, [completedCards.size, cards.length, onCollectionComplete]);

  const handleCardComplete = (index: number) => {
    setCompletedCards(prev => new Set([...prev, index]));
  };

  return (
    <div className="trick-collection" data-testid="trick-collection">
      {cards.map((card, index) => (
        <CardAnimation
          key={`${card.suit}-${card.rank}-${index}`}
          card={card}
          startPosition={cardPositions[index]}
          endPosition={winnerPosition}
          animationType="collect"
          duration={400}
          delay={index * 100}
          onComplete={() => handleCardComplete(index)}
          size="small"
        />
      ))}
    </div>
  );
};

// Card flip animation
interface CardFlipProps {
  card: Card;
  position: { x: number; y: number };
  isRevealed: boolean;
  onFlipComplete?: () => void;
}

export const CardFlip: React.FC<CardFlipProps> = ({
  card,
  position,
  isRevealed,
  onFlipComplete
}) => {
  const [isFlipping, setIsFlipping] = useState(false);

  useEffect(() => {
    setIsFlipping(true);
    const timer = setTimeout(() => {
      setIsFlipping(false);
      onFlipComplete?.();
    }, 600);

    return () => clearTimeout(timer);
  }, [isRevealed, onFlipComplete]);

  return (
    <div 
      className={`card-flip ${isFlipping ? 'flipping' : ''} ${isRevealed ? 'revealed' : 'hidden'}`}
      style={{ 
        transform: `translate(${position.x}px, ${position.y}px)`,
        position: 'absolute'
      }}
      data-testid="card-flip"
    >
      <div className="card-flip-inner">
        <div className="card-flip-front">
          <div className="card-back" />
        </div>
        <div className="card-flip-back">
          <CardComponent card={card} size="medium" isPlayable={false} />
        </div>
      </div>
    </div>
  );
};

// Hover animation wrapper
interface CardHoverProps {
  children: React.ReactNode;
  isEnabled?: boolean;
  hoverScale?: number;
  hoverRotation?: number;
}

export const CardHover: React.FC<CardHoverProps> = ({
  children,
  isEnabled = true,
  hoverScale = 1.05,
  hoverRotation = 2
}) => {
  const [isHovered, setIsHovered] = useState(false);

  if (!isEnabled) {
    return <>{children}</>;
  }

  return (
    <div
      className={`card-hover ${isHovered ? 'hovered' : ''}`}
      style={{
        '--hover-scale': hoverScale,
        '--hover-rotation': `${hoverRotation}deg`
      } as React.CSSProperties}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      data-testid="card-hover"
    >
      {children}
    </div>
  );
};

export default CardAnimation;

/**
 * Card animation styles
 */

/* Base card animation */
.card-animation {
  position: absolute;
  z-index: 1000;
  pointer-events: none;
  will-change: transform;
}

/* Deal animation */
.card-animation.deal {
  animation: dealCard var(--duration, 600ms) cubic-bezier(0.4, 0, 0.2, 1) forwards;
  animation-delay: var(--delay, 0ms);
}

@keyframes dealCard {
  0% {
    transform: translate(var(--start-x), var(--start-y)) rotate(0deg) scale(0.8);
    opacity: 0.8;
  }
  20% {
    transform: translate(
      calc(var(--start-x) + var(--delta-x) * 0.1), 
      calc(var(--start-y) + var(--delta-y) * 0.1)
    ) rotate(5deg) scale(0.9);
    opacity: 0.9;
  }
  60% {
    transform: translate(
      calc(var(--start-x) + var(--delta-x) * 0.8), 
      calc(var(--start-y) + var(--delta-y) * 0.8)
    ) rotate(-2deg) scale(1.05);
    opacity: 1;
  }
  100% {
    transform: translate(var(--end-x), var(--end-y)) rotate(0deg) scale(1);
    opacity: 1;
  }
}

/* Play animation */
.card-animation.play {
  animation: playCard var(--duration, 500ms) cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
  animation-delay: var(--delay, 0ms);
}

@keyframes playCard {
  0% {
    transform: translate(var(--start-x), var(--start-y)) rotate(0deg) scale(1);
    opacity: 1;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }
  30% {
    transform: translate(
      calc(var(--start-x) + var(--delta-x) * 0.3), 
      calc(var(--start-y) + var(--delta-y) * 0.3 - 20px)
    ) rotate(3deg) scale(1.1);
    opacity: 1;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
  }
  70% {
    transform: translate(
      calc(var(--start-x) + var(--delta-x) * 0.8), 
      calc(var(--start-y) + var(--delta-y) * 0.8 - 5px)
    ) rotate(-1deg) scale(1.02);
    opacity: 1;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.25);
  }
  100% {
    transform: translate(var(--end-x), var(--end-y)) rotate(0deg) scale(1);
    opacity: 1;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }
}

/* Collect animation */
.card-animation.collect {
  animation: collectCard var(--duration, 400ms) cubic-bezier(0.55, 0.085, 0.68, 0.53) forwards;
  animation-delay: var(--delay, 0ms);
}

@keyframes collectCard {
  0% {
    transform: translate(var(--start-x), var(--start-y)) rotate(0deg) scale(1);
    opacity: 1;
  }
  50% {
    transform: translate(
      calc(var(--start-x) + var(--delta-x) * 0.5), 
      calc(var(--start-y) + var(--delta-y) * 0.5)
    ) rotate(10deg) scale(0.8);
    opacity: 0.8;
  }
  100% {
    transform: translate(var(--end-x), var(--end-y)) rotate(0deg) scale(0.6);
    opacity: 0.6;
  }
}

/* Flip animation */
.card-flip {
  perspective: 1000px;
  width: 75px;
  height: 105px;
}

.card-flip-inner {
  position: relative;
  width: 100%;
  height: 100%;
  text-align: center;
  transition: transform 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  transform-style: preserve-3d;
}

.card-flip.flipping .card-flip-inner {
  transform: rotateY(180deg);
}

.card-flip.revealed .card-flip-inner {
  transform: rotateY(180deg);
}

.card-flip-front,
.card-flip-back {
  position: absolute;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.card-flip-front {
  background: linear-gradient(135deg, #1a365d, #2d5a87);
  border: 2px solid #4a9eff;
}

.card-flip-back {
  transform: rotateY(180deg);
}

.card-back {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #1a365d, #2d5a87);
  border-radius: 4px;
  position: relative;
}

.card-back::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 60%;
  height: 60%;
  background: repeating-linear-gradient(
    45deg,
    #4a9eff,
    #4a9eff 2px,
    transparent 2px,
    transparent 6px
  );
  border-radius: 4px;
  opacity: 0.6;
}

/* Hover animation */
.card-hover {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
}

.card-hover.hovered {
  transform: scale(var(--hover-scale, 1.05)) rotate(var(--hover-rotation, 2deg));
  z-index: 100;
}

/* Dealing animation container */
.dealing-animation {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1000;
}

/* Trick collection container */
.trick-collection {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1000;
}

/* Enhanced animations for different card sizes */
.card-animation.small {
  transform-origin: center center;
}

.card-animation.medium {
  transform-origin: center center;
}

.card-animation.large {
  transform-origin: center center;
}

/* Stagger animation for multiple cards */
.card-animation:nth-child(1) { animation-delay: calc(var(--delay, 0ms) + 0ms); }
.card-animation:nth-child(2) { animation-delay: calc(var(--delay, 0ms) + 100ms); }
.card-animation:nth-child(3) { animation-delay: calc(var(--delay, 0ms) + 200ms); }
.card-animation:nth-child(4) { animation-delay: calc(var(--delay, 0ms) + 300ms); }
.card-animation:nth-child(5) { animation-delay: calc(var(--delay, 0ms) + 400ms); }

/* Smooth entrance animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInFromLeft {
  from {
    opacity: 0;
    transform: translateX(-100px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInFromRight {
  from {
    opacity: 0;
    transform: translateX(100px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInFromTop {
  from {
    opacity: 0;
    transform: translateY(-100px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInFromBottom {
  from {
    opacity: 0;
    transform: translateY(100px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Utility classes for entrance animations */
.animate-fade-in-up {
  animation: fadeInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.animate-slide-in-left {
  animation: slideInFromLeft 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.animate-slide-in-right {
  animation: slideInFromRight 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.animate-slide-in-top {
  animation: slideInFromTop 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.animate-slide-in-bottom {
  animation: slideInFromBottom 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

/* Performance optimizations */
.card-animation,
.card-flip,
.card-hover {
  will-change: transform;
  backface-visibility: hidden;
  transform-style: preserve-3d;
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .card-animation,
  .card-flip-inner,
  .card-hover {
    animation: none !important;
    transition: none !important;
  }
  
  .card-animation.deal,
  .card-animation.play,
  .card-animation.collect {
    transform: translate(var(--end-x), var(--end-y)) !important;
    opacity: 1 !important;
  }
  
  .card-flip.revealed .card-flip-inner {
    transform: rotateY(180deg) !important;
  }
  
  .card-hover.hovered {
    transform: scale(1.02) !important;
  }
}

/* High contrast mode adjustments */
@media (prefers-contrast: high) {
  .card-back {
    border: 2px solid currentColor;
  }
  
  .card-back::before {
    background: currentColor;
    opacity: 0.8;
  }
}

/* Print mode - hide animations */
@media print {
  .card-animation,
  .dealing-animation,
  .trick-collection,
  .card-flip {
    display: none !important;
  }
}

/**
 * Unit tests for CardAnimations components
 */

import React from 'react';
import { render, screen, act, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { 
  DealingAnimation, 
  PlayingAnimation, 
  TrickCollection, 
  CardFlip, 
  CardHover 
} from './CardAnimations';
import { Card, Suit, Rank, Position } from '../../types/bridge';

// Mock the Card component
jest.mock('../Card/Card', () => {
  return function MockCard({ card, size }: any) {
    return (
      <div 
        className={`mock-card ${size}`}
        data-testid={`card-${card.rank}-${card.suit}`}
      >
        {card.rank}{card.suit}
      </div>
    );
  };
});

// Mock timers
jest.useFakeTimers();

describe('CardAnimations', () => {
  const mockCard: Card = { suit: Suit.HEARTS, rank: Rank.ACE };
  const mockCards: Card[] = [
    { suit: Suit.HEARTS, rank: Rank.ACE },
    { suit: Suit.SPADES, rank: Rank.KING },
    { suit: Suit.DIAMONDS, rank: Rank.QUEEN }
  ];

  beforeEach(() => {
    jest.clearAllTimers();
    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.runOnlyPendingTimers();
    jest.useRealTimers();
    jest.useFakeTimers();
  });

  describe('DealingAnimation', () => {
    const mockProps = {
      cards: mockCards,
      targetPositions: [Position.NORTH, Position.SOUTH, Position.EAST],
      deckPosition: { x: 100, y: 100 },
      playerPositions: {
        [Position.NORTH]: { x: 200, y: 50 },
        [Position.SOUTH]: { x: 200, y: 350 },
        [Position.EAST]: { x: 350, y: 200 },
        [Position.WEST]: { x: 50, y: 200 }
      },
      onDealComplete: jest.fn(),
      dealingSpeed: 100
    };

    it('renders without crashing', () => {
      render(<DealingAnimation {...mockProps} />);
      expect(screen.getByTestId('dealing-animation')).toBeInTheDocument();
    });

    it('starts dealing cards progressively', () => {
      render(<DealingAnimation {...mockProps} />);
      
      // Initially should show first card
      expect(screen.getByTestId('card-ACE-HEARTS')).toBeInTheDocument();
      expect(screen.queryByTestId('card-KING-SPADES')).not.toBeInTheDocument();
      
      // After dealing speed delay, should show second card
      act(() => {
        jest.advanceTimersByTime(100);
      });
      
      expect(screen.getByTestId('card-KING-SPADES')).toBeInTheDocument();
    });

    it('calls onDealComplete when all cards are dealt', async () => {
      render(<DealingAnimation {...mockProps} />);
      
      // Advance time to complete all dealing and animations
      act(() => {
        jest.advanceTimersByTime(3000); // Enough time for all cards and animations
      });
      
      await waitFor(() => {
        expect(mockProps.onDealComplete).toHaveBeenCalled();
      });
    });

    it('handles empty cards array', () => {
      render(<DealingAnimation {...mockProps} cards={[]} />);
      expect(screen.getByTestId('dealing-animation')).toBeInTheDocument();
    });
  });

  describe('PlayingAnimation', () => {
    const mockProps = {
      card: mockCard,
      fromPosition: { x: 100, y: 100 },
      toPosition: { x: 200, y: 200 },
      onPlayComplete: jest.fn()
    };

    it('renders without crashing', () => {
      render(<PlayingAnimation {...mockProps} />);
      expect(screen.getByTestId('card-animation')).toBeInTheDocument();
    });

    it('displays the correct card', () => {
      render(<PlayingAnimation {...mockProps} />);
      expect(screen.getByTestId('card-ACE-HEARTS')).toBeInTheDocument();
    });

    it('calls onPlayComplete after animation duration', async () => {
      render(<PlayingAnimation {...mockProps} />);
      
      act(() => {
        jest.advanceTimersByTime(1000); // Default duration + buffer
      });
      
      await waitFor(() => {
        expect(mockProps.onPlayComplete).toHaveBeenCalled();
      });
    });
  });

  describe('TrickCollection', () => {
    const mockProps = {
      cards: mockCards,
      cardPositions: [
        { x: 100, y: 100 },
        { x: 200, y: 100 },
        { x: 300, y: 100 }
      ],
      winnerPosition: { x: 250, y: 250 },
      onCollectionComplete: jest.fn()
    };

    it('renders without crashing', () => {
      render(<TrickCollection {...mockProps} />);
      expect(screen.getByTestId('trick-collection')).toBeInTheDocument();
    });

    it('displays all cards', () => {
      render(<TrickCollection {...mockProps} />);
      
      expect(screen.getByTestId('card-ACE-HEARTS')).toBeInTheDocument();
      expect(screen.getByTestId('card-KING-SPADES')).toBeInTheDocument();
      expect(screen.getByTestId('card-QUEEN-DIAMONDS')).toBeInTheDocument();
    });

    it('calls onCollectionComplete after all animations', async () => {
      render(<TrickCollection {...mockProps} />);
      
      act(() => {
        jest.advanceTimersByTime(2000); // Enough time for all animations
      });
      
      await waitFor(() => {
        expect(mockProps.onCollectionComplete).toHaveBeenCalled();
      });
    });
  });

  describe('CardFlip', () => {
    const mockProps = {
      card: mockCard,
      position: { x: 100, y: 100 },
      isRevealed: true,
      onFlipComplete: jest.fn()
    };

    it('renders without crashing', () => {
      render(<CardFlip {...mockProps} />);
      expect(screen.getByTestId('card-flip')).toBeInTheDocument();
    });

    it('shows revealed state correctly', () => {
      render(<CardFlip {...mockProps} />);
      expect(screen.getByTestId('card-flip')).toHaveClass('revealed');
    });

    it('shows hidden state correctly', () => {
      render(<CardFlip {...mockProps} isRevealed={false} />);
      expect(screen.getByTestId('card-flip')).toHaveClass('hidden');
    });

    it('calls onFlipComplete after flip animation', async () => {
      render(<CardFlip {...mockProps} />);
      
      act(() => {
        jest.advanceTimersByTime(700); // Flip duration + buffer
      });
      
      await waitFor(() => {
        expect(mockProps.onFlipComplete).toHaveBeenCalled();
      });
    });
  });

  describe('CardHover', () => {
    it('renders children without crashing', () => {
      render(
        <CardHover>
          <div data-testid="child-content">Test Content</div>
        </CardHover>
      );
      
      expect(screen.getByTestId('card-hover')).toBeInTheDocument();
      expect(screen.getByTestId('child-content')).toBeInTheDocument();
    });

    it('applies hover class on mouse enter', () => {
      render(
        <CardHover>
          <div>Test Content</div>
        </CardHover>
      );
      
      const hoverElement = screen.getByTestId('card-hover');
      
      act(() => {
        hoverElement.dispatchEvent(new MouseEvent('mouseenter', { bubbles: true }));
      });
      
      expect(hoverElement).toHaveClass('hovered');
    });

    it('removes hover class on mouse leave', () => {
      render(
        <CardHover>
          <div>Test Content</div>
        </CardHover>
      );
      
      const hoverElement = screen.getByTestId('card-hover');
      
      act(() => {
        hoverElement.dispatchEvent(new MouseEvent('mouseenter', { bubbles: true }));
      });
      
      expect(hoverElement).toHaveClass('hovered');
      
      act(() => {
        hoverElement.dispatchEvent(new MouseEvent('mouseleave', { bubbles: true }));
      });
      
      expect(hoverElement).not.toHaveClass('hovered');
    });

    it('does not apply hover effects when disabled', () => {
      render(
        <CardHover isEnabled={false}>
          <div data-testid="child-content">Test Content</div>
        </CardHover>
      );
      
      // Should render children directly without hover wrapper
      expect(screen.getByTestId('child-content')).toBeInTheDocument();
      expect(screen.queryByTestId('card-hover')).not.toBeInTheDocument();
    });

    it('applies custom hover scale and rotation', () => {
      render(
        <CardHover hoverScale={1.2} hoverRotation={5}>
          <div>Test Content</div>
        </CardHover>
      );
      
      const hoverElement = screen.getByTestId('card-hover');
      expect(hoverElement).toHaveStyle({
        '--hover-scale': 1.2,
        '--hover-rotation': '5deg'
      });
    });
  });
});

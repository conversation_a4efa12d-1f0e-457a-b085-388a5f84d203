/**
 * MultiplayerGameManager component for handling real-time multiplayer Bridge games
 * Integrates WebSocket communication with game state management
 */

import React, { useState, useEffect, useCallback } from 'react';
import { useGameSocket } from '../../hooks/useGameSocket';
import { useGameState } from '../../hooks/useGameState';
import { GameState, Position, Card, Bid, Player, PlayerType } from '../../types/bridge';
import GameTable from '../GameTable/GameTable';
import { DealingAnimation } from '../CardAnimations/CardAnimations';
import './MultiplayerGameManager.css';

interface MultiplayerGameManagerProps {
  gameId: string;
  playerId: string;
  playerName: string;
  playerPosition?: Position;
  onGameEnd?: (result: any) => void;
  onError?: (error: Error) => void;
}

interface ConnectionStatus {
  isConnected: boolean;
  isConnecting: boolean;
  error: string | null;
  playersConnected: number;
  lastActivity: Date | null;
}

const MultiplayerGameManager: React.FC<MultiplayerGameManagerProps> = ({
  gameId,
  playerId,
  playerName,
  playerPosition,
  onGameEnd,
  onError
}) => {
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>({
    isConnected: false,
    isConnecting: false,
    error: null,
    playersConnected: 0,
    lastActivity: null
  });

  const [isDealing, setIsDealing] = useState(false);
  const [connectedPlayers, setConnectedPlayers] = useState<Map<Position, Player>>(new Map());

  // Initialize game state management
  const {
    gameState,
    updateGameState,
    makeBid,
    playCard,
    canMakeBid,
    canPlayCard,
    getValidCards
  } = useGameState(gameId, playerId);

  // Initialize WebSocket connection
  const {
    state: socketState,
    connect,
    disconnect,
    sendBid,
    sendCardPlay,
    requestGameState
  } = useGameSocket({
    gameId,
    playerId,
    playerName,
    autoConnect: true,
    onGameStateUpdate: handleGameStateUpdate,
    onPlayerJoined: handlePlayerJoined,
    onPlayerLeft: handlePlayerLeft,
    onBidMade: handleBidMade,
    onCardPlayed: handleCardPlayed,
    onTrickCompleted: handleTrickCompleted,
    onGameStarted: handleGameStarted,
    onGameEnded: handleGameEnded,
    onError: handleSocketError
  });

  // Update connection status when socket state changes
  useEffect(() => {
    setConnectionStatus(prev => ({
      ...prev,
      isConnected: socketState.isConnected,
      isConnecting: socketState.isConnecting,
      error: socketState.error?.message || null,
      lastActivity: socketState.lastMessage ? new Date() : prev.lastActivity
    }));
  }, [socketState]);

  // Handle game state updates from server
  function handleGameStateUpdate(newGameState: GameState): void {
    updateGameState(newGameState);
    
    // Count connected players
    const connectedCount = Object.values(newGameState.players)
      .filter(player => player.isConnected).length;
    
    setConnectionStatus(prev => ({
      ...prev,
      playersConnected: connectedCount
    }));
  }

  // Handle player joining
  function handlePlayerJoined(joinedPlayerId: string, joinedPlayerName: string, position: Position): void {
    const player: Player = {
      id: joinedPlayerId,
      name: joinedPlayerName,
      position,
      type: PlayerType.HUMAN,
      hand: [],
      isConnected: true
    };

    setConnectedPlayers(prev => new Map(prev.set(position, player)));
    
    setConnectionStatus(prev => ({
      ...prev,
      playersConnected: prev.playersConnected + 1,
      lastActivity: new Date()
    }));
  }

  // Handle player leaving
  function handlePlayerLeft(leftPlayerId: string, position: Position): void {
    setConnectedPlayers(prev => {
      const newMap = new Map(prev);
      newMap.delete(position);
      return newMap;
    });

    setConnectionStatus(prev => ({
      ...prev,
      playersConnected: Math.max(0, prev.playersConnected - 1),
      lastActivity: new Date()
    }));
  }

  // Handle bid made by another player
  function handleBidMade(position: Position, bid: Bid): void {
    if (position !== playerPosition) {
      makeBid(bid, position);
    }
    
    setConnectionStatus(prev => ({
      ...prev,
      lastActivity: new Date()
    }));
  }

  // Handle card played by another player
  function handleCardPlayed(position: Position, card: Card): void {
    if (position !== playerPosition) {
      playCard(card, position);
    }
    
    setConnectionStatus(prev => ({
      ...prev,
      lastActivity: new Date()
    }));
  }

  // Handle trick completion
  function handleTrickCompleted(winner: Position, trick: Card[]): void {
    // Animation and state updates handled by game state manager
    setConnectionStatus(prev => ({
      ...prev,
      lastActivity: new Date()
    }));
  }

  // Handle game start
  function handleGameStarted(): void {
    setIsDealing(true);
    
    // Request current game state to sync
    setTimeout(() => {
      requestGameState();
      setIsDealing(false);
    }, 3000); // Allow time for dealing animation
  }

  // Handle game end
  function handleGameEnded(result: any): void {
    onGameEnd?.(result);
  }

  // Handle socket errors
  function handleSocketError(error: Error): void {
    console.error('WebSocket error:', error);
    onError?.(error);
  }

  // Handle local bid action
  const handleLocalBid = useCallback((bid: Bid) => {
    if (!canMakeBid(bid)) {
      console.warn('Invalid bid attempted:', bid);
      return;
    }

    // Update local state immediately for responsiveness
    makeBid(bid, playerPosition);
    
    // Send to server
    sendBid(bid);
  }, [canMakeBid, makeBid, playerPosition, sendBid]);

  // Handle local card play action
  const handleLocalCardPlay = useCallback((card: Card) => {
    if (!canPlayCard(card)) {
      console.warn('Invalid card play attempted:', card);
      return;
    }

    // Update local state immediately for responsiveness
    playCard(card, playerPosition);
    
    // Send to server
    sendCardPlay(card);
  }, [canPlayCard, playCard, playerPosition, sendCardPlay]);

  // Retry connection
  const retryConnection = useCallback(async () => {
    try {
      await connect();
    } catch (error) {
      console.error('Failed to reconnect:', error);
    }
  }, [connect]);

  // Render connection status
  const renderConnectionStatus = () => (
    <div className={`connection-status ${connectionStatus.isConnected ? 'connected' : 'disconnected'}`}>
      <div className="status-indicator">
        <div className={`status-dot ${connectionStatus.isConnected ? 'green' : 'red'}`} />
        <span className="status-text">
          {connectionStatus.isConnecting ? 'Connecting...' : 
           connectionStatus.isConnected ? 'Connected' : 'Disconnected'}
        </span>
      </div>
      
      <div className="players-count">
        {connectionStatus.playersConnected}/4 players
      </div>
      
      {connectionStatus.error && (
        <div className="error-message">
          {connectionStatus.error}
          <button onClick={retryConnection} className="retry-button">
            Retry
          </button>
        </div>
      )}
      
      {connectionStatus.lastActivity && (
        <div className="last-activity">
          Last activity: {connectionStatus.lastActivity.toLocaleTimeString()}
        </div>
      )}
    </div>
  );

  // Render waiting for players
  const renderWaitingForPlayers = () => (
    <div className="waiting-for-players">
      <h2>Waiting for Players</h2>
      <p>Game ID: {gameId}</p>
      <div className="player-slots">
        {[Position.NORTH, Position.EAST, Position.SOUTH, Position.WEST].map(position => {
          const player = connectedPlayers.get(position);
          return (
            <div key={position} className={`player-slot ${player ? 'filled' : 'empty'}`}>
              <div className="position-label">{position}</div>
              <div className="player-info">
                {player ? (
                  <>
                    <span className="player-name">{player.name}</span>
                    <span className="connection-indicator">●</span>
                  </>
                ) : (
                  <span className="waiting-text">Waiting...</span>
                )}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );

  return (
    <div className="multiplayer-game-manager" data-testid="multiplayer-game-manager">
      {renderConnectionStatus()}
      
      {connectionStatus.playersConnected < 4 ? (
        renderWaitingForPlayers()
      ) : (
        <>
          {isDealing && (
            <DealingAnimation
              cards={gameState.players[playerPosition || Position.SOUTH].hand}
              targetPositions={[Position.NORTH, Position.EAST, Position.SOUTH, Position.WEST]}
              deckPosition={{ x: 400, y: 300 }}
              playerPositions={{
                [Position.NORTH]: { x: 400, y: 100 },
                [Position.SOUTH]: { x: 400, y: 500 },
                [Position.EAST]: { x: 700, y: 300 },
                [Position.WEST]: { x: 100, y: 300 }
              }}
              onDealComplete={() => setIsDealing(false)}
            />
          )}
          
          <GameTable
            initialGameState={gameState}
          />
        </>
      )}
    </div>
  );
};

export default MultiplayerGameManager;

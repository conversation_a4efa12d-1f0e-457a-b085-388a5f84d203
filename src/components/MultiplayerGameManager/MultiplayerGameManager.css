/**
 * MultiplayerGameManager component styles
 */

.multiplayer-game-manager {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #0f4c3a 0%, #1a5c4a 100%);
  position: relative;
}

/* Connection Status */
.connection-status {
  position: fixed;
  top: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 12px 16px;
  border-radius: 8px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  z-index: 1000;
  min-width: 200px;
  font-size: 14px;
}

.connection-status.connected {
  border-color: rgba(40, 167, 69, 0.5);
  background: rgba(0, 50, 0, 0.8);
}

.connection-status.disconnected {
  border-color: rgba(220, 53, 69, 0.5);
  background: rgba(50, 0, 0, 0.8);
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.status-dot.green {
  background: #28a745;
  box-shadow: 0 0 8px rgba(40, 167, 69, 0.6);
}

.status-dot.red {
  background: #dc3545;
  box-shadow: 0 0 8px rgba(220, 53, 69, 0.6);
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.2);
  }
}

.status-text {
  font-weight: 500;
}

.players-count {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 4px;
}

.error-message {
  color: #ff6b6b;
  font-size: 12px;
  margin-top: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.retry-button {
  background: #dc3545;
  color: white;
  border: none;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 11px;
  cursor: pointer;
  transition: background 0.2s;
}

.retry-button:hover {
  background: #c82333;
}

.last-activity {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.6);
  margin-top: 4px;
}

/* Waiting for Players */
.waiting-for-players {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: white;
  padding: 40px;
}

.waiting-for-players h2 {
  font-size: 32px;
  margin-bottom: 16px;
  color: #4a9eff;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.waiting-for-players p {
  font-size: 18px;
  margin-bottom: 40px;
  opacity: 0.8;
}

.player-slots {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
  gap: 20px;
  max-width: 400px;
  width: 100%;
}

.player-slot {
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 20px;
  transition: all 0.3s ease;
  min-height: 100px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.player-slot.filled {
  background: rgba(40, 167, 69, 0.2);
  border-color: rgba(40, 167, 69, 0.5);
  transform: scale(1.02);
}

.player-slot.empty {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.1);
  animation: breathe 3s ease-in-out infinite;
}

@keyframes breathe {
  0%, 100% {
    opacity: 0.7;
  }
  50% {
    opacity: 1;
  }
}

.position-label {
  font-size: 14px;
  font-weight: bold;
  color: #4a9eff;
  margin-bottom: 8px;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.player-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.player-name {
  font-size: 16px;
  font-weight: 500;
  color: white;
}

.connection-indicator {
  color: #28a745;
  font-size: 12px;
  animation: pulse 2s infinite;
}

.waiting-text {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.6);
  font-style: italic;
}

/* Game Area */
.game-area {
  flex: 1;
  position: relative;
  overflow: hidden;
}

/* Responsive Design */
@media (max-width: 768px) {
  .connection-status {
    top: 10px;
    right: 10px;
    left: 10px;
    min-width: auto;
    font-size: 12px;
    padding: 8px 12px;
  }

  .waiting-for-players {
    padding: 20px;
  }

  .waiting-for-players h2 {
    font-size: 24px;
  }

  .waiting-for-players p {
    font-size: 16px;
    margin-bottom: 30px;
  }

  .player-slots {
    grid-template-columns: 1fr;
    grid-template-rows: repeat(4, 1fr);
    gap: 15px;
    max-width: 300px;
  }

  .player-slot {
    padding: 15px;
    min-height: 80px;
  }

  .position-label {
    font-size: 12px;
  }

  .player-name {
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .connection-status {
    position: relative;
    top: 0;
    right: 0;
    left: 0;
    margin: 10px;
    border-radius: 6px;
  }

  .waiting-for-players {
    padding: 15px;
  }

  .waiting-for-players h2 {
    font-size: 20px;
  }

  .waiting-for-players p {
    font-size: 14px;
    margin-bottom: 20px;
  }

  .player-slots {
    gap: 10px;
    max-width: 250px;
  }

  .player-slot {
    padding: 12px;
    min-height: 70px;
  }
}

/* Loading Animation */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid #4a9eff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .connection-status {
    background: Canvas;
    color: CanvasText;
    border: 2px solid currentColor;
  }

  .player-slot {
    background: Canvas;
    border: 2px solid currentColor;
  }

  .status-dot {
    border: 2px solid currentColor;
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .status-dot,
  .connection-indicator,
  .player-slot.empty,
  .loading-spinner {
    animation: none !important;
  }

  .player-slot {
    transition: none !important;
  }
}

/* Print styles */
@media print {
  .connection-status,
  .waiting-for-players {
    display: none !important;
  }
}

/**
 * GameStatusIndicator component for displaying current game state information
 * Shows phase, current player, contract status, and game progress
 */

import React from 'react';
import { GameState, Position, PlayerType, GamePhase } from '../../types/bridge';
import { formatBid } from '../../lib/bridge/biddingUtils';
import './GameStatusIndicator.css';

interface GameStatusIndicatorProps {
  gameState: GameState;
  humanPosition: Position;
  compact?: boolean;
}

const GameStatusIndicator: React.FC<GameStatusIndicatorProps> = ({
  gameState,
  humanPosition,
  compact = false
}) => {
  const currentPlayer = gameState.players[gameState.currentPlayer];
  const isHumanTurn = currentPlayer.type === PlayerType.HUMAN;
  
  // Get phase display information
  const getPhaseInfo = () => {
    switch (gameState.phase) {
      case GamePhase.BIDDING:
        return {
          name: 'Bidding',
          icon: '🎯',
          color: '#4a9eff',
          description: 'Players are making bids'
        };
      case GamePhase.PLAYING:
        return {
          name: 'Playing',
          icon: '🃏',
          color: '#28a745',
          description: 'Cards are being played'
        };
      case GamePhase.FINISHED:
        return {
          name: 'Complete',
          icon: '🏆',
          color: '#ffd700',
          description: 'Hand is finished'
        };
      default:
        return {
          name: 'Unknown',
          icon: '❓',
          color: '#6c757d',
          description: 'Game state unknown'
        };
    }
  };

  // Get current player status
  const getPlayerStatus = () => {
    if (isHumanTurn) {
      return {
        message: "Your turn",
        color: '#28a745',
        icon: '👤',
        urgent: true
      };
    } else {
      return {
        message: `${currentPlayer.name}'s turn`,
        color: '#6c757d',
        icon: '🤖',
        urgent: false
      };
    }
  };

  // Get contract information
  const getContractInfo = () => {
    if (!gameState.contract) {
      return null;
    }

    const declarer = gameState.players[gameState.contract.declarer];
    const isHumanDeclarer = declarer.type === PlayerType.HUMAN;
    
    return {
      level: gameState.contract.level,
      suit: gameState.contract.suit,
      declarer: declarer.name,
      isHumanDeclarer,
      doubled: gameState.contract.doubled,
      target: 6 + gameState.contract.level
    };
  };

  // Get auction progress
  const getAuctionProgress = () => {
    const totalBids = gameState.auction.length;
    const passes = gameState.auction.filter(bid => bid.value === 'PASS').length;
    const contractBids = gameState.auction.filter(bid => 
      typeof bid.value === 'object' && 'level' in bid.value
    ).length;
    
    return {
      totalBids,
      passes,
      contractBids,
      lastBid: gameState.auction.length > 0 ? gameState.auction[gameState.auction.length - 1] : null
    };
  };

  // Get trick progress
  const getTrickProgress = () => {
    const tricksPlayed = gameState.tricks.length;
    const currentTrickCards = gameState.currentTrick.length;
    const totalTricks = 13;
    
    return {
      tricksPlayed,
      currentTrickCards,
      totalTricks,
      tricksRemaining: totalTricks - tricksPlayed
    };
  };

  const phaseInfo = getPhaseInfo();
  const playerStatus = getPlayerStatus();
  const contractInfo = getContractInfo();
  const auctionProgress = getAuctionProgress();
  const trickProgress = getTrickProgress();

  if (compact) {
    return (
      <div className="game-status-indicator compact" data-testid="game-status-indicator">
        <div className="status-row">
          <div className="phase-indicator" style={{ color: phaseInfo.color }}>
            <span className="phase-icon">{phaseInfo.icon}</span>
            <span className="phase-name">{phaseInfo.name}</span>
          </div>
          
          <div className={`player-indicator ${isHumanTurn ? 'human-turn' : 'ai-turn'}`}>
            <span className="player-icon">{playerStatus.icon}</span>
            <span className="player-message">{playerStatus.message}</span>
          </div>
        </div>
        
        {contractInfo && (
          <div className="contract-compact">
            <span className="contract-label">Contract:</span>
            <span className="contract-value">
              {contractInfo.level}{contractInfo.suit}
              {contractInfo.doubled !== 'none' && (
                <span className="doubled-indicator">
                  {contractInfo.doubled === 'doubled' ? ' X' : ' XX'}
                </span>
              )}
            </span>
            <span className="declarer">by {contractInfo.declarer}</span>
          </div>
        )}
      </div>
    );
  }

  return (
    <div className="game-status-indicator" data-testid="game-status-indicator">
      {/* Phase Header */}
      <div className="status-header">
        <div className="phase-section" style={{ borderColor: phaseInfo.color }}>
          <div className="phase-icon" style={{ color: phaseInfo.color }}>
            {phaseInfo.icon}
          </div>
          <div className="phase-info">
            <div className="phase-name" style={{ color: phaseInfo.color }}>
              {phaseInfo.name}
            </div>
            <div className="phase-description">{phaseInfo.description}</div>
          </div>
        </div>
      </div>

      {/* Current Player Status */}
      <div className={`player-status ${isHumanTurn ? 'human-turn' : 'ai-turn'}`}>
        <div className="player-icon" style={{ color: playerStatus.color }}>
          {playerStatus.icon}
        </div>
        <div className="player-info">
          <div className="player-message" style={{ color: playerStatus.color }}>
            {playerStatus.message}
          </div>
          {!isHumanTurn && (
            <div className="ai-thinking">
              <div className="thinking-dots">
                <span></span>
                <span></span>
                <span></span>
              </div>
              <span className="thinking-text">Thinking...</span>
            </div>
          )}
        </div>
      </div>

      {/* Contract Information */}
      {contractInfo && (
        <div className="contract-status">
          <div className="contract-header">Contract</div>
          <div className="contract-details">
            <div className="contract-bid">
              <span className="contract-level">{contractInfo.level}</span>
              <span className="contract-suit">{contractInfo.suit}</span>
              {contractInfo.doubled !== 'none' && (
                <span className="doubled-indicator">
                  {contractInfo.doubled === 'doubled' ? ' X' : ' XX'}
                </span>
              )}
            </div>
            <div className="contract-declarer">
              <span className="declarer-label">Declarer:</span>
              <span className={`declarer-name ${contractInfo.isHumanDeclarer ? 'human' : 'ai'}`}>
                {contractInfo.declarer}
              </span>
            </div>
            <div className="contract-target">
              <span className="target-label">Target:</span>
              <span className="target-value">{contractInfo.target} tricks</span>
            </div>
          </div>
        </div>
      )}

      {/* Phase-specific Progress */}
      {gameState.phase === GamePhase.BIDDING && (
        <div className="auction-progress">
          <div className="progress-header">Auction Progress</div>
          <div className="auction-stats">
            <div className="stat-item">
              <span className="stat-label">Total Bids:</span>
              <span className="stat-value">{auctionProgress.totalBids}</span>
            </div>
            <div className="stat-item">
              <span className="stat-label">Contract Bids:</span>
              <span className="stat-value">{auctionProgress.contractBids}</span>
            </div>
            <div className="stat-item">
              <span className="stat-label">Passes:</span>
              <span className="stat-value">{auctionProgress.passes}</span>
            </div>
          </div>
          {auctionProgress.lastBid && (
            <div className="last-bid">
              <span className="last-bid-label">Last Bid:</span>
              <span className="last-bid-value">
                {formatBid(auctionProgress.lastBid)} by {gameState.players[auctionProgress.lastBid.player].name}
              </span>
            </div>
          )}
        </div>
      )}

      {gameState.phase === GamePhase.PLAYING && (
        <div className="trick-progress">
          <div className="progress-header">Trick Progress</div>
          <div className="trick-stats">
            <div className="stat-item">
              <span className="stat-label">Tricks Played:</span>
              <span className="stat-value">{trickProgress.tricksPlayed}</span>
            </div>
            <div className="stat-item">
              <span className="stat-label">Current Trick:</span>
              <span className="stat-value">{trickProgress.currentTrickCards}/4 cards</span>
            </div>
            <div className="stat-item">
              <span className="stat-label">Remaining:</span>
              <span className="stat-value">{trickProgress.tricksRemaining} tricks</span>
            </div>
          </div>
          
          {/* Progress Bar */}
          <div className="progress-bar">
            <div className="progress-track">
              <div 
                className="progress-fill"
                style={{ 
                  width: `${(trickProgress.tricksPlayed / trickProgress.totalTricks) * 100}%` 
                }}
              ></div>
            </div>
            <div className="progress-label">
              {trickProgress.tricksPlayed} / {trickProgress.totalTricks}
            </div>
          </div>
        </div>
      )}

      {/* Game Number */}
      <div className="game-info">
        <div className="game-number">Game #{gameState.gameNumber}</div>
        <div className="dealer-info">
          Dealer: {gameState.players[gameState.dealer].name}
        </div>
      </div>
    </div>
  );
};

export default GameStatusIndicator;

/**
 * Unit tests for GameStatusIndicator component
 */

import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import GameStatusIndicator from './GameStatusIndicator';
import { 
  Position, 
  PlayerType, 
  GamePhase, 
  GameState,
  BidSuit,
  SpecialBid
} from '../../types/bridge';

// Mock the bidding utils
jest.mock('../../lib/bridge/biddingUtils', () => ({
  formatBid: jest.fn((bid) => {
    if (typeof bid.value === 'string') {
      return bid.value === 'PASS' ? 'Pass' : bid.value;
    }
    return `${bid.value.level}${bid.value.suit}`;
  }),
}));

describe('GameStatusIndicator', () => {
  const mockGameState: GameState = {
    id: 'test-game',
    phase: GamePhase.BIDDING,
    dealer: Position.NORTH,
    currentPlayer: Position.SOUTH,
    players: {
      [Position.NORTH]: {
        id: 'north',
        name: 'North Player',
        position: Position.NORTH,
        type: PlayerType.AI,
        hand: []
      },
      [Position.SOUTH]: {
        id: 'south',
        name: 'You',
        position: Position.SOUTH,
        type: PlayerType.HUMAN,
        hand: []
      },
      [Position.EAST]: {
        id: 'east',
        name: 'East Player',
        position: Position.EAST,
        type: PlayerType.AI,
        hand: []
      },
      [Position.WEST]: {
        id: 'west',
        name: 'West Player',
        position: Position.WEST,
        type: PlayerType.AI,
        hand: []
      }
    },
    auction: [
      {
        player: Position.NORTH,
        value: { level: 1, suit: BidSuit.CLUBS },
        timestamp: new Date()
      },
      {
        player: Position.EAST,
        value: SpecialBid.PASS,
        timestamp: new Date()
      }
    ],
    contract: null,
    tricks: [],
    currentTrick: [],
    dummy: null,
    vulnerabilities: { northSouth: false, eastWest: false },
    score: { northSouth: 0, eastWest: 0 },
    gameNumber: 1,
    rubberScore: { northSouth: 0, eastWest: 0 }
  };

  it('renders without crashing', () => {
    render(
      <GameStatusIndicator
        gameState={mockGameState}
        humanPosition={Position.SOUTH}
      />
    );
    
    expect(screen.getByTestId('game-status-indicator')).toBeInTheDocument();
  });

  it('displays bidding phase information', () => {
    render(
      <GameStatusIndicator
        gameState={mockGameState}
        humanPosition={Position.SOUTH}
      />
    );
    
    expect(screen.getByText('Bidding')).toBeInTheDocument();
    expect(screen.getByText('Players are making bids')).toBeInTheDocument();
  });

  it('shows human player turn correctly', () => {
    render(
      <GameStatusIndicator
        gameState={mockGameState}
        humanPosition={Position.SOUTH}
      />
    );
    
    expect(screen.getByText('Your turn')).toBeInTheDocument();
  });

  it('shows AI player turn correctly', () => {
    const aiTurnState = {
      ...mockGameState,
      currentPlayer: Position.NORTH
    };

    render(
      <GameStatusIndicator
        gameState={aiTurnState}
        humanPosition={Position.SOUTH}
      />
    );
    
    expect(screen.getByText("North Player's turn")).toBeInTheDocument();
    expect(screen.getByText('Thinking...')).toBeInTheDocument();
  });

  it('displays auction progress', () => {
    render(
      <GameStatusIndicator
        gameState={mockGameState}
        humanPosition={Position.SOUTH}
      />
    );
    
    expect(screen.getByText('Auction Progress')).toBeInTheDocument();
    expect(screen.getByText('Total Bids:')).toBeInTheDocument();
    expect(screen.getByText('Contract Bids:')).toBeInTheDocument();
    expect(screen.getByText('Passes:')).toBeInTheDocument();
  });

  it('shows last bid information', () => {
    render(
      <GameStatusIndicator
        gameState={mockGameState}
        humanPosition={Position.SOUTH}
      />
    );
    
    expect(screen.getByText('Last Bid:')).toBeInTheDocument();
  });

  it('displays contract when available', () => {
    const contractState = {
      ...mockGameState,
      phase: GamePhase.PLAYING,
      contract: {
        level: 3,
        suit: BidSuit.NO_TRUMP,
        declarer: Position.SOUTH,
        doubled: 'none' as const
      }
    };

    render(
      <GameStatusIndicator
        gameState={contractState}
        humanPosition={Position.SOUTH}
      />
    );
    
    expect(screen.getByText('Contract')).toBeInTheDocument();
    expect(screen.getByText('3')).toBeInTheDocument();
    expect(screen.getByText('NO_TRUMP')).toBeInTheDocument();
    expect(screen.getByText('You')).toBeInTheDocument();
    expect(screen.getByText('9 tricks')).toBeInTheDocument();
  });

  it('shows doubled indicator', () => {
    const doubledState = {
      ...mockGameState,
      contract: {
        level: 2,
        suit: BidSuit.HEARTS,
        declarer: Position.NORTH,
        doubled: 'doubled' as const
      }
    };

    render(
      <GameStatusIndicator
        gameState={doubledState}
        humanPosition={Position.SOUTH}
      />
    );
    
    expect(screen.getByText('X')).toBeInTheDocument();
  });

  it('displays playing phase information', () => {
    const playingState = {
      ...mockGameState,
      phase: GamePhase.PLAYING,
      tricks: [
        {
          id: 1,
          cards: [],
          winner: Position.SOUTH,
          leadPlayer: Position.NORTH
        }
      ],
      currentTrick: [
        { card: { suit: 'HEARTS', rank: 'ACE' }, player: Position.NORTH }
      ]
    };

    render(
      <GameStatusIndicator
        gameState={playingState}
        humanPosition={Position.SOUTH}
      />
    );
    
    expect(screen.getByText('Playing')).toBeInTheDocument();
    expect(screen.getByText('Cards are being played')).toBeInTheDocument();
    expect(screen.getByText('Trick Progress')).toBeInTheDocument();
  });

  it('shows game number and dealer', () => {
    render(
      <GameStatusIndicator
        gameState={mockGameState}
        humanPosition={Position.SOUTH}
      />
    );
    
    expect(screen.getByText('Game #1')).toBeInTheDocument();
    expect(screen.getByText('Dealer: North Player')).toBeInTheDocument();
  });

  it('renders in compact mode', () => {
    render(
      <GameStatusIndicator
        gameState={mockGameState}
        humanPosition={Position.SOUTH}
        compact={true}
      />
    );
    
    expect(screen.getByTestId('game-status-indicator')).toHaveClass('compact');
    expect(screen.getByText('Bidding')).toBeInTheDocument();
    expect(screen.getByText('Your turn')).toBeInTheDocument();
  });

  it('handles complete phase', () => {
    const completeState = {
      ...mockGameState,
      phase: GamePhase.FINISHED
    };

    render(
      <GameStatusIndicator
        gameState={completeState}
        humanPosition={Position.SOUTH}
      />
    );
    
    expect(screen.getByText('Complete')).toBeInTheDocument();
    expect(screen.getByText('Hand is finished')).toBeInTheDocument();
  });

  it('handles empty auction', () => {
    const emptyAuctionState = {
      ...mockGameState,
      auction: []
    };

    render(
      <GameStatusIndicator
        gameState={emptyAuctionState}
        humanPosition={Position.SOUTH}
      />
    );
    
    expect(screen.queryByText('Last Bid:')).not.toBeInTheDocument();
  });
});

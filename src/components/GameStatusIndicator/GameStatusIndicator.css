/**
 * GameStatusIndicator component styles
 */

.game-status-indicator {
  background: rgba(0, 0, 0, 0.9);
  border-radius: 12px;
  padding: 16px;
  color: white;
  min-width: 280px;
  max-width: 320px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.game-status-indicator.compact {
  min-width: 200px;
  max-width: 250px;
  padding: 12px;
  gap: 8px;
}

/* Status Header */
.status-header {
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding-bottom: 12px;
}

.phase-section {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px;
  border-radius: 8px;
  border-left: 4px solid;
  background: rgba(255, 255, 255, 0.05);
}

.phase-icon {
  font-size: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
}

.phase-info {
  flex: 1;
}

.phase-name {
  font-size: 18px;
  font-weight: 700;
  margin-bottom: 2px;
}

.phase-description {
  font-size: 12px;
  color: #cccccc;
  font-style: italic;
}

/* Compact Status Row */
.status-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 12px;
}

.phase-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  font-weight: 600;
}

.phase-indicator .phase-icon {
  font-size: 16px;
  width: auto;
  height: auto;
  background: none;
}

/* Player Status */
.player-status {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.05);
  border: 2px solid transparent;
  transition: all 0.3s ease;
}

.player-status.human-turn {
  border-color: #28a745;
  background: rgba(40, 167, 69, 0.1);
  animation: pulse-human 2s infinite;
}

.player-status.ai-turn {
  border-color: #6c757d;
  background: rgba(108, 117, 125, 0.1);
}

@keyframes pulse-human {
  0%, 100% { 
    border-color: #28a745;
    box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.4);
  }
  50% { 
    border-color: #20c997;
    box-shadow: 0 0 0 8px rgba(40, 167, 69, 0);
  }
}

.player-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
}

.player-icon {
  font-size: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
}

.player-info {
  flex: 1;
}

.player-message {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 4px;
}

.ai-thinking {
  display: flex;
  align-items: center;
  gap: 8px;
}

.thinking-dots {
  display: flex;
  gap: 2px;
}

.thinking-dots span {
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background: #6c757d;
  animation: thinking 1.4s infinite ease-in-out;
}

.thinking-dots span:nth-child(1) { animation-delay: -0.32s; }
.thinking-dots span:nth-child(2) { animation-delay: -0.16s; }

@keyframes thinking {
  0%, 80%, 100% { 
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% { 
    transform: scale(1);
    opacity: 1;
  }
}

.thinking-text {
  font-size: 12px;
  color: #6c757d;
  font-style: italic;
}

/* Contract Status */
.contract-status {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  padding: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.contract-header {
  font-size: 14px;
  font-weight: 600;
  color: #ffd700;
  margin-bottom: 8px;
  text-align: center;
}

.contract-details {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.contract-bid {
  text-align: center;
  font-size: 20px;
  font-weight: 700;
  color: white;
}

.contract-level {
  color: #4a9eff;
}

.contract-suit {
  margin-left: 2px;
}

.doubled-indicator {
  color: #ff6b6b;
  font-weight: 700;
  margin-left: 4px;
}

.contract-declarer,
.contract-target {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
}

.declarer-label,
.target-label {
  color: #cccccc;
}

.declarer-name.human {
  color: #28a745;
  font-weight: 600;
}

.declarer-name.ai {
  color: #6c757d;
}

.target-value {
  color: #4a9eff;
  font-weight: 600;
}

/* Compact Contract */
.contract-compact {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  padding: 4px 8px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 4px;
}

.contract-label {
  color: #ffd700;
  font-weight: 600;
}

.contract-value {
  color: white;
  font-weight: 700;
}

.declarer {
  color: #cccccc;
  font-size: 11px;
}

/* Progress Sections */
.auction-progress,
.trick-progress {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  padding: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.progress-header {
  font-size: 14px;
  font-weight: 600;
  color: #4a9eff;
  margin-bottom: 8px;
  text-align: center;
}

.auction-stats,
.trick-stats {
  display: flex;
  flex-direction: column;
  gap: 4px;
  margin-bottom: 8px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
}

.stat-label {
  color: #cccccc;
}

.stat-value {
  color: white;
  font-weight: 600;
}

.last-bid {
  padding: 6px 8px;
  background: rgba(74, 158, 255, 0.1);
  border-radius: 4px;
  border: 1px solid rgba(74, 158, 255, 0.3);
  font-size: 12px;
}

.last-bid-label {
  color: #4a9eff;
  font-weight: 500;
}

.last-bid-value {
  color: white;
  font-weight: 600;
  margin-left: 6px;
}

/* Progress Bar */
.progress-bar {
  margin-top: 8px;
}

.progress-track {
  width: 100%;
  height: 8px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 4px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4a9eff, #28a745);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.progress-label {
  text-align: center;
  font-size: 11px;
  color: #cccccc;
}

/* Game Info */
.game-info {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 6px;
  padding: 8px 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  text-align: center;
}

.game-number {
  font-size: 14px;
  font-weight: 600;
  color: #ffd700;
  margin-bottom: 2px;
}

.dealer-info {
  font-size: 12px;
  color: #cccccc;
}

/* Responsive Design */
@media (max-width: 768px) {
  .game-status-indicator {
    min-width: 240px;
    max-width: 280px;
    padding: 12px;
  }
  
  .game-status-indicator.compact {
    min-width: 180px;
    max-width: 220px;
    padding: 8px;
  }
  
  .status-row {
    flex-direction: column;
    gap: 8px;
  }
  
  .phase-section {
    gap: 8px;
  }
  
  .phase-icon {
    width: 32px;
    height: 32px;
    font-size: 20px;
  }
  
  .phase-name {
    font-size: 16px;
  }
}

/* Accessibility */
.game-status-indicator button:focus,
.game-status-indicator [tabindex]:focus {
  outline: 2px solid #4a9eff;
  outline-offset: 2px;
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .player-status.human-turn,
  .thinking-dots span {
    animation: none;
  }
  
  .progress-fill {
    transition: none;
  }
}


/* Enhanced Responsive Design */
@media (max-width: 1200px) {
  .game-status-indicator {
    min-width: 240px;
    max-width: 280px;
    padding: 14px;
  }
}

@media (max-width: 1024px) {
  .game-status-indicator {
    min-width: 220px;
    max-width: 260px;
    padding: 12px;
  }
  
  .phase-name {
    font-size: 16px;
  }
  
  .player-message {
    font-size: 14px;
  }
}

@media (max-width: 768px) {
  .game-status-indicator {
    min-width: 200px;
    max-width: 240px;
    padding: 10px;
    gap: 12px;
  }
  
  .game-status-indicator.compact {
    min-width: 160px;
    max-width: 200px;
    padding: 8px;
    gap: 6px;
  }
  
  .phase-name {
    font-size: 14px;
  }
  
  .player-message {
    font-size: 12px;
  }
  
  .contract-details {
    flex-direction: column;
    gap: 6px;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .game-status-indicator {
    min-width: 160px;
    max-width: 200px;
    padding: 8px;
    gap: 8px;
  }
  
  .game-status-indicator.compact {
    min-width: 140px;
    max-width: 180px;
    padding: 6px;
    gap: 4px;
  }
  
  .phase-icon {
    width: 32px;
    height: 32px;
    font-size: 20px;
  }
  
  .phase-name {
    font-size: 12px;
  }
  
  .player-message {
    font-size: 11px;
  }
  
  .contract-bid {
    font-size: 16px;
  }
  
  .auction-stats,
  .trick-stats {
    gap: 2px;
  }
  
  .stat-item {
    font-size: 10px;
  }
}

/* Ultra-wide screen optimizations */
@media (min-width: 1440px) {
  .game-status-indicator {
    min-width: 320px;
    max-width: 380px;
    padding: 20px;
  }
  
  .phase-name {
    font-size: 20px;
  }
  
  .player-message {
    font-size: 18px;
  }
}

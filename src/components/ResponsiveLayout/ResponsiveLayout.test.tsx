/**
 * Unit tests for ResponsiveLayout component
 */

import React from 'react';
import { render, screen, act } from '@testing-library/react';
import '@testing-library/jest-dom';
import ResponsiveLayout, { 
  useResponsive, 
  ResponsiveGrid, 
  ResponsiveFlex, 
  ResponsiveVisibility 
} from './ResponsiveLayout';

// Mock window.innerWidth and innerHeight
const mockWindowSize = (width: number, height: number) => {
  Object.defineProperty(window, 'innerWidth', {
    writable: true,
    configurable: true,
    value: width,
  });
  Object.defineProperty(window, 'innerHeight', {
    writable: true,
    configurable: true,
    value: height,
  });
};

// Test component that uses the useResponsive hook
const TestResponsiveComponent: React.FC = () => {
  const responsive = useResponsive();
  
  return (
    <div data-testid="responsive-info">
      <span data-testid="is-mobile">{responsive.isMobile.toString()}</span>
      <span data-testid="is-tablet">{responsive.isTablet.toString()}</span>
      <span data-testid="is-desktop">{responsive.isDesktop.toString()}</span>
      <span data-testid="width">{responsive.width}</span>
      <span data-testid="height">{responsive.height}</span>
    </div>
  );
};

describe('ResponsiveLayout', () => {
  beforeEach(() => {
    // Reset to desktop size by default
    mockWindowSize(1024, 768);
  });

  describe('ResponsiveLayout component', () => {
    it('renders without crashing', () => {
      render(
        <ResponsiveLayout>
          <div>Test content</div>
        </ResponsiveLayout>
      );
      
      expect(screen.getByTestId('responsive-layout')).toBeInTheDocument();
      expect(screen.getByText('Test content')).toBeInTheDocument();
    });

    it('applies correct classes based on props', () => {
      render(
        <ResponsiveLayout 
          className="custom-class" 
          padding={true} 
          center={true}
        >
          <div>Test content</div>
        </ResponsiveLayout>
      );
      
      const layout = screen.getByTestId('responsive-layout');
      expect(layout).toHaveClass('responsive-layout');
      expect(layout).toHaveClass('custom-class');
      expect(layout).toHaveClass('with-padding');
      expect(layout).toHaveClass('centered');
    });

    it('applies maxWidth style', () => {
      render(
        <ResponsiveLayout maxWidth="800px">
          <div>Test content</div>
        </ResponsiveLayout>
      );
      
      const layout = screen.getByTestId('responsive-layout');
      expect(layout).toHaveStyle({ maxWidth: '800px' });
    });
  });

  describe('useResponsive hook', () => {
    it('detects mobile breakpoint correctly', () => {
      mockWindowSize(600, 800);
      
      render(<TestResponsiveComponent />);
      
      expect(screen.getByTestId('is-mobile')).toHaveTextContent('true');
      expect(screen.getByTestId('is-tablet')).toHaveTextContent('false');
      expect(screen.getByTestId('is-desktop')).toHaveTextContent('false');
      expect(screen.getByTestId('width')).toHaveTextContent('600');
      expect(screen.getByTestId('height')).toHaveTextContent('800');
    });

    it('detects tablet breakpoint correctly', () => {
      mockWindowSize(900, 600);
      
      render(<TestResponsiveComponent />);
      
      expect(screen.getByTestId('is-mobile')).toHaveTextContent('false');
      expect(screen.getByTestId('is-tablet')).toHaveTextContent('true');
      expect(screen.getByTestId('is-desktop')).toHaveTextContent('false');
    });

    it('detects desktop breakpoint correctly', () => {
      mockWindowSize(1200, 800);
      
      render(<TestResponsiveComponent />);
      
      expect(screen.getByTestId('is-mobile')).toHaveTextContent('false');
      expect(screen.getByTestId('is-tablet')).toHaveTextContent('false');
      expect(screen.getByTestId('is-desktop')).toHaveTextContent('true');
    });

    it('updates on window resize', () => {
      mockWindowSize(1200, 800);
      
      render(<TestResponsiveComponent />);
      
      expect(screen.getByTestId('is-desktop')).toHaveTextContent('true');
      
      // Simulate window resize
      act(() => {
        mockWindowSize(600, 800);
        window.dispatchEvent(new Event('resize'));
      });
      
      expect(screen.getByTestId('is-mobile')).toHaveTextContent('true');
      expect(screen.getByTestId('is-desktop')).toHaveTextContent('false');
    });
  });

  describe('ResponsiveGrid component', () => {
    it('renders without crashing', () => {
      render(
        <ResponsiveGrid>
          <div>Item 1</div>
          <div>Item 2</div>
        </ResponsiveGrid>
      );
      
      expect(screen.getByTestId('responsive-grid')).toBeInTheDocument();
      expect(screen.getByText('Item 1')).toBeInTheDocument();
      expect(screen.getByText('Item 2')).toBeInTheDocument();
    });

    it('applies custom gap', () => {
      render(
        <ResponsiveGrid gap="20px">
          <div>Item 1</div>
          <div>Item 2</div>
        </ResponsiveGrid>
      );
      
      const grid = screen.getByTestId('responsive-grid');
      expect(grid).toHaveStyle({ gap: '20px' });
    });

    it('applies custom className', () => {
      render(
        <ResponsiveGrid className="custom-grid">
          <div>Item 1</div>
        </ResponsiveGrid>
      );
      
      const grid = screen.getByTestId('responsive-grid');
      expect(grid).toHaveClass('responsive-grid');
      expect(grid).toHaveClass('custom-grid');
    });
  });

  describe('ResponsiveFlex component', () => {
    it('renders without crashing', () => {
      render(
        <ResponsiveFlex>
          <div>Item 1</div>
          <div>Item 2</div>
        </ResponsiveFlex>
      );
      
      expect(screen.getByTestId('responsive-flex')).toBeInTheDocument();
      expect(screen.getByText('Item 1')).toBeInTheDocument();
      expect(screen.getByText('Item 2')).toBeInTheDocument();
    });

    it('applies flex properties correctly', () => {
      render(
        <ResponsiveFlex 
          direction="column" 
          justify="center" 
          align="center"
          gap="10px"
        >
          <div>Item 1</div>
          <div>Item 2</div>
        </ResponsiveFlex>
      );
      
      const flex = screen.getByTestId('responsive-flex');
      expect(flex).toHaveStyle({
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
        gap: '10px'
      });
    });

    it('changes direction on mobile when mobileDirection is specified', () => {
      mockWindowSize(600, 800); // Mobile size
      
      render(
        <ResponsiveFlex 
          direction="row" 
          mobileDirection="column"
        >
          <div>Item 1</div>
          <div>Item 2</div>
        </ResponsiveFlex>
      );
      
      const flex = screen.getByTestId('responsive-flex');
      expect(flex).toHaveStyle({ flexDirection: 'column' });
    });
  });

  describe('ResponsiveVisibility component', () => {
    it('shows content by default', () => {
      render(
        <ResponsiveVisibility>
          <div>Visible content</div>
        </ResponsiveVisibility>
      );
      
      expect(screen.getByText('Visible content')).toBeInTheDocument();
    });

    it('hides content on specified breakpoints', () => {
      mockWindowSize(600, 800); // Mobile size
      
      render(
        <ResponsiveVisibility hideOn={['mobile']}>
          <div>Hidden on mobile</div>
        </ResponsiveVisibility>
      );
      
      expect(screen.queryByText('Hidden on mobile')).not.toBeInTheDocument();
    });

    it('shows content only on specified breakpoints', () => {
      mockWindowSize(1200, 800); // Desktop size
      
      render(
        <ResponsiveVisibility showOn={['mobile']}>
          <div>Mobile only content</div>
        </ResponsiveVisibility>
      );
      
      expect(screen.queryByText('Mobile only content')).not.toBeInTheDocument();
    });

    it('shows content when current breakpoint is in showOn array', () => {
      mockWindowSize(1200, 800); // Desktop size
      
      render(
        <ResponsiveVisibility showOn={['desktop']}>
          <div>Desktop only content</div>
        </ResponsiveVisibility>
      );
      
      expect(screen.getByText('Desktop only content')).toBeInTheDocument();
    });
  });
});

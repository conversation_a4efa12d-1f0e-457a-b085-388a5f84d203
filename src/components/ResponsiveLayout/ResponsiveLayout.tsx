/**
 * ResponsiveLayout component for managing responsive behavior
 * Provides layout utilities and responsive containers
 */

import React, { useState, useEffect } from 'react';
import './ResponsiveLayout.css';

interface ResponsiveLayoutProps {
  children: React.ReactNode;
  className?: string;
  maxWidth?: string;
  padding?: boolean;
  center?: boolean;
}

interface BreakpointInfo {
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  isLargeDesktop: boolean;
  isUltrawide: boolean;
  width: number;
  height: number;
}

// Custom hook for responsive breakpoints
export const useResponsive = (): BreakpointInfo => {
  const [breakpointInfo, setBreakpointInfo] = useState<BreakpointInfo>({
    isMobile: false,
    isTablet: false,
    isDesktop: false,
    isLargeDesktop: false,
    isUltrawide: false,
    width: 0,
    height: 0
  });

  useEffect(() => {
    const updateBreakpoints = () => {
      const width = window.innerWidth;
      const height = window.innerHeight;

      setBreakpointInfo({
        isMobile: width < 768,
        isTablet: width >= 768 && width < 1024,
        isDesktop: width >= 1024 && width < 1280,
        isLargeDesktop: width >= 1280 && width < 1440,
        isUltrawide: width >= 1440,
        width,
        height
      });
    };

    updateBreakpoints();
    window.addEventListener('resize', updateBreakpoints);
    
    return () => window.removeEventListener('resize', updateBreakpoints);
  }, []);

  return breakpointInfo;
};

// Responsive container component
const ResponsiveLayout: React.FC<ResponsiveLayoutProps> = ({
  children,
  className = '',
  maxWidth = '100%',
  padding = true,
  center = true
}) => {
  const responsive = useResponsive();

  const containerClass = [
    'responsive-layout',
    responsive.isMobile ? 'mobile' : '',
    responsive.isTablet ? 'tablet' : '',
    responsive.isDesktop ? 'desktop' : '',
    responsive.isLargeDesktop ? 'large-desktop' : '',
    responsive.isUltrawide ? 'ultrawide' : '',
    padding ? 'with-padding' : '',
    center ? 'centered' : '',
    className
  ].filter(Boolean).join(' ');

  return (
    <div 
      className={containerClass}
      style={{ maxWidth }}
      data-testid="responsive-layout"
    >
      {children}
    </div>
  );
};

// Responsive grid component
interface ResponsiveGridProps {
  children: React.ReactNode;
  columns?: {
    mobile?: number;
    tablet?: number;
    desktop?: number;
    largeDesktop?: number;
  };
  gap?: string;
  className?: string;
}

export const ResponsiveGrid: React.FC<ResponsiveGridProps> = ({
  children,
  columns = { mobile: 1, tablet: 2, desktop: 3, largeDesktop: 4 },
  gap = 'var(--spacing-md)',
  className = ''
}) => {
  const responsive = useResponsive();

  const getColumns = () => {
    if (responsive.isMobile) return columns.mobile || 1;
    if (responsive.isTablet) return columns.tablet || 2;
    if (responsive.isDesktop) return columns.desktop || 3;
    return columns.largeDesktop || 4;
  };

  const gridStyle: React.CSSProperties = {
    display: 'grid',
    gridTemplateColumns: `repeat(${getColumns()}, 1fr)`,
    gap,
    width: '100%'
  };

  return (
    <div 
      className={`responsive-grid ${className}`}
      style={gridStyle}
      data-testid="responsive-grid"
    >
      {children}
    </div>
  );
};

// Responsive flex component
interface ResponsiveFlexProps {
  children: React.ReactNode;
  direction?: 'row' | 'column';
  mobileDirection?: 'row' | 'column';
  wrap?: boolean;
  justify?: 'flex-start' | 'center' | 'flex-end' | 'space-between' | 'space-around';
  align?: 'flex-start' | 'center' | 'flex-end' | 'stretch';
  gap?: string;
  className?: string;
}

export const ResponsiveFlex: React.FC<ResponsiveFlexProps> = ({
  children,
  direction = 'row',
  mobileDirection,
  wrap = true,
  justify = 'flex-start',
  align = 'stretch',
  gap = 'var(--spacing-md)',
  className = ''
}) => {
  const responsive = useResponsive();

  const flexDirection = responsive.isMobile && mobileDirection 
    ? mobileDirection 
    : direction;

  const flexStyle: React.CSSProperties = {
    display: 'flex',
    flexDirection,
    flexWrap: wrap ? 'wrap' : 'nowrap',
    justifyContent: justify,
    alignItems: align,
    gap,
    width: '100%'
  };

  return (
    <div 
      className={`responsive-flex ${className}`}
      style={flexStyle}
      data-testid="responsive-flex"
    >
      {children}
    </div>
  );
};

// Responsive visibility component
interface ResponsiveVisibilityProps {
  children: React.ReactNode;
  hideOn?: ('mobile' | 'tablet' | 'desktop' | 'large-desktop' | 'ultrawide')[];
  showOn?: ('mobile' | 'tablet' | 'desktop' | 'large-desktop' | 'ultrawide')[];
}

export const ResponsiveVisibility: React.FC<ResponsiveVisibilityProps> = ({
  children,
  hideOn = [],
  showOn = []
}) => {
  const responsive = useResponsive();

  const shouldHide = hideOn.some(breakpoint => {
    switch (breakpoint) {
      case 'mobile': return responsive.isMobile;
      case 'tablet': return responsive.isTablet;
      case 'desktop': return responsive.isDesktop;
      case 'large-desktop': return responsive.isLargeDesktop;
      case 'ultrawide': return responsive.isUltrawide;
      default: return false;
    }
  });

  const shouldShow = showOn.length === 0 || showOn.some(breakpoint => {
    switch (breakpoint) {
      case 'mobile': return responsive.isMobile;
      case 'tablet': return responsive.isTablet;
      case 'desktop': return responsive.isDesktop;
      case 'large-desktop': return responsive.isLargeDesktop;
      case 'ultrawide': return responsive.isUltrawide;
      default: return false;
    }
  });

  if (shouldHide || !shouldShow) {
    return null;
  }

  return <>{children}</>;
};

// Responsive spacing component
interface ResponsiveSpacingProps {
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'xxl';
  mobileSize?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'xxl';
  direction?: 'horizontal' | 'vertical' | 'both';
}

export const ResponsiveSpacing: React.FC<ResponsiveSpacingProps> = ({
  size = 'md',
  mobileSize,
  direction = 'both'
}) => {
  const responsive = useResponsive();
  
  const actualSize = responsive.isMobile && mobileSize ? mobileSize : size;
  const spacingVar = `var(--spacing-${actualSize})`;

  const style: React.CSSProperties = {
    width: direction === 'horizontal' || direction === 'both' ? spacingVar : '0',
    height: direction === 'vertical' || direction === 'both' ? spacingVar : '0',
    flexShrink: 0
  };

  return <div style={style} />;
};

export default ResponsiveLayout;

/**
 * ResponsiveLayout component styles
 */

.responsive-layout {
  width: 100%;
  box-sizing: border-box;
}

.responsive-layout.with-padding {
  padding: var(--spacing-md, 16px);
}

.responsive-layout.centered {
  margin: 0 auto;
}

/* Breakpoint-specific styles */
.responsive-layout.mobile.with-padding {
  padding: var(--spacing-sm, 8px);
}

.responsive-layout.tablet.with-padding {
  padding: var(--spacing-md, 16px);
}

.responsive-layout.desktop.with-padding {
  padding: var(--spacing-lg, 24px);
}

.responsive-layout.large-desktop.with-padding {
  padding: var(--spacing-xl, 32px);
}

.responsive-layout.ultrawide.with-padding {
  padding: var(--spacing-xxl, 48px);
}

/* Responsive grid styles */
.responsive-grid {
  width: 100%;
  box-sizing: border-box;
}

.responsive-grid > * {
  min-width: 0; /* Prevent grid items from overflowing */
}

/* Responsive flex styles */
.responsive-flex {
  width: 100%;
  box-sizing: border-box;
}

.responsive-flex > * {
  min-width: 0; /* Prevent flex items from overflowing */
}

/* Utility classes for responsive behavior */
.responsive-container {
  width: 100%;
  max-width: var(--breakpoint-xxl, 1920px);
  margin: 0 auto;
  padding: 0 var(--spacing-md, 16px);
  box-sizing: border-box;
}

.responsive-text {
  font-size: clamp(var(--font-size-sm, 14px), 2.5vw, var(--font-size-lg, 18px));
  line-height: 1.5;
}

.responsive-spacing {
  padding: clamp(var(--spacing-sm, 8px), 2vw, var(--spacing-lg, 24px));
}

.responsive-margin {
  margin: clamp(var(--spacing-sm, 8px), 2vw, var(--spacing-lg, 24px));
}

/* Responsive card sizing */
.card-responsive {
  width: clamp(var(--card-width-xs, 45px), 5vw, var(--card-width-lg, 90px));
  height: clamp(var(--card-height-xs, 63px), 7vw, var(--card-height-lg, 126px));
  aspect-ratio: 5 / 7;
}

/* Responsive table sizing */
.table-responsive {
  width: clamp(var(--table-min-width, 800px), 90vw, var(--table-max-width, 1400px));
  height: clamp(var(--table-min-height, 600px), 80vh, var(--table-max-height, 1000px));
}

/* Responsive sidebar */
.sidebar-responsive {
  width: clamp(var(--sidebar-width-sm, 280px), 25vw, var(--sidebar-width-lg, 360px));
}

/* Flexible layouts */
.flex-responsive {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-md, 16px);
}

.flex-responsive > * {
  flex: 1 1 auto;
  min-width: 0;
}

/* Grid layouts */
.grid-responsive {
  display: grid;
  gap: var(--spacing-md, 16px);
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

/* Aspect ratio utilities */
.aspect-ratio-card {
  aspect-ratio: 5 / 7; /* Standard playing card ratio */
}

.aspect-ratio-square {
  aspect-ratio: 1 / 1;
}

.aspect-ratio-wide {
  aspect-ratio: 16 / 9;
}

/* Responsive visibility utilities */
.hide-on-mobile {
  display: block;
}

.hide-on-tablet {
  display: block;
}

.hide-on-desktop {
  display: block;
}

.hide-on-large-desktop {
  display: block;
}

.hide-on-ultrawide {
  display: block;
}

.mobile-only,
.tablet-only,
.desktop-only,
.large-desktop-only,
.ultrawide-only {
  display: none;
}

/* Breakpoint-specific visibility */
@media (max-width: 767px) {
  .hide-on-mobile {
    display: none !important;
  }
  
  .mobile-only {
    display: block !important;
  }
}

@media (min-width: 768px) and (max-width: 1023px) {
  .hide-on-tablet {
    display: none !important;
  }
  
  .tablet-only {
    display: block !important;
  }
}

@media (min-width: 1024px) and (max-width: 1279px) {
  .hide-on-desktop {
    display: none !important;
  }
  
  .desktop-only {
    display: block !important;
  }
}

@media (min-width: 1280px) and (max-width: 1439px) {
  .hide-on-large-desktop {
    display: none !important;
  }
  
  .large-desktop-only {
    display: block !important;
  }
}

@media (min-width: 1440px) {
  .hide-on-ultrawide {
    display: none !important;
  }
  
  .ultrawide-only {
    display: block !important;
  }
}

/* Container queries (for modern browsers) */
@supports (container-type: inline-size) {
  .container-responsive {
    container-type: inline-size;
  }

  @container (max-width: 400px) {
    .container-compact {
      padding: var(--spacing-sm, 8px);
      font-size: var(--font-size-sm, 14px);
    }
  }

  @container (min-width: 600px) {
    .container-expanded {
      padding: var(--spacing-lg, 24px);
      font-size: var(--font-size-lg, 18px);
    }
  }
}

/* Focus management for keyboard navigation */
.responsive-focus:focus-visible {
  outline: 2px solid #4a9eff;
  outline-offset: 2px;
  border-radius: 4px;
}

/* Smooth scrolling for better UX */
@media (prefers-reduced-motion: no-preference) {
  html {
    scroll-behavior: smooth;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .responsive-contrast {
    border: 2px solid currentColor;
    background: Canvas;
    color: CanvasText;
  }
}

/* Print styles */
@media print {
  .responsive-container,
  .responsive-layout {
    max-width: none;
    padding: 0;
  }

  .hide-on-print {
    display: none !important;
  }

  .print-only {
    display: block !important;
  }
  
  .responsive-grid {
    grid-template-columns: repeat(2, 1fr) !important;
    gap: var(--spacing-sm, 8px) !important;
  }
  
  .responsive-flex {
    flex-direction: column !important;
    gap: var(--spacing-sm, 8px) !important;
  }
}

/* Accessibility and reduced motion */
@media (prefers-reduced-motion: reduce) {
  .responsive-animation {
    animation: none !important;
    transition: none !important;
  }
}

/* High DPI / Retina displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .responsive-image {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

/* Landscape orientation adjustments */
@media (orientation: landscape) and (max-height: 600px) {
  .responsive-layout.with-padding {
    padding: var(--spacing-sm, 8px);
  }
  
  .landscape-compact {
    padding: var(--spacing-sm, 8px) !important;
  }
}

/* Portrait orientation adjustments */
@media (orientation: portrait) and (max-width: 768px) {
  .portrait-stack {
    flex-direction: column !important;
  }

  .portrait-full-width {
    width: 100% !important;
  }
}

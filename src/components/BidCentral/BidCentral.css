/**
 * Bid Central Component Styles - Unified bidding and coaching interface
 */

.bid-central {
  width: 100%;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  overflow: hidden;
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', sans-serif;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.bid-central-header {
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  padding: 12px 16px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.bid-central-header h3 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #ecf0f1;
}

.turn-indicator {
  font-size: 11px;
  color: #bdc3c7;
  font-weight: 500;
}

.bid-central-content {
  padding: 8px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

/* Recommendation Section */
.recommendation-section {
  background: rgba(52, 152, 219, 0.1);
  border: 1px solid rgba(52, 152, 219, 0.3);
  border-radius: 6px;
  padding: 8px;
}

.analyzing {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 11px;
  color: #f39c12;
}

.spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.recommendation-content {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.recommendation-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 11px;
  font-weight: 600;
  color: #3498db;
}

.recommended-bid {
  display: flex;
  align-items: center;
  gap: 8px;
}

.bid-value {
  font-size: 14px;
  font-weight: 700;
  color: #ecf0f1;
  padding: 3px 6px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
  min-width: 40px;
  text-align: center;
}

.bid-value.red {
  color: #e74c3c;
}

.bid-value.black {
  color: #ecf0f1;
}

.bid-value.blue {
  color: #3498db;
}

.execute-btn {
  background: rgba(46, 204, 113, 0.8);
  color: white;
  border: none;
  border-radius: 3px;
  padding: 3px 6px;
  font-size: 9px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.execute-btn:hover {
  background: rgba(46, 204, 113, 1);
  transform: translateY(-1px);
}

.recommendation-reason {
  font-size: 10px;
  color: #95a5a6;
  line-height: 1.3;
  font-style: italic;
}

/* Hand Analysis - Enhanced */
.hand-analysis {
  background: rgba(255, 255, 255, 0.03);
  border-radius: 6px;
  padding: 8px;
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.analysis-header {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 11px;
  font-weight: 600;
  color: #f39c12;
  margin-bottom: 6px;
}

.analysis-icon {
  font-size: 12px;
}

.analysis-stats {
  display: flex;
  justify-content: space-between;
  gap: 8px;
  margin-bottom: 8px;
}

.stat {
  display: flex;
  flex-direction: column;
  align-items: center;
  font-size: 9px;
  flex: 1;
}

.stat span:first-child {
  color: #95a5a6;
  font-weight: 500;
  margin-bottom: 2px;
}

.value {
  color: #ecf0f1;
  font-weight: 600;
  font-size: 10px;
}

.value.red {
  color: #e74c3c;
}

.value.black {
  color: #ecf0f1;
}

.analysis-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.strength-assessment {
  font-size: 10px;
  color: #e67e22;
  text-align: center;
  font-weight: 600;
}

.bidding-strategy {
  font-size: 9px;
  color: #bdc3c7;
  text-align: center;
  line-height: 1.3;
  font-style: italic;
}

/* Coaching Advice */
.coaching-advice {
  background: rgba(46, 204, 113, 0.1);
  border: 1px solid rgba(46, 204, 113, 0.3);
  border-radius: 6px;
  padding: 8px;
}

.advice-header {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 11px;
  font-weight: 600;
  color: #2ecc71;
  margin-bottom: 6px;
}

.advice-icon {
  font-size: 12px;
}

.advice-content {
  display: flex;
  flex-direction: column;
}

.advice-text {
  font-size: 10px;
  color: #ecf0f1;
  line-height: 1.4;
  text-align: left;
}

/* Bidding Controls */
.bidding-controls {
  background: rgba(255, 255, 255, 0.03);
  border-radius: 6px;
  padding: 8px;
  border: 1px solid rgba(255, 255, 255, 0.05);
  display: flex;
  flex-direction: column;
  gap: 8px;
}

/* Level Section - Horizontal Layout */
.level-section {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.section-label {
  font-size: 11px;
  font-weight: 600;
  color: #bdc3c7;
}

.level-buttons {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.level-button {
  width: 28px;
  height: 28px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  font-size: 11px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.level-button:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

.level-button.selected {
  background: rgba(52, 152, 219, 0.8);
  border-color: rgba(52, 152, 219, 1);
  color: white;
  box-shadow: 0 2px 4px rgba(52, 152, 219, 0.3);
}

/* Suit Section */
.suit-section {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.suit-buttons {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.suit-button {
  width: 32px;
  height: 28px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
}

.suit-button:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

.suit-button.selected {
  border-color: #ffd700;
  background: rgba(255, 215, 0, 0.2);
  box-shadow: 0 2px 4px rgba(255, 215, 0, 0.3);
}

.suit-button.red {
  color: #e74c3c;
}

.suit-button.black {
  color: #ecf0f1;
}

.suit-button.blue {
  color: #3498db;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: 8px;
  margin-top: 4px;
}

.pass-button {
  flex: 1;
  background: rgba(231, 76, 60, 0.8);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 12px;
  font-size: 11px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.pass-button:hover {
  background: rgba(231, 76, 60, 1);
  transform: translateY(-1px);
}

.bid-button {
  flex: 2;
  background: rgba(46, 204, 113, 0.8);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 12px;
  font-size: 11px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.bid-button:hover {
  background: rgba(46, 204, 113, 1);
  transform: translateY(-1px);
}

.pass-button:disabled,
.bid-button:disabled,
.level-button:disabled,
.suit-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/* Waiting State */
.waiting-state {
  background: rgba(255, 255, 255, 0.02);
  border-radius: 6px;
  padding: 12px;
  text-align: center;
}

.waiting-text {
  font-size: 11px;
  color: #95a5a6;
  margin-bottom: 4px;
}

.game-phase {
  font-size: 10px;
  color: #7f8c8d;
  font-style: italic;
}

/**
 * Bid Central - Unified bidding controls and coaching interface
 * Combines bidding panel and coach into one cohesive component
 */

import React, { useState, useEffect } from 'react';
import { Position, GameState, GamePhase, Card, BidValue, SpecialBid, BidLevel, BidSuit, Bid } from '../../types/bridge';
import { isValidSimpleBid } from '../../lib/bridge/biddingUtils';
import './BidCentral.css';

interface BidCentralProps {
  gameState: GameState;
  humanPosition: Position;
  onBid: (bid: Bid) => void;
  onGameStateChange?: (newState: GameState) => void;
}

const BidCentral: React.FC<BidCentralProps> = ({
  gameState,
  humanPosition,
  onBid,
  onGameStateChange
}) => {
  const [selectedLevel, setSelectedLevel] = useState<BidLevel | null>(null);
  const [selectedSuit, setSelectedSuit] = useState<BidSuit | null>(null);
  const [bidRecommendation, setBidRecommendation] = useState<{bid?: BidValue, reasoning: string} | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [advice, setAdvice] = useState<string>('');
  const [handAnalysis, setHandAnalysis] = useState<{
    highCardPoints: number;
    distribution: string;
    longestSuit: string;
    suitCounts: Record<string, number>;
    strength: string;
    biddingStrategy: string;
  } | null>(null);

  const currentPlayer = gameState.currentPlayer;
  const canBid = currentPlayer === humanPosition && gameState.phase === GamePhase.BIDDING;
  const disabled = !canBid;

  // Get last bid from auction
  const lastBid = gameState.auction.length > 0 ?
    gameState.auction[gameState.auction.length - 1] : null;

  // Get available bid levels based on last bid
  const getAvailableLevels = (): BidLevel[] => {
    if (!lastBid || typeof lastBid.value === 'string') {
      return [BidLevel.ONE, BidLevel.TWO, BidLevel.THREE, BidLevel.FOUR, BidLevel.FIVE, BidLevel.SIX, BidLevel.SEVEN];
    }

    const lastLevel = lastBid.value.level;
    const levels: BidLevel[] = [];

    // Add levels higher than the last bid
    const allLevels = [BidLevel.ONE, BidLevel.TWO, BidLevel.THREE, BidLevel.FOUR, BidLevel.FIVE, BidLevel.SIX, BidLevel.SEVEN];
    const lastLevelIndex = allLevels.indexOf(lastLevel);

    for (let i = lastLevelIndex; i < allLevels.length; i++) {
      levels.push(allLevels[i]);
    }

    return levels;
  };

  // Get available suits for selected level
  const getAvailableSuits = (level: BidLevel): BidSuit[] => {
    if (!lastBid || typeof lastBid.value === 'string') {
      return [BidSuit.CLUBS, BidSuit.DIAMONDS, BidSuit.HEARTS, BidSuit.SPADES, BidSuit.NO_TRUMP];
    }

    const lastLevel = lastBid.value.level;
    const lastSuit = lastBid.value.suit;

    // If bidding same level, must bid higher suit
    if (level === lastLevel) {
      const suits = [BidSuit.CLUBS, BidSuit.DIAMONDS, BidSuit.HEARTS, BidSuit.SPADES, BidSuit.NO_TRUMP];
      const lastSuitIndex = suits.indexOf(lastSuit);
      return suits.slice(lastSuitIndex + 1);
    }

    // If bidding higher level, all suits available
    return [BidSuit.CLUBS, BidSuit.DIAMONDS, BidSuit.HEARTS, BidSuit.SPADES, BidSuit.NO_TRUMP];
  };

  const availableLevels = getAvailableLevels();
  const availableSuits = selectedLevel ? getAvailableSuits(selectedLevel) : [];

  // Generate recommendations and analysis when it's the player's turn
  useEffect(() => {
    if (canBid) {
      generateBidRecommendation();
      analyzeHand();
      generateAdvice();
    }
  }, [gameState, humanPosition, canBid]);

  const generateBidRecommendation = () => {
    setIsAnalyzing(true);
    
    try {
      const humanPlayer = gameState.players[humanPosition];
      if (!humanPlayer || !humanPlayer.hand) return;

      const hcp = calculateHighCardPoints(humanPlayer.hand);
      let recommendedBid: BidValue;
      let reasoning: string;

      if (hcp >= 12) {
        // Find longest suit
        const suitCounts = {
          'S': humanPlayer.hand.filter(c => c.suit === 'S').length,
          'H': humanPlayer.hand.filter(c => c.suit === 'H').length,
          'D': humanPlayer.hand.filter(c => c.suit === 'D').length,
          'C': humanPlayer.hand.filter(c => c.suit === 'C').length,
        };

        const longestSuitKey = Object.entries(suitCounts).reduce((a, b) =>
          suitCounts[a[0] as keyof typeof suitCounts] > suitCounts[b[0] as keyof typeof suitCounts] ? a : b
        )[0];

        const longestSuit = longestSuitKey === 'S' ? BidSuit.SPADES :
                           longestSuitKey === 'H' ? BidSuit.HEARTS :
                           longestSuitKey === 'D' ? BidSuit.DIAMONDS :
                           BidSuit.CLUBS;

        recommendedBid = { level: BidLevel.ONE, suit: longestSuit };
        reasoning = `${hcp} HCP - Consider opening with your longest suit (${getSuitSymbol(longestSuit)} - ${suitCounts[longestSuitKey as keyof typeof suitCounts]} cards)`;
      } else {
        recommendedBid = SpecialBid.PASS;
        reasoning = `${hcp} HCP - With fewer than 12 points, passing is usually best`;
      }

      setBidRecommendation({ bid: recommendedBid, reasoning });
    } catch (error) {
      console.error('Error generating bid recommendation:', error);
      setBidRecommendation({
        bid: SpecialBid.PASS,
        reasoning: "Unable to analyze hand. Consider your options carefully."
      });
    } finally {
      setIsAnalyzing(false);
    }
  };

  const analyzeHand = () => {
    const humanPlayer = gameState.players[humanPosition];
    if (!humanPlayer || !humanPlayer.hand) return;

    const hcp = calculateHighCardPoints(humanPlayer.hand);
    const suitCounts = {
      'S': humanPlayer.hand.filter(card => card.suit === 'S').length,
      'H': humanPlayer.hand.filter(card => card.suit === 'H').length,
      'D': humanPlayer.hand.filter(card => card.suit === 'D').length,
      'C': humanPlayer.hand.filter(card => card.suit === 'C').length,
    };

    const longestSuitKey = Object.keys(suitCounts).reduce((a, b) =>
      suitCounts[a as keyof typeof suitCounts] > suitCounts[b as keyof typeof suitCounts] ? a : b
    );

    const distribution = `${suitCounts.S}-${suitCounts.H}-${suitCounts.D}-${suitCounts.C}`;

    // Determine hand strength
    let strength = '';
    if (hcp >= 22) strength = 'Very Strong (22+ HCP)';
    else if (hcp >= 16) strength = 'Strong (16-21 HCP)';
    else if (hcp >= 12) strength = 'Opening (12-15 HCP)';
    else if (hcp >= 6) strength = 'Weak (6-11 HCP)';
    else strength = 'Very Weak (0-5 HCP)';

    // Determine bidding strategy
    let biddingStrategy = '';
    const maxSuitLength = Math.max(...Object.values(suitCounts));
    const isBalanced = maxSuitLength <= 5 && Math.min(...Object.values(suitCounts)) >= 2;

    if (hcp >= 12) {
      if (isBalanced && hcp >= 15 && hcp <= 17) {
        biddingStrategy = 'Consider 1NT opening (15-17 HCP, balanced)';
      } else if (maxSuitLength >= 5) {
        biddingStrategy = `Open longest suit (${getSuitSymbol(longestSuitKey)} - ${suitCounts[longestSuitKey as keyof typeof suitCounts]} cards)`;
      } else {
        biddingStrategy = 'Open longest minor suit (balanced hand)';
      }
    } else if (hcp >= 6) {
      biddingStrategy = 'Consider overcall or preemptive bid if opponents open';
    } else {
      biddingStrategy = 'Pass unless you have exceptional distribution';
    }

    setHandAnalysis({
      highCardPoints: hcp,
      distribution,
      longestSuit: longestSuitKey,
      suitCounts,
      strength,
      biddingStrategy
    });
  };

  const generateAdvice = () => {
    setIsAnalyzing(true);

    try {
      const humanPlayer = gameState.players[humanPosition];
      if (!humanPlayer || !humanPlayer.hand) return;

      const hcp = calculateHighCardPoints(humanPlayer.hand);
      let newAdvice = '';

      if (gameState.phase === GamePhase.BIDDING) {
        if (gameState.auction.length === 0) {
          // Opening bid advice
          if (hcp >= 22) {
            newAdvice = `With ${hcp} HCP, you have a very strong hand. Consider opening 2♣ (artificial strong) or 2NT if balanced.`;
          } else if (hcp >= 20) {
            newAdvice = `Strong hand with ${hcp} HCP. Open at the 2-level in your longest suit or 2NT if balanced (20-21 HCP).`;
          } else if (hcp >= 15) {
            if (isBalancedHand(humanPlayer.hand)) {
              newAdvice = `Balanced hand with ${hcp} HCP. Perfect for 1NT opening (15-17 HCP range).`;
            } else {
              newAdvice = `Good opening hand with ${hcp} HCP. Open your longest suit at the 1-level.`;
            }
          } else if (hcp >= 12) {
            newAdvice = `Minimum opening hand with ${hcp} HCP. Open your longest suit. Remember: 5-card majors, longer minor if no 5-card major.`;
          } else if (hcp >= 10) {
            newAdvice = `Borderline hand with ${hcp} HCP. Consider passing unless you have good distribution or quick tricks.`;
          } else {
            newAdvice = `Light hand with ${hcp} HCP. Pass and wait for a better opportunity.`;
          }
        } else {
          // Response/overcall advice
          const lastBid = gameState.auction[gameState.auction.length - 1];
          const partnerBid = gameState.auction.length >= 2 ? gameState.auction[gameState.auction.length - 2] : null;

          if (partnerBid && typeof partnerBid.value === 'object') {
            // Partner opened
            if (hcp >= 13) {
              newAdvice = `Partner opened! With ${hcp} HCP, you have enough for game. Look for major suit fits or consider NT.`;
            } else if (hcp >= 6) {
              newAdvice = `Partner opened. With ${hcp} HCP, respond to show your values. Bid your longest suit or support partner.`;
            } else {
              newAdvice = `Partner opened but you only have ${hcp} HCP. Pass unless you have exceptional support.`;
            }
          } else {
            // Opponents opened
            if (hcp >= 17) {
              newAdvice = `Opponents opened but you have ${hcp} HCP. Consider doubling for takeout or overcalling.`;
            } else if (hcp >= 8) {
              newAdvice = `Opponents opened. With ${hcp} HCP, consider overcalling if you have a good 5+ card suit.`;
            } else {
              newAdvice = `Opponents opened and you have ${hcp} HCP. Usually best to pass and defend.`;
            }
          }
        }
      } else if (gameState.phase === GamePhase.PLAYING) {
        if (gameState.currentTrick.length === 0) {
          newAdvice = "You're leading. Consider leading from your longest and strongest suit, or lead partner's suit if they bid.";
        } else {
          newAdvice = "Follow suit if possible. If you can't follow suit, consider trumping or discarding your lowest card.";
        }
      }

      setAdvice(newAdvice);
    } catch (error) {
      console.error('Error generating advice:', error);
      setAdvice('Consider your hand strength and the auction when making your decision.');
    } finally {
      setIsAnalyzing(false);
    }
  };

  const isBalancedHand = (hand: Card[]): boolean => {
    const suitCounts = {
      'S': hand.filter(card => card.suit === 'S').length,
      'H': hand.filter(card => card.suit === 'H').length,
      'D': hand.filter(card => card.suit === 'D').length,
      'C': hand.filter(card => card.suit === 'C').length,
    };

    const counts = Object.values(suitCounts).sort((a, b) => b - a);
    // Balanced: 4-3-3-3, 4-4-3-2, 5-3-3-2
    return (counts[0] <= 5 && counts[3] >= 2);
  };

  const calculateHighCardPoints = (hand: Card[]): number => {
    return hand.reduce((total, card) => {
      switch (card.rank) {
        case 'A': return total + 4;
        case 'K': return total + 3;
        case 'Q': return total + 2;
        case 'J': return total + 1;
        default: return total;
      }
    }, 0);
  };

  const handleLevelSelect = (level: BidLevel) => {
    setSelectedLevel(level);
    setSelectedSuit(null);
  };

  const handleSuitSelect = (suit: BidSuit) => {
    setSelectedSuit(suit);
  };

  const handleBid = (bidValue: BidValue) => {
    const bid: Bid = {
      player: currentPlayer,
      value: bidValue,
      timestamp: new Date()
    };

    if (isValidSimpleBid(bid)) {
      onBid(bid);
      setSelectedLevel(null);
      setSelectedSuit(null);
    }
  };

  const handleMakeBid = () => {
    if (!selectedLevel || !selectedSuit) return;
    handleBid({ level: selectedLevel, suit: selectedSuit });
  };

  const handlePass = () => {
    handleBid(SpecialBid.PASS);
  };

  const handleExecuteRecommendation = () => {
    if (!bidRecommendation?.bid) return;
    handleBid(bidRecommendation.bid);
    setBidRecommendation(null);
  };

  const getSuitSymbol = (suit: string): string => {
    switch (suit) {
      case 'S': case BidSuit.SPADES: return '♠';
      case 'H': case BidSuit.HEARTS: return '♥';
      case 'D': case BidSuit.DIAMONDS: return '♦';
      case 'C': case BidSuit.CLUBS: return '♣';
      case BidSuit.NO_TRUMP: return 'NT';
      default: return suit;
    }
  };

  const getSuitColor = (suit: string): string => {
    return (suit === 'H' || suit === 'D' || suit === BidSuit.HEARTS || suit === BidSuit.DIAMONDS) ? 'red' : 
           (suit === BidSuit.NO_TRUMP) ? 'blue' : 'black';
  };

  const formatBidValue = (bidValue?: BidValue): string => {
    if (!bidValue || bidValue === SpecialBid.PASS) return "PASS";
    if (typeof bidValue === "object" && bidValue.level && bidValue.suit) {
      return `${bidValue.level}${getSuitSymbol(bidValue.suit)}`;
    }
    return "PASS";
  };

  const isMyTurn = gameState.currentPlayer === humanPosition;

  return (
    <div className="bid-central">
      <div className="bid-central-header">
        <h3>🎯 Bid Central</h3>
        <div className="turn-indicator">
          {isMyTurn ? "Your turn" : `${gameState.players[currentPlayer].name}'s turn`}
        </div>
      </div>

      <div className="bid-central-content">
        {/* Expert Recommendation - Only during bidding phase */}
        {isMyTurn && gameState.phase === GamePhase.BIDDING && (
          <div className="recommendation-section">
            {isAnalyzing ? (
              <div className="analyzing">
                <span className="spinner">⏳</span> Analyzing position...
              </div>
            ) : bidRecommendation ? (
              <div className="recommendation-content">
                <div className="recommendation-header">
                  <span>💡 Recommended:</span>
                  <div className="recommended-bid">
                    <span className={`bid-value ${getSuitColor(
                      bidRecommendation.bid !== SpecialBid.PASS &&
                      typeof bidRecommendation.bid === "object" &&
                      bidRecommendation.bid.suit ? bidRecommendation.bid.suit : ''
                    )}`}>
                      {formatBidValue(bidRecommendation.bid)}
                    </span>
                    <button
                      className="execute-btn"
                      title="Use recommended bid"
                      onClick={handleExecuteRecommendation}
                    >
                      ✓ Execute
                    </button>
                  </div>
                </div>
                <div className="recommendation-reason">
                  {bidRecommendation.reasoning}
                </div>
              </div>
            ) : null}
          </div>
        )}

        {/* Hand Analysis - Enhanced */}
        {handAnalysis && isMyTurn && gameState.phase === GamePhase.BIDDING && (
          <div className="hand-analysis">
            <div className="analysis-header">
              <span className="analysis-icon">📊</span>
              <span>Hand Analysis</span>
            </div>
            <div className="analysis-stats">
              <div className="stat">
                <span>HCP:</span>
                <span className="value">{handAnalysis.highCardPoints}</span>
              </div>
              <div className="stat">
                <span>Shape:</span>
                <span className="value">{handAnalysis.distribution}</span>
              </div>
              <div className="stat">
                <span>Longest:</span>
                <span className={`value ${getSuitColor(handAnalysis.longestSuit)}`}>
                  {getSuitSymbol(handAnalysis.longestSuit)} ({handAnalysis.suitCounts[handAnalysis.longestSuit as keyof typeof handAnalysis.suitCounts]})
                </span>
              </div>
            </div>
            <div className="analysis-details">
              <div className="strength-assessment">
                <strong>{handAnalysis.strength}</strong>
              </div>
              <div className="bidding-strategy">
                {handAnalysis.biddingStrategy}
              </div>
            </div>
          </div>
        )}

        {/* Detailed Coaching Advice */}
        {isMyTurn && advice && (
          <div className="coaching-advice">
            <div className="advice-header">
              <span className="advice-icon">🎓</span>
              <span>Bridge Coach</span>
            </div>
            <div className="advice-content">
              {isAnalyzing ? (
                <div className="analyzing">
                  <span className="spinner">⏳</span> Analyzing position...
                </div>
              ) : (
                <div className="advice-text">
                  {advice}
                </div>
              )}
            </div>
          </div>
        )}

        {/* Bidding Controls - Only when it's your turn */}
        {canBid && (
          <div className="bidding-controls">
            {/* Level Selection - Horizontal */}
            <div className="level-section">
              <div className="section-label">Level:</div>
              <div className="level-buttons">
                {availableLevels.map((level: BidLevel) => (
                  <button
                    key={level}
                    className={`level-button ${selectedLevel === level ? 'selected' : ''}`}
                    onClick={() => handleLevelSelect(level)}
                    disabled={disabled}
                  >
                    {level}
                  </button>
                ))}
              </div>
            </div>

            {/* Suit Selection - Show when level is selected */}
            {selectedLevel && (
              <div className="suit-section">
                <div className="section-label">Suit:</div>
                <div className="suit-buttons">
                  {availableSuits.map((suit: BidSuit) => (
                    <button
                      key={suit}
                      className={`suit-button ${getSuitColor(suit)} ${selectedSuit === suit ? 'selected' : ''}`}
                      onClick={() => handleSuitSelect(suit)}
                      disabled={disabled}
                    >
                      {getSuitSymbol(suit)}
                    </button>
                  ))}
                </div>
              </div>
            )}

            {/* Action Buttons */}
            <div className="action-buttons">
              <button
                className="pass-button"
                onClick={handlePass}
                disabled={disabled}
              >
                Pass
              </button>
              
              {selectedLevel && selectedSuit && (
                <button
                  className="bid-button"
                  onClick={handleMakeBid}
                  disabled={disabled}
                >
                  Bid {selectedLevel}{getSuitSymbol(selectedSuit)}
                </button>
              )}
            </div>
          </div>
        )}

        {/* Waiting State */}
        {!isMyTurn && (
          <div className="waiting-state">
            <div className="waiting-text">
              Waiting for {gameState.players[gameState.currentPlayer].name}...
            </div>
            <div className="game-phase">
              Phase: {gameState.phase}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default BidCentral;

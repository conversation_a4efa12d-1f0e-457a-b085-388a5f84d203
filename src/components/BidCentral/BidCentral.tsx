/**
 * Bid Central - Unified bidding controls and coaching interface
 * Combines bidding panel and coach into one cohesive component
 */

import React, { useState, useEffect } from 'react';
import { Position, GameState, GamePhase, Card, BidValue, SpecialBid, BidLevel, BidSuit, Bid } from '../../types/bridge';
import { isValidSimpleBid } from '../../lib/bridge/biddingUtils';
import './BidCentral.css';

interface BidCentralProps {
  gameState: GameState;
  humanPosition: Position;
  onBid: (bid: Bid) => void;
  onGameStateChange?: (newState: GameState) => void;
}

const BidCentral: React.FC<BidCentralProps> = ({
  gameState,
  humanPosition,
  onBid,
  onGameStateChange
}) => {
  const [selectedLevel, setSelectedLevel] = useState<BidLevel | null>(null);
  const [selectedSuit, setSelectedSuit] = useState<BidSuit | null>(null);
  const [bidRecommendation, setBidRecommendation] = useState<{bid?: BidValue, reasoning: string} | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [handAnalysis, setHandAnalysis] = useState<{
    highCardPoints: number;
    distribution: string;
    longestSuit: string;
    suitCounts: Record<string, number>;
  } | null>(null);

  const currentPlayer = gameState.currentPlayer;
  const canBid = currentPlayer === humanPosition && gameState.phase === GamePhase.BIDDING;
  const disabled = !canBid;

  // Get last bid from auction
  const lastBid = gameState.auction.length > 0 ?
    gameState.auction[gameState.auction.length - 1] : null;

  // Get available bid levels based on last bid
  const getAvailableLevels = (): BidLevel[] => {
    if (!lastBid || typeof lastBid.value === 'string') {
      return [BidLevel.ONE, BidLevel.TWO, BidLevel.THREE, BidLevel.FOUR, BidLevel.FIVE, BidLevel.SIX, BidLevel.SEVEN];
    }

    const lastLevel = lastBid.value.level;
    const levels: BidLevel[] = [];

    // Add levels higher than the last bid
    const allLevels = [BidLevel.ONE, BidLevel.TWO, BidLevel.THREE, BidLevel.FOUR, BidLevel.FIVE, BidLevel.SIX, BidLevel.SEVEN];
    const lastLevelIndex = allLevels.indexOf(lastLevel);

    for (let i = lastLevelIndex; i < allLevels.length; i++) {
      levels.push(allLevels[i]);
    }

    return levels;
  };

  // Get available suits for selected level
  const getAvailableSuits = (level: BidLevel): BidSuit[] => {
    if (!lastBid || typeof lastBid.value === 'string') {
      return [BidSuit.CLUBS, BidSuit.DIAMONDS, BidSuit.HEARTS, BidSuit.SPADES, BidSuit.NO_TRUMP];
    }

    const lastLevel = lastBid.value.level;
    const lastSuit = lastBid.value.suit;

    // If bidding same level, must bid higher suit
    if (level === lastLevel) {
      const suits = [BidSuit.CLUBS, BidSuit.DIAMONDS, BidSuit.HEARTS, BidSuit.SPADES, BidSuit.NO_TRUMP];
      const lastSuitIndex = suits.indexOf(lastSuit);
      return suits.slice(lastSuitIndex + 1);
    }

    // If bidding higher level, all suits available
    return [BidSuit.CLUBS, BidSuit.DIAMONDS, BidSuit.HEARTS, BidSuit.SPADES, BidSuit.NO_TRUMP];
  };

  const availableLevels = getAvailableLevels();
  const availableSuits = selectedLevel ? getAvailableSuits(selectedLevel) : [];

  // Generate recommendations and analysis when it's the player's turn
  useEffect(() => {
    if (canBid) {
      generateBidRecommendation();
      analyzeHand();
    }
  }, [gameState, humanPosition, canBid]);

  const generateBidRecommendation = () => {
    setIsAnalyzing(true);
    
    try {
      const humanPlayer = gameState.players[humanPosition];
      if (!humanPlayer || !humanPlayer.hand) return;

      const hcp = calculateHighCardPoints(humanPlayer.hand);
      let recommendedBid: BidValue;
      let reasoning: string;

      if (hcp >= 12) {
        // Find longest suit
        const suitCounts = {
          'S': humanPlayer.hand.filter(c => c.suit === 'S').length,
          'H': humanPlayer.hand.filter(c => c.suit === 'H').length,
          'D': humanPlayer.hand.filter(c => c.suit === 'D').length,
          'C': humanPlayer.hand.filter(c => c.suit === 'C').length,
        };

        const longestSuitKey = Object.entries(suitCounts).reduce((a, b) =>
          suitCounts[a[0] as keyof typeof suitCounts] > suitCounts[b[0] as keyof typeof suitCounts] ? a : b
        )[0];

        const longestSuit = longestSuitKey === 'S' ? BidSuit.SPADES :
                           longestSuitKey === 'H' ? BidSuit.HEARTS :
                           longestSuitKey === 'D' ? BidSuit.DIAMONDS :
                           BidSuit.CLUBS;

        recommendedBid = { level: BidLevel.ONE, suit: longestSuit };
        reasoning = `${hcp} HCP - Consider opening with your longest suit (${getSuitSymbol(longestSuit)} - ${suitCounts[longestSuitKey as keyof typeof suitCounts]} cards)`;
      } else {
        recommendedBid = SpecialBid.PASS;
        reasoning = `${hcp} HCP - With fewer than 12 points, passing is usually best`;
      }

      setBidRecommendation({ bid: recommendedBid, reasoning });
    } catch (error) {
      console.error('Error generating bid recommendation:', error);
      setBidRecommendation({
        bid: SpecialBid.PASS,
        reasoning: "Unable to analyze hand. Consider your options carefully."
      });
    } finally {
      setIsAnalyzing(false);
    }
  };

  const analyzeHand = () => {
    const humanPlayer = gameState.players[humanPosition];
    if (!humanPlayer || !humanPlayer.hand) return;

    const hcp = calculateHighCardPoints(humanPlayer.hand);
    const suitCounts = {
      'S': humanPlayer.hand.filter(card => card.suit === 'S').length,
      'H': humanPlayer.hand.filter(card => card.suit === 'H').length,
      'D': humanPlayer.hand.filter(card => card.suit === 'D').length,
      'C': humanPlayer.hand.filter(card => card.suit === 'C').length,
    };

    const longestSuitKey = Object.keys(suitCounts).reduce((a, b) => 
      suitCounts[a as keyof typeof suitCounts] > suitCounts[b as keyof typeof suitCounts] ? a : b
    );

    const distribution = `${suitCounts.S}-${suitCounts.H}-${suitCounts.D}-${suitCounts.C}`;

    setHandAnalysis({
      highCardPoints: hcp,
      distribution,
      longestSuit: longestSuitKey,
      suitCounts
    });
  };

  const calculateHighCardPoints = (hand: Card[]): number => {
    return hand.reduce((total, card) => {
      switch (card.rank) {
        case 'A': return total + 4;
        case 'K': return total + 3;
        case 'Q': return total + 2;
        case 'J': return total + 1;
        default: return total;
      }
    }, 0);
  };

  const handleLevelSelect = (level: BidLevel) => {
    setSelectedLevel(level);
    setSelectedSuit(null);
  };

  const handleSuitSelect = (suit: BidSuit) => {
    setSelectedSuit(suit);
  };

  const handleBid = (bidValue: BidValue) => {
    const bid: Bid = {
      player: currentPlayer,
      value: bidValue,
      timestamp: new Date()
    };

    if (isValidSimpleBid(bid)) {
      onBid(bid);
      setSelectedLevel(null);
      setSelectedSuit(null);
    }
  };

  const handleMakeBid = () => {
    if (!selectedLevel || !selectedSuit) return;
    handleBid({ level: selectedLevel, suit: selectedSuit });
  };

  const handlePass = () => {
    handleBid(SpecialBid.PASS);
  };

  const handleExecuteRecommendation = () => {
    if (!bidRecommendation?.bid) return;
    handleBid(bidRecommendation.bid);
    setBidRecommendation(null);
  };

  const getSuitSymbol = (suit: string): string => {
    switch (suit) {
      case 'S': case BidSuit.SPADES: return '♠';
      case 'H': case BidSuit.HEARTS: return '♥';
      case 'D': case BidSuit.DIAMONDS: return '♦';
      case 'C': case BidSuit.CLUBS: return '♣';
      case BidSuit.NO_TRUMP: return 'NT';
      default: return suit;
    }
  };

  const getSuitColor = (suit: string): string => {
    return (suit === 'H' || suit === 'D' || suit === BidSuit.HEARTS || suit === BidSuit.DIAMONDS) ? 'red' : 
           (suit === BidSuit.NO_TRUMP) ? 'blue' : 'black';
  };

  const formatBidValue = (bidValue?: BidValue): string => {
    if (!bidValue || bidValue === SpecialBid.PASS) return "PASS";
    if (typeof bidValue === "object" && bidValue.level && bidValue.suit) {
      return `${bidValue.level}${getSuitSymbol(bidValue.suit)}`;
    }
    return "PASS";
  };

  const isMyTurn = gameState.currentPlayer === humanPosition;

  return (
    <div className="bid-central">
      <div className="bid-central-header">
        <h3>🎯 Bid Central</h3>
        <div className="turn-indicator">
          {isMyTurn ? "Your turn" : `${gameState.players[currentPlayer].name}'s turn`}
        </div>
      </div>

      <div className="bid-central-content">
        {/* Expert Recommendation - Only during bidding phase */}
        {isMyTurn && gameState.phase === GamePhase.BIDDING && (
          <div className="recommendation-section">
            {isAnalyzing ? (
              <div className="analyzing">
                <span className="spinner">⏳</span> Analyzing position...
              </div>
            ) : bidRecommendation ? (
              <div className="recommendation-content">
                <div className="recommendation-header">
                  <span>💡 Recommended:</span>
                  <div className="recommended-bid">
                    <span className={`bid-value ${getSuitColor(
                      bidRecommendation.bid !== SpecialBid.PASS &&
                      typeof bidRecommendation.bid === "object" &&
                      bidRecommendation.bid.suit ? bidRecommendation.bid.suit : ''
                    )}`}>
                      {formatBidValue(bidRecommendation.bid)}
                    </span>
                    <button
                      className="execute-btn"
                      title="Use recommended bid"
                      onClick={handleExecuteRecommendation}
                    >
                      ✓ Execute
                    </button>
                  </div>
                </div>
                <div className="recommendation-reason">
                  {bidRecommendation.reasoning}
                </div>
              </div>
            ) : null}
          </div>
        )}

        {/* Hand Analysis - Compact */}
        {handAnalysis && isMyTurn && gameState.phase === GamePhase.BIDDING && (
          <div className="hand-analysis">
            <div className="analysis-stats">
              <div className="stat">
                <span>HCP:</span>
                <span className="value">{handAnalysis.highCardPoints}</span>
              </div>
              <div className="stat">
                <span>Shape:</span>
                <span className="value">{handAnalysis.distribution}</span>
              </div>
              <div className="stat">
                <span>Longest:</span>
                <span className={`value ${getSuitColor(handAnalysis.longestSuit)}`}>
                  {getSuitSymbol(handAnalysis.longestSuit)} ({handAnalysis.suitCounts[handAnalysis.longestSuit as keyof typeof handAnalysis.suitCounts]})
                </span>
              </div>
            </div>
          </div>
        )}

        {/* Bidding Controls - Only when it's your turn */}
        {canBid && (
          <div className="bidding-controls">
            {/* Level Selection - Horizontal */}
            <div className="level-section">
              <div className="section-label">Level:</div>
              <div className="level-buttons">
                {availableLevels.map((level: BidLevel) => (
                  <button
                    key={level}
                    className={`level-button ${selectedLevel === level ? 'selected' : ''}`}
                    onClick={() => handleLevelSelect(level)}
                    disabled={disabled}
                  >
                    {level}
                  </button>
                ))}
              </div>
            </div>

            {/* Suit Selection - Show when level is selected */}
            {selectedLevel && (
              <div className="suit-section">
                <div className="section-label">Suit:</div>
                <div className="suit-buttons">
                  {availableSuits.map((suit: BidSuit) => (
                    <button
                      key={suit}
                      className={`suit-button ${getSuitColor(suit)} ${selectedSuit === suit ? 'selected' : ''}`}
                      onClick={() => handleSuitSelect(suit)}
                      disabled={disabled}
                    >
                      {getSuitSymbol(suit)}
                    </button>
                  ))}
                </div>
              </div>
            )}

            {/* Action Buttons */}
            <div className="action-buttons">
              <button
                className="pass-button"
                onClick={handlePass}
                disabled={disabled}
              >
                Pass
              </button>
              
              {selectedLevel && selectedSuit && (
                <button
                  className="bid-button"
                  onClick={handleMakeBid}
                  disabled={disabled}
                >
                  Bid {selectedLevel}{getSuitSymbol(selectedSuit)}
                </button>
              )}
            </div>
          </div>
        )}

        {/* Waiting State */}
        {!isMyTurn && (
          <div className="waiting-state">
            <div className="waiting-text">
              Waiting for {gameState.players[gameState.currentPlayer].name}...
            </div>
            <div className="game-phase">
              Phase: {gameState.phase}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default BidCentral;

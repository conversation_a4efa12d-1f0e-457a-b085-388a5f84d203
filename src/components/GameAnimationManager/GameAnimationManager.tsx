/**
 * GameAnimationManager component for orchestrating complex game animations
 * Manages dealing, playing, and trick collection sequences
 */

import React, { useState, useEffect, useCallback } from 'react';
import { Card, Position, GameState } from '../../types/bridge';
import { DealingAnimation, PlayingAnimation, TrickCollection } from '../CardAnimations/CardAnimations';
import './GameAnimationManager.css';

interface GameAnimationManagerProps {
  gameState: GameState;
  onAnimationComplete?: (type: AnimationType) => void;
  isAnimationEnabled?: boolean;
}

type AnimationType = 'dealing' | 'playing' | 'collecting' | 'none';

interface AnimationState {
  type: AnimationType;
  isActive: boolean;
  data?: any;
}

const GameAnimationManager: React.FC<GameAnimationManagerProps> = ({
  gameState,
  onAnimationComplete,
  isAnimationEnabled = true
}) => {
  const [animationState, setAnimationState] = useState<AnimationState>({
    type: 'none',
    isActive: false
  });

  // Position mappings for animations
  const getPlayerPositions = useCallback(() => {
    // These would be calculated based on actual game table layout
    return {
      [Position.NORTH]: { x: 400, y: 50 },
      [Position.SOUTH]: { x: 400, y: 550 },
      [Position.EAST]: { x: 750, y: 300 },
      [Position.WEST]: { x: 50, y: 300 }
    };
  }, []);

  const getDeckPosition = useCallback(() => {
    return { x: 350, y: 250 }; // Center of table
  }, []);

  const getTrickPosition = useCallback(() => {
    return { x: 400, y: 300 }; // Center of table
  }, []);

  // Handle dealing animation
  const startDealingAnimation = useCallback((cards: Card[], targetPositions: Position[]) => {
    if (!isAnimationEnabled) {
      onAnimationComplete?.('dealing');
      return;
    }

    setAnimationState({
      type: 'dealing',
      isActive: true,
      data: {
        cards,
        targetPositions,
        deckPosition: getDeckPosition(),
        playerPositions: getPlayerPositions()
      }
    });
  }, [isAnimationEnabled, onAnimationComplete, getDeckPosition, getPlayerPositions]);

  // Handle card playing animation
  const startPlayingAnimation = useCallback((
    card: Card, 
    fromPosition: Position, 
    toPosition: { x: number; y: number }
  ) => {
    if (!isAnimationEnabled) {
      onAnimationComplete?.('playing');
      return;
    }

    const playerPositions = getPlayerPositions();
    const fromPos = playerPositions[fromPosition];

    setAnimationState({
      type: 'playing',
      isActive: true,
      data: {
        card,
        fromPosition: fromPos,
        toPosition
      }
    });
  }, [isAnimationEnabled, onAnimationComplete, getPlayerPositions]);

  // Handle trick collection animation
  const startTrickCollection = useCallback((
    cards: Card[], 
    cardPositions: { x: number; y: number }[], 
    winnerPosition: Position
  ) => {
    if (!isAnimationEnabled) {
      onAnimationComplete?.('collecting');
      return;
    }

    const playerPositions = getPlayerPositions();
    const winnerPos = playerPositions[winnerPosition];

    setAnimationState({
      type: 'collecting',
      isActive: true,
      data: {
        cards,
        cardPositions,
        winnerPosition: winnerPos
      }
    });
  }, [isAnimationEnabled, onAnimationComplete, getPlayerPositions]);

  // Handle animation completion
  const handleAnimationComplete = useCallback((type: AnimationType) => {
    setAnimationState({
      type: 'none',
      isActive: false
    });
    onAnimationComplete?.(type);
  }, [onAnimationComplete]);

  // Auto-trigger animations based on game state changes
  useEffect(() => {
    // This would be enhanced to detect game state changes and trigger appropriate animations
    // For now, it's a placeholder for the animation system
  }, [gameState]);

  // Render active animation
  const renderAnimation = () => {
    if (!animationState.isActive || !animationState.data) {
      return null;
    }

    switch (animationState.type) {
      case 'dealing':
        return (
          <DealingAnimation
            cards={animationState.data.cards}
            targetPositions={animationState.data.targetPositions}
            deckPosition={animationState.data.deckPosition}
            playerPositions={animationState.data.playerPositions}
            onDealComplete={() => handleAnimationComplete('dealing')}
            dealingSpeed={120}
          />
        );

      case 'playing':
        return (
          <PlayingAnimation
            card={animationState.data.card}
            fromPosition={animationState.data.fromPosition}
            toPosition={animationState.data.toPosition}
            onPlayComplete={() => handleAnimationComplete('playing')}
          />
        );

      case 'collecting':
        return (
          <TrickCollection
            cards={animationState.data.cards}
            cardPositions={animationState.data.cardPositions}
            winnerPosition={animationState.data.winnerPosition}
            onCollectionComplete={() => handleAnimationComplete('collecting')}
          />
        );

      default:
        return null;
    }
  };

  return (
    <div 
      className="game-animation-manager"
      data-testid="game-animation-manager"
      data-animation-active={animationState.isActive}
      data-animation-type={animationState.type}
    >
      {renderAnimation()}
    </div>
  );
};

// Hook for using the animation manager
export const useGameAnimations = (gameState: GameState, isEnabled: boolean = true) => {
  const [animationManager, setAnimationManager] = useState<{
    startDealing: (cards: Card[], positions: Position[]) => void;
    startPlaying: (card: Card, from: Position, to: { x: number; y: number }) => void;
    startCollecting: (cards: Card[], positions: { x: number; y: number }[], winner: Position) => void;
    isAnimating: boolean;
  } | null>(null);

  const [isAnimating, setIsAnimating] = useState(false);

  const handleAnimationComplete = useCallback((type: AnimationType) => {
    setIsAnimating(false);
  }, []);

  const startDealing = useCallback((cards: Card[], positions: Position[]) => {
    if (isEnabled) {
      setIsAnimating(true);
      // Trigger dealing animation
    }
  }, [isEnabled]);

  const startPlaying = useCallback((card: Card, from: Position, to: { x: number; y: number }) => {
    if (isEnabled) {
      setIsAnimating(true);
      // Trigger playing animation
    }
  }, [isEnabled]);

  const startCollecting = useCallback((
    cards: Card[], 
    positions: { x: number; y: number }[], 
    winner: Position
  ) => {
    if (isEnabled) {
      setIsAnimating(true);
      // Trigger collecting animation
    }
  }, [isEnabled]);

  useEffect(() => {
    setAnimationManager({
      startDealing,
      startPlaying,
      startCollecting,
      isAnimating
    });
  }, [startDealing, startPlaying, startCollecting, isAnimating]);

  return animationManager;
};

// Animation presets for common game scenarios
export const AnimationPresets = {
  // Fast animations for quick games
  fast: {
    dealingSpeed: 80,
    playingDuration: 300,
    collectingDuration: 250,
    hoverScale: 1.03,
    selectionScale: 1.05
  },

  // Normal animations for standard play
  normal: {
    dealingSpeed: 120,
    playingDuration: 500,
    collectingDuration: 400,
    hoverScale: 1.05,
    selectionScale: 1.1
  },

  // Slow animations for demonstration
  slow: {
    dealingSpeed: 200,
    playingDuration: 800,
    collectingDuration: 600,
    hoverScale: 1.08,
    selectionScale: 1.15
  },

  // No animations for accessibility
  none: {
    dealingSpeed: 0,
    playingDuration: 0,
    collectingDuration: 0,
    hoverScale: 1,
    selectionScale: 1
  }
};

export default GameAnimationManager;

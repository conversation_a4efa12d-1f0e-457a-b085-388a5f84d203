/**
 * GameAnimationManager component styles
 */

.game-animation-manager {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1000;
  overflow: hidden;
}

.game-animation-manager[data-animation-active="true"] {
  pointer-events: auto;
}

/* Animation overlay */
.game-animation-manager::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.1);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.game-animation-manager[data-animation-active="true"]::before {
  opacity: 1;
}

/* Animation type specific styles */
.game-animation-manager[data-animation-type="dealing"] {
  z-index: 1100;
}

.game-animation-manager[data-animation-type="playing"] {
  z-index: 1050;
}

.game-animation-manager[data-animation-type="collecting"] {
  z-index: 1075;
}

/* Enhanced dealing animation styles */
.game-animation-manager .dealing-animation {
  position: relative;
  width: 100%;
  height: 100%;
}

.game-animation-manager .dealing-animation .card-animation {
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
}

/* Enhanced playing animation styles */
.game-animation-manager .playing-animation {
  position: relative;
  width: 100%;
  height: 100%;
}

.game-animation-manager .playing-animation .card-animation {
  filter: drop-shadow(0 6px 12px rgba(0, 0, 0, 0.4));
}

/* Enhanced trick collection styles */
.game-animation-manager .trick-collection {
  position: relative;
  width: 100%;
  height: 100%;
}

.game-animation-manager .trick-collection .card-animation {
  filter: drop-shadow(0 2px 6px rgba(0, 0, 0, 0.25));
}

/* Particle effects for special animations */
.animation-particles {
  position: absolute;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1200;
}

.particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: #ffd700;
  border-radius: 50%;
  opacity: 0;
  animation: particleFloat 2s ease-out forwards;
}

@keyframes particleFloat {
  0% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
  100% {
    opacity: 0;
    transform: translateY(-50px) scale(0.5);
  }
}

/* Success animation for completed tricks */
.trick-success-animation {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  pointer-events: none;
  z-index: 1300;
}

.success-ring {
  width: 100px;
  height: 100px;
  border: 4px solid #28a745;
  border-radius: 50%;
  opacity: 0;
  animation: successRingExpand 1s ease-out forwards;
}

@keyframes successRingExpand {
  0% {
    opacity: 1;
    transform: scale(0.5);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.2);
  }
  100% {
    opacity: 0;
    transform: scale(2);
  }
}

.success-checkmark {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 48px;
  color: #28a745;
  opacity: 0;
  animation: successCheckmarkPop 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55) forwards;
}

@keyframes successCheckmarkPop {
  0% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0);
  }
  50% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1.3);
  }
  100% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(1);
  }
}

/* Loading animation for dealing */
.dealing-progress {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 12px 24px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  opacity: 0;
  animation: fadeInProgress 0.3s ease-out forwards;
}

@keyframes fadeInProgress {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

.dealing-progress-bar {
  width: 200px;
  height: 4px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 2px;
  margin-top: 8px;
  overflow: hidden;
}

.dealing-progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4a9eff, #28a745);
  border-radius: 2px;
  transition: width 0.3s ease;
}

/* Sound wave animation for audio feedback */
.sound-wave {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  gap: 2px;
  opacity: 0;
  animation: soundWaveFade 0.5s ease-out forwards;
}

@keyframes soundWaveFade {
  0% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.8);
  }
  50% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
  100% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(1.2);
  }
}

.sound-bar {
  width: 3px;
  height: 20px;
  background: #4a9eff;
  border-radius: 2px;
  animation: soundBarPulse 0.6s ease-in-out infinite;
}

.sound-bar:nth-child(1) { animation-delay: 0ms; }
.sound-bar:nth-child(2) { animation-delay: 100ms; }
.sound-bar:nth-child(3) { animation-delay: 200ms; }
.sound-bar:nth-child(4) { animation-delay: 300ms; }
.sound-bar:nth-child(5) { animation-delay: 400ms; }

@keyframes soundBarPulse {
  0%, 100% {
    transform: scaleY(0.5);
    opacity: 0.7;
  }
  50% {
    transform: scaleY(1);
    opacity: 1;
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .dealing-progress {
    bottom: 10px;
    padding: 8px 16px;
    font-size: 12px;
  }
  
  .dealing-progress-bar {
    width: 150px;
    height: 3px;
  }
  
  .success-ring {
    width: 80px;
    height: 80px;
    border-width: 3px;
  }
  
  .success-checkmark {
    font-size: 36px;
  }
  
  .particle {
    width: 3px;
    height: 3px;
  }
}

@media (max-width: 480px) {
  .dealing-progress {
    bottom: 5px;
    padding: 6px 12px;
    font-size: 11px;
  }
  
  .dealing-progress-bar {
    width: 120px;
    height: 2px;
  }
  
  .success-ring {
    width: 60px;
    height: 60px;
    border-width: 2px;
  }
  
  .success-checkmark {
    font-size: 24px;
  }
}

/* Performance optimizations */
.game-animation-manager,
.game-animation-manager * {
  will-change: transform, opacity;
  backface-visibility: hidden;
  transform-style: preserve-3d;
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .game-animation-manager .card-animation,
  .particle,
  .success-ring,
  .success-checkmark,
  .sound-wave,
  .sound-bar {
    animation: none !important;
    transition: none !important;
  }
  
  .game-animation-manager::before {
    transition: none !important;
  }
  
  .dealing-progress {
    animation: none !important;
    opacity: 1 !important;
    transform: translateX(-50%) !important;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .dealing-progress {
    background: Canvas;
    color: CanvasText;
    border: 2px solid currentColor;
  }
  
  .success-ring {
    border-color: currentColor;
  }
  
  .success-checkmark {
    color: currentColor;
  }
  
  .particle {
    background: currentColor;
  }
}

/* Print mode - hide all animations */
@media print {
  .game-animation-manager {
    display: none !important;
  }
}

/**
 * Simplified CardTable CSS - Clean, predictable layout
 * Uses CSS Grid for reliable positioning
 */

.card-table {
  display: grid;
  grid-template-areas:
    ". north ."
    "west center east"
    ". south .";
  grid-template-columns: 1fr 2fr 1fr;
  grid-template-rows: 1fr 2fr 1fr;
  width: 100%;
  height: 100%;
  min-height: 600px;
  gap: 20px;
  padding: 20px;
  background: linear-gradient(135deg, #0f4c3a 0%, #1a5c4a 100%);
  border-radius: 12px;
  box-shadow: inset 0 0 50px rgba(0, 0, 0, 0.3);
}

/* Position assignments */
.card-table-position.north {
  grid-area: north;
  justify-self: center;
  align-self: end;
}

.card-table-position.south {
  grid-area: south;
  justify-self: center;
  align-self: start;
}

.card-table-position.east {
  grid-area: east;
  justify-self: start;
  align-self: center;
}

.card-table-position.west {
  grid-area: west;
  justify-self: end;
  align-self: center;
}

.card-table-center {
  grid-area: center;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  border-radius: 20px;
}

/* Drag over state */
.card-table-center.drag-over {
  background: rgba(16, 185, 129, 0.2);
  border: 3px dashed #10b981;
  box-shadow:
    0 0 20px rgba(16, 185, 129, 0.3),
    inset 0 0 20px rgba(16, 185, 129, 0.1);
  transform: scale(1.02);
}

/* Player position styling */
.card-table-position {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  min-width: 200px;
}

/* Player info */
.player-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
}

.player-name {
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 600;
  text-align: center;
  min-width: 80px;
}

.current-indicator {
  background: #ff6b35;
  color: white;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 10px;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Hand container */
.hand-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100px;
}

/* Ensure PlayerHand components fit well */
.card-table-position .player-hand {
  max-width: 100%;
}

/* North and South hands - horizontal layout */
.card-table-position.north .player-hand,
.card-table-position.south .player-hand {
  width: auto;
  max-width: 600px;
}

/* East and West hands - vertical layout with two rows */
.card-table-position.east .player-hand,
.card-table-position.west .player-hand {
  width: auto;
  max-width: 200px;
}

/* Center area styling */
.trick-display {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 20px;
  min-height: 200px;
  min-width: 200px;
}

.current-trick {
  display: grid;
  grid-template-areas:
    ". north ."
    "west center east"
    ". south .";
  grid-template-columns: 1fr 1fr 1fr;
  grid-template-rows: 1fr 1fr 1fr;
  gap: 10px;
  width: 150px;
  height: 150px;
}

.played-card {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 70px;
  min-width: 50px;
}

.played-card.north { grid-area: north; }
.played-card.south { grid-area: south; }
.played-card.east { grid-area: east; }
.played-card.west { grid-area: west; }

/* Drop zone indicator */
.drop-zone-indicator {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(16, 185, 129, 0.9);
  color: white;
  padding: 12px 20px;
  border-radius: 12px;
  font-weight: 600;
  font-size: 14px;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
  animation: dropZonePulse 1.5s ease-in-out infinite;
  z-index: 10;
}

.drop-zone-text {
  text-align: center;
  white-space: nowrap;
}

@keyframes dropZonePulse {
  0%, 100% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0.9;
  }
  50% {
    transform: translate(-50%, -50%) scale(1.05);
    opacity: 1;
  }
}

/* Success feedback for successful card play */
.card-table-center.play-success {
  animation: playSuccess 0.6s ease;
}

@keyframes playSuccess {
  0% {
    background: rgba(16, 185, 129, 0.2);
    transform: scale(1);
  }
  50% {
    background: rgba(16, 185, 129, 0.4);
    transform: scale(1.05);
  }
  100% {
    background: rgba(16, 185, 129, 0.1);
    transform: scale(1);
  }
}

.game-status {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.phase-indicator {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  padding: 4px 12px;
  border-radius: 4px;
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.contract-display {
  background: rgba(255, 215, 0, 0.9);
  color: #333;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: bold;
}

/* Responsive design */
@media (max-width: 1024px) {
  .card-table {
    grid-template-columns: 0.8fr 2fr 0.8fr;
    gap: 15px;
    padding: 15px;
    min-height: 500px;
  }
  
  .card-table-position {
    min-width: 150px;
  }
}

@media (max-width: 768px) {
  .card-table {
    grid-template-columns: 0.6fr 2fr 0.6fr;
    gap: 10px;
    padding: 10px;
    min-height: 400px;
  }
  
  .card-table-position {
    min-width: 120px;
  }
  
  .player-name {
    font-size: 12px;
    padding: 4px 8px;
  }
  
  .trick-display {
    min-height: 150px;
    min-width: 150px;
  }
}

@media (max-width: 480px) {
  .card-table {
    grid-template-areas:
      "north"
      "center"
      "south"
      "west"
      "east";
    grid-template-columns: 1fr;
    grid-template-rows: auto auto auto auto auto;
    gap: 15px;
  }
  
  .card-table-position {
    justify-self: center;
    align-self: center;
  }
}

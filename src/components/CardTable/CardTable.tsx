/**
 * Simplified CardTable component - Clean, reliable display of four player hands
 * Focuses purely on card display with predictable layout and visibility logic
 */

import React, { useState } from 'react';
import { Position, GameState, PlayerType, Card as CardType } from '../../types/bridge';
import { getValidCardsForPlayer } from '../../lib/bridge/gameStateUtils';
import PlayerHand from '../GameTable/PlayerHand';
import Card from '../Card/Card';
import './CardTable.css';

interface CardTableProps {
  gameState: GameState;
  openHandedMode: boolean;
  onCardPlay: (position: Position, cardIndex: number) => void;
  humanPosition?: Position;
  onLogPlayAttempt?: (
    position: Position,
    cardIndex: number,
    card: string,
    method: 'double-click' | 'drag-drop' | 'single-click' | 'coach-recommendation',
    success: boolean,
    error?: string
  ) => void;
}

interface DraggedCard {
  card: CardType;
  position: Position;
  cardIndex: number;
}

const CardTable: React.FC<CardTableProps> = ({
  gameState,
  openHandedMode,
  onCardPlay,
  humanPosition = Position.SOUTH,
  onLogPlayAttempt
}) => {
  const [draggedCard, setDraggedCard] = useState<DraggedCard | null>(null);
  const [isDragOver, setIsDragOver] = useState(false);
  const [showPlaySuccess, setShowPlaySuccess] = useState(false);
  
  // Unified card visibility logic - single source of truth
  const shouldShowCards = (position: Position): boolean => {
    const player = gameState.players[position];
    if (!player) return false;

    // Priority 1: Always show human player's own cards
    if (position === humanPosition && player.type === PlayerType.HUMAN) {
      return true;
    }

    // Priority 2: Open-handed mode shows all cards (for learning/debugging)
    if (openHandedMode) {
      return true;
    }

    // Priority 3: Show dummy cards during play phase (after opening lead)
    if (gameState.dummy === position && gameState.phase === 'playing') {
      // Dummy is visible after first card is played
      return gameState.tricks.length > 0 || gameState.currentTrick.length > 0;
    }

    // Priority 4: Hide all other AI cards (normal bridge rules)
    return false;
  };

  // Debug helper for visibility logic
  const getVisibilityReason = (position: Position): string => {
    const player = gameState.players[position];
    if (!player) return 'No player';

    if (position === humanPosition && player.type === PlayerType.HUMAN) {
      return 'Human player';
    }
    if (openHandedMode) return 'Open-handed mode';
    if (gameState.dummy === position && gameState.phase === 'playing') {
      return 'Dummy visible';
    }
    return 'Hidden (AI)';
  };

  // Determine if player can play cards
  const canPlayCards = (position: Position): boolean => {
    const player = gameState.players[position];

    // In debug mode (when we have a contract but minimal auction), be more permissive
    const isDebugMode = gameState.contract && gameState.auction.length < 4;

    const canPlay = !!(
      (gameState.currentPlayer === position || (isDebugMode && player?.type === PlayerType.HUMAN)) &&
      gameState.phase === 'playing' &&
      player?.type === PlayerType.HUMAN
    );

    console.log('canPlayCards check:', {
      position,
      currentPlayer: gameState.currentPlayer,
      phase: gameState.phase,
      playerType: player?.type,
      isDebugMode,
      canPlay
    });

    return canPlay;
  };

  // Handle card play
  const handleCardPlay = (position: Position) => (cardIndex: number) => {
    onCardPlay(position, cardIndex);

    // Show success animation
    setShowPlaySuccess(true);
    setTimeout(() => setShowPlaySuccess(false), 600);
  };

  // Handle drag start
  const handleDragStart = (position: Position, cardIndex: number, card: CardType) => (e: React.DragEvent) => {
    const dragData: DraggedCard = { card, position, cardIndex };
    setDraggedCard(dragData);
    e.dataTransfer.setData('application/json', JSON.stringify(dragData));
    e.dataTransfer.effectAllowed = 'move';

    // Add dragging class to the card
    const target = e.target as HTMLElement;
    target.classList.add('dragging');
  };

  // Handle drag end
  const handleDragEnd = (e: React.DragEvent) => {
    setDraggedCard(null);
    const target = e.target as HTMLElement;
    target.classList.remove('dragging');
  };

  // Handle drop zone events
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
    setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    // Only set drag over to false if we're leaving the drop zone entirely
    const rect = (e.currentTarget as HTMLElement).getBoundingClientRect();
    const x = e.clientX;
    const y = e.clientY;

    if (x < rect.left || x > rect.right || y < rect.top || y > rect.bottom) {
      setIsDragOver(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);

    try {
      const dragData = JSON.parse(e.dataTransfer.getData('application/json')) as DraggedCard;
      if (dragData && canPlayCards(dragData.position)) {
        const playerCards = gameState.players[dragData.position]?.hand || [];
        const cardToPlay = playerCards[dragData.cardIndex];

        // In debug mode, allow any card to be played
        const isDebugMode = gameState.contract && gameState.auction.length < 4;

        if (cardToPlay) {
          if (isDebugMode) {
            console.log('🎯 DEBUG MODE: Allowing drag-and-drop of any card:', `${cardToPlay.rank}${cardToPlay.suit}`);
            onCardPlay(dragData.position, dragData.cardIndex);
          } else {
            // Normal mode - validate card is playable
            const validCards = getValidCardsForPlayer(gameState, dragData.position);
            if (validCards.some(validCard =>
              validCard.suit === cardToPlay.suit && validCard.rank === cardToPlay.rank
            )) {
              onCardPlay(dragData.position, dragData.cardIndex);
            } else {
              console.warn('Attempted to play invalid card via drag and drop:', `${cardToPlay.rank}${cardToPlay.suit}`);
            }
          }
        }
      }
    } catch (error) {
      console.error('Error handling card drop:', error);
    }
  };

  // Get player info for display
  const getPlayerInfo = (position: Position) => {
    const player = gameState.players[position];
    if (!player) return { name: '', cardCount: 0 };
    
    const isDummy = gameState.dummy === position;
    const isHuman = player.type === PlayerType.HUMAN;
    const isCurrentPlayer = gameState.currentPlayer === position;
    
    let name = '';
    if (isHuman) {
      name = position === humanPosition ? 'Your Hand' : 'Human Player';
    } else {
      name = `AI ${position}${isDummy ? ' (Dummy)' : ''}`;
    }
    
    return {
      name,
      cardCount: player.hand?.length || 0,
      isCurrentPlayer,
      isDummy,
      isHuman
    };
  };

  return (
    <div className="card-table">
      {/* North Player */}
      <div className="card-table-position north">
        <div className="player-info">
          <div className="player-name">{getPlayerInfo(Position.NORTH).name}</div>
          {getPlayerInfo(Position.NORTH).isCurrentPlayer && (
            <div className="current-indicator">CURRENT</div>
          )}
        </div>

        <div className="hand-container">
          <PlayerHand
            cards={gameState.players[Position.NORTH]?.hand || []}
            position={Position.NORTH}
            shouldShowCards={shouldShowCards(Position.NORTH)}
            canPlayCards={canPlayCards(Position.NORTH)}
            onCardPlay={handleCardPlay(Position.NORTH)}
            onCardDragStart={handleDragStart}
            onCardDragEnd={handleDragEnd}
            gameState={gameState}
            onLogPlayAttempt={onLogPlayAttempt}
          />
        </div>
      </div>

      {/* West Player */}
      <div className="card-table-position west">
        <div className="player-info">
          <div className="player-name">{getPlayerInfo(Position.WEST).name}</div>
          {getPlayerInfo(Position.WEST).isCurrentPlayer && (
            <div className="current-indicator">CURRENT</div>
          )}
        </div>
        <div className="hand-container">
          <PlayerHand
            cards={gameState.players[Position.WEST]?.hand || []}
            position={Position.WEST}
            shouldShowCards={shouldShowCards(Position.WEST)}
            canPlayCards={canPlayCards(Position.WEST)}
            onCardPlay={handleCardPlay(Position.WEST)}
            onCardDragStart={handleDragStart}
            onCardDragEnd={handleDragEnd}
            gameState={gameState}
            onLogPlayAttempt={onLogPlayAttempt}
          />
        </div>
      </div>

      {/* Center Area - For played cards/tricks */}
      <div
        className={`card-table-center ${isDragOver ? 'drag-over' : ''} ${showPlaySuccess ? 'play-success' : ''}`}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        <div className="trick-display">
          {/* Current trick display */}
          {gameState.currentTrick.length > 0 && (
            <div className="current-trick">
              {/* Validate and limit to maximum 4 cards */}
              {gameState.currentTrick.slice(0, 4).map((playedCard, index) => {
                // Log warning if more than 4 cards detected
                if (gameState.currentTrick.length > 4) {
                  console.warn(`⚠️ BRIDGE RULE VIOLATION: ${gameState.currentTrick.length} cards in current trick (max 4)`, gameState.currentTrick);
                }

                return (
                  <div key={`${playedCard.player}-${playedCard.card.suit}-${playedCard.card.rank}-${index}`} className={`played-card ${playedCard.player.toLowerCase()}`}>
                    <Card
                      card={playedCard.card}
                      size="small"
                    />
                  </div>
                );
              })}
            </div>
          )}

          {/* Drop zone indicator */}
          {isDragOver && (
            <div className="drop-zone-indicator">
              <div className="drop-zone-text">Drop card here to play</div>
            </div>
          )}
          
          {/* Game status */}
          <div className="game-status">
            <div className="phase-indicator">{gameState.phase}</div>
            {gameState.contract && (
              <div className="contract-display">
                {gameState.contract.level} {gameState.contract.suit}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* East Player */}
      <div className="card-table-position east">
        <div className="player-info">
          <div className="player-name">{getPlayerInfo(Position.EAST).name}</div>
          {getPlayerInfo(Position.EAST).isCurrentPlayer && (
            <div className="current-indicator">CURRENT</div>
          )}
        </div>
        <div className="hand-container">
          <PlayerHand
            cards={gameState.players[Position.EAST]?.hand || []}
            position={Position.EAST}
            shouldShowCards={shouldShowCards(Position.EAST)}
            canPlayCards={canPlayCards(Position.EAST)}
            onCardPlay={handleCardPlay(Position.EAST)}
            onCardDragStart={handleDragStart}
            onCardDragEnd={handleDragEnd}
            gameState={gameState}
            onLogPlayAttempt={onLogPlayAttempt}
          />
        </div>
      </div>

      {/* South Player */}
      <div className="card-table-position south">
        <div className="player-info">
          <div className="player-name">{getPlayerInfo(Position.SOUTH).name}</div>
          {getPlayerInfo(Position.SOUTH).isCurrentPlayer && (
            <div className="current-indicator">CURRENT</div>
          )}
        </div>

        <div className="hand-container">
          <PlayerHand
            cards={gameState.players[Position.SOUTH]?.hand || []}
            position={Position.SOUTH}
            shouldShowCards={shouldShowCards(Position.SOUTH)}
            canPlayCards={canPlayCards(Position.SOUTH)}
            onCardPlay={handleCardPlay(Position.SOUTH)}
            onCardDragStart={handleDragStart}
            onCardDragEnd={handleDragEnd}
            gameState={gameState}
            onLogPlayAttempt={onLogPlayAttempt}
          />
        </div>
      </div>
    </div>
  );
};

export default CardTable;

/**
 * TrickWinnerDisplay component styles
 * Overlay that shows trick winner information
 */

.trick-winner-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease-in;
}

.trick-winner-display {
  background: linear-gradient(135deg, #2c5530 0%, #1a3d1f 100%);
  border: 3px solid #4a7c59;
  border-radius: 15px;
  padding: 30px 40px;
  text-align: center;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
  animation: slideIn 0.4s ease-out;
  max-width: 400px;
  min-width: 300px;
}

.winner-announcement {
  color: white;
}

.winner-name {
  font-size: 28px;
  font-weight: bold;
  margin-bottom: 15px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  color: #ffd700; /* Gold color for winner */
}

.win-reason {
  font-size: 18px;
  font-weight: 500;
  color: #e8f5e8;
  line-height: 1.4;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    transform: translateY(-50px) scale(0.9);
    opacity: 0;
  }
  to {
    transform: translateY(0) scale(1);
    opacity: 1;
  }
}

/* Responsive design */
@media (max-width: 768px) {
  .trick-winner-display {
    margin: 20px;
    padding: 20px 25px;
    max-width: none;
    min-width: 250px;
  }
  
  .winner-name {
    font-size: 24px;
    margin-bottom: 12px;
  }
  
  .win-reason {
    font-size: 16px;
  }
}

@media (max-width: 480px) {
  .trick-winner-display {
    margin: 15px;
    padding: 15px 20px;
  }
  
  .winner-name {
    font-size: 20px;
    margin-bottom: 10px;
  }
  
  .win-reason {
    font-size: 14px;
  }
}

/**
 * TrickWinnerDisplay component
 * Shows who won the trick and why, displayed for 2 seconds after trick completion
 */

import React, { useEffect, useState } from 'react';
import { Position, PlayedCard, Contract, Suit } from '../../types/bridge';
import { getTrickWinner } from '../../lib/bridge/trickUtils';
import { getRankValue } from '../../lib/bridge/cardUtils';
import './TrickWinnerDisplay.css';

interface TrickWinnerDisplayProps {
  trick: PlayedCard[];
  contract: Contract | null;
  onComplete: () => void;
  visible: boolean;
}

const TrickWinnerDisplay: React.FC<TrickWinnerDisplayProps> = ({
  trick,
  contract,
  onComplete,
  visible
}) => {
  const [winner, setWinner] = useState<Position | null>(null);
  const [reason, setReason] = useState<string>('');

  useEffect(() => {
    if (visible && trick.length === 4) {
      const trickWinner = getTrickWinner(trick, contract);
      const winReason = getWinReason(trick, contract, trickWinner);
      
      setWinner(trickWinner);
      setReason(winReason);

      // Auto-hide after 2 seconds
      const timer = setTimeout(() => {
        onComplete();
      }, 2000);

      return () => clearTimeout(timer);
    }
  }, [visible, trick, contract, onComplete]);

  const getWinReason = (trick: PlayedCard[], contract: Contract | null, winner: Position): string => {
    if (trick.length !== 4) return '';

    const leadSuit = trick[0].card.suit;
    const trumpSuit = getTrumpSuit(contract);
    const winningCard = trick.find(played => played.player === winner)?.card;
    
    if (!winningCard) return '';

    const getSuitSymbol = (suit: Suit): string => {
      switch (suit) {
        case Suit.SPADES: return '♠';
        case Suit.HEARTS: return '♥';
        case Suit.DIAMONDS: return '♦';
        case Suit.CLUBS: return '♣';
        default: return suit;
      }
    };

    const getSuitName = (suit: Suit): string => {
      switch (suit) {
        case Suit.SPADES: return 'Spades';
        case Suit.HEARTS: return 'Hearts';
        case Suit.DIAMONDS: return 'Diamonds';
        case Suit.CLUBS: return 'Clubs';
        default: return suit;
      }
    };

    const cardDisplay = `${winningCard.rank}${getSuitSymbol(winningCard.suit)}`;

    // Check if won with trump
    if (trumpSuit && winningCard.suit === trumpSuit && leadSuit !== trumpSuit) {
      return `Won with trump ${cardDisplay}`;
    }

    // Check if won by following suit with highest card
    if (winningCard.suit === leadSuit) {
      const suitCards = trick.filter(played => played.card.suit === leadSuit);
      const highestInSuit = suitCards.reduce((highest, current) => 
        getRankValue(current.card.rank) > getRankValue(highest.card.rank) ? current : highest
      );
      
      if (highestInSuit.player === winner) {
        return `Won with highest ${getSuitName(leadSuit)} (${cardDisplay})`;
      }
    }

    // Check if won with highest trump
    if (trumpSuit && winningCard.suit === trumpSuit) {
      const trumpCards = trick.filter(played => played.card.suit === trumpSuit);
      if (trumpCards.length > 1) {
        return `Won with highest trump ${cardDisplay}`;
      } else {
        return `Won with trump ${cardDisplay}`;
      }
    }

    // Default case
    return `Won with ${cardDisplay}`;
  };

  const getTrumpSuit = (contract: Contract | null): Suit | null => {
    if (!contract || contract.suit === 'NT') return null;
    // Convert BidSuit to Suit
    switch (contract.suit) {
      case 'S': return Suit.SPADES;
      case 'H': return Suit.HEARTS;
      case 'D': return Suit.DIAMONDS;
      case 'C': return Suit.CLUBS;
      default: return null;
    }
  };

  const getPlayerName = (position: Position): string => {
    switch (position) {
      case Position.NORTH: return 'North';
      case Position.SOUTH: return 'South';
      case Position.EAST: return 'East';
      case Position.WEST: return 'West';
      default: return position;
    }
  };

  if (!visible || !winner) {
    return null;
  }

  return (
    <div className="trick-winner-overlay">
      <div className="trick-winner-display">
        <div className="winner-announcement">
          <div className="winner-name">
            {getPlayerName(winner)} wins the trick!
          </div>
          <div className="win-reason">
            {reason}
          </div>
        </div>
      </div>
    </div>
  );
};

export default TrickWinnerDisplay;

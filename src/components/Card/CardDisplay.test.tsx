/**
 * Card display tests - verify rank and suit are shown together
 */

import React from 'react';
import { render, screen } from '@testing-library/react';
import Card from './Card';
import { Suit, Rank } from '../../types/bridge';

describe('Card Display Format', () => {
  it('displays rank and suit together in corners', () => {
    const aceOfSpades = { suit: Suit.SPADES, rank: Rank.ACE };
    render(<Card card={aceOfSpades} />);
    
    // Should show "A♠" in the corners
    const rankSuitElements = document.querySelectorAll('.rank-suit');
    expect(rankSuitElements.length).toBe(2); // top-left and bottom-right corners
    
    rankSuitElements.forEach(element => {
      expect(element.textContent).toBe('A♠');
    });
  });

  it('displays different card combinations correctly', () => {
    const testCards = [
      { card: { suit: Suit.HEARTS, rank: Rank.KING }, expected: 'K♥' },
      { card: { suit: Suit.DIAMONDS, rank: Rank.QUEEN }, expected: 'Q♦' },
      { card: { suit: Suit.CLUBS, rank: Rank.JACK }, expected: 'J♣' },
      { card: { suit: Suit.SPADES, rank: Rank.TEN }, expected: '10♠' }
    ];

    testCards.forEach(({ card, expected }) => {
      const { unmount } = render(<Card card={card} />);
      
      const rankSuitElements = document.querySelectorAll('.rank-suit');
      expect(rankSuitElements.length).toBe(2);
      
      rankSuitElements.forEach(element => {
        expect(element.textContent).toBe(expected);
      });
      
      unmount();
    });
  });

  it('still displays center suit symbol', () => {
    const aceOfHearts = { suit: Suit.HEARTS, rank: Rank.ACE };
    render(<Card card={aceOfHearts} />);
    
    // Should still have the large center suit symbol
    const centerSuit = document.querySelector('.center-suit');
    expect(centerSuit).toBeInTheDocument();
    expect(centerSuit?.textContent).toBe('♥');
  });

  it('applies correct colors to rank-suit elements', () => {
    const redCard = { suit: Suit.HEARTS, rank: Rank.ACE };
    const { unmount } = render(<Card card={redCard} />);
    
    const cardElement = screen.getByTestId('card-A-H');
    expect(cardElement).toHaveClass('red');
    
    unmount();
    
    const blackCard = { suit: Suit.SPADES, rank: Rank.ACE };
    render(<Card card={blackCard} />);
    
    const blackCardElement = screen.getByTestId('card-A-S');
    expect(blackCardElement).toHaveClass('black');
  });
});

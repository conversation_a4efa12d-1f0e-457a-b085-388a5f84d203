/**
 * Fixed Card component styles
 * Works with existing Card.tsx structure
 */

/* Base playing card styling with dynamic sizing */
.playing-card {
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
  border: 1px solid #dee2e6;
  border-radius: 8px;
  box-shadow: 
    0 2px 4px rgba(0, 0, 0, 0.1),
    0 1px 2px rgba(0, 0, 0, 0.06);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: default;
  user-select: none;
  font-family: 'Arial', sans-serif;
  font-weight: bold;
  box-sizing: border-box;
  
  /* Dynamic sizing based on container */
  width: clamp(40px, 8vw, 70px);
  height: clamp(56px, 11.2vw, 98px);
  aspect-ratio: 5/7;
}

/* Size variants for different positions */
.playing-card.small {
  width: clamp(35px, 6vw, 50px);
  height: clamp(49px, 8.4vw, 70px);
  font-size: clamp(8px, 1.2vw, 12px);
}

.playing-card.medium {
  width: clamp(45px, 7vw, 60px);
  height: clamp(63px, 9.8vw, 84px);
  font-size: clamp(10px, 1.4vw, 14px);
}

.playing-card.large {
  width: clamp(55px, 9vw, 75px);
  height: clamp(77px, 12.6vw, 105px);
  font-size: clamp(12px, 1.6vw, 16px);
}

/* Card content layout */
.playing-card .card-content {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2px;
  box-sizing: border-box;
}

/* Card corners */
.playing-card .card-corner {
  position: absolute;
  display: flex;
  flex-direction: column;
  align-items: center;
  font-size: inherit;
  line-height: 1;
}

.playing-card .card-corner.top-left {
  top: 2px;
  left: 2px;
}

.playing-card .card-corner.bottom-right {
  bottom: 2px;
  right: 2px;
  transform: rotate(180deg);
}

/* Center symbol */
.playing-card .card-center {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: calc(1em * 1.5);
}

.playing-card .center-suit {
  font-size: inherit;
}

/* Rank and suit styling */
.playing-card .rank {
  font-size: inherit;
  line-height: 1;
  margin-bottom: 1px;
}

.playing-card .suit {
  font-size: calc(1em * 0.8);
  line-height: 1;
}

/* Suit colors */
.playing-card.red .rank,
.playing-card.red .suit,
.playing-card.red .center-suit {
  color: #dc3545;
}

.playing-card.black .rank,
.playing-card.black .suit,
.playing-card.black .center-suit {
  color: #212529;
}

/* Interactive states */
.playing-card.playable {
  cursor: pointer;
  border-color: #28a745;
  box-shadow: 
    0 2px 8px rgba(40, 167, 69, 0.2),
    0 1px 3px rgba(0, 0, 0, 0.1);
}

.playing-card.playable:hover {
  transform: translateY(-2px) scale(1.02);
  box-shadow: 
    0 4px 12px rgba(40, 167, 69, 0.3),
    0 2px 6px rgba(0, 0, 0, 0.15);
  border-color: #20c997;
  background: linear-gradient(145deg, #f8fff9 0%, #e8f5e8 100%);
}

.playing-card.selected {
  transform: translateY(-4px) scale(1.05);
  box-shadow: 
    0 6px 16px rgba(255, 193, 7, 0.4),
    0 3px 8px rgba(0, 0, 0, 0.2);
  border-color: #ffc107;
  background: linear-gradient(145deg, #fffbf0 0%, #fff3cd 100%);
  z-index: 100;
}

.playing-card.clickable {
  cursor: pointer;
}

/* Position-specific optimizations */
.player-position.north .playing-card,
.player-position.south .playing-card {
  /* Horizontal positions get slightly wider cards */
  width: clamp(45px, 8.5vw, 75px);
}

.player-position.east .playing-card,
.player-position.west .playing-card {
  /* Vertical positions get slightly narrower cards */
  width: clamp(35px, 7vw, 60px);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .playing-card {
    width: clamp(30px, 10vw, 50px);
    height: clamp(42px, 14vw, 70px);
    font-size: clamp(8px, 2vw, 12px);
  }
  
  .playing-card .card-corner.top-left {
    top: 1px;
    left: 1px;
  }
  
  .playing-card .card-corner.bottom-right {
    bottom: 1px;
    right: 1px;
  }
}

@media (max-width: 480px) {
  .playing-card {
    width: clamp(25px, 12vw, 40px);
    height: clamp(35px, 16.8vw, 56px);
    font-size: clamp(6px, 2.5vw, 10px);
    border-radius: 4px;
  }
  
  .playing-card .card-corner.top-left {
    top: 1px;
    left: 1px;
  }
  
  .playing-card .card-corner.bottom-right {
    bottom: 1px;
    right: 1px;
  }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .playing-card {
    border-width: 0.5px;
  }
}

/* Animation for card dealing */
@keyframes dealCard {
  from {
    opacity: 0;
    transform: translateY(-20px) rotate(-10deg);
  }
  to {
    opacity: 1;
    transform: translateY(0) rotate(0deg);
  }
}

.playing-card.dealing {
  animation: dealCard 0.3s ease-out;
}

/* Focus states for accessibility */
.playing-card:focus {
  outline: 2px solid #007bff;
  outline-offset: 2px;
}

.playing-card.playable:focus {
  outline-color: #28a745;
}

/* Playable and selected indicators */
.playing-card .playable-indicator {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: inherit;
  pointer-events: none;
}

.playing-card .indicator-glow {
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border-radius: inherit;
  background: linear-gradient(45deg, #28a745, #20c997);
  opacity: 0.3;
  animation: pulse 2s infinite;
}

.playing-card .selected-indicator {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: inherit;
  pointer-events: none;
}

.playing-card .selection-border {
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border: 2px solid #ffc107;
  border-radius: inherit;
  box-shadow: 0 0 10px rgba(255, 193, 7, 0.5);
}

@keyframes pulse {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 0.6; }
}

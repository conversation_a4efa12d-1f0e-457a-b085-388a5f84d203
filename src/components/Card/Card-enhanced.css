/**
 * Enhanced Card component styles
 * Improved sizing, interactions, and visual polish
 */

/* Base card styling with dynamic sizing */
.card {
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
  border: 1px solid #dee2e6;
  border-radius: 8px;
  box-shadow: 
    0 2px 4px rgba(0, 0, 0, 0.1),
    0 1px 2px rgba(0, 0, 0, 0.06);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: default;
  user-select: none;
  font-family: 'Arial', sans-serif;
  font-weight: bold;
  box-sizing: border-box;
  
  /* Dynamic sizing based on container */
  width: clamp(40px, 8vw, 70px);
  height: clamp(56px, 11.2vw, 98px);
  aspect-ratio: 5/7;
}

/* Size variants for different positions */
.card.size-small {
  width: clamp(35px, 6vw, 50px);
  height: clamp(49px, 8.4vw, 70px);
  font-size: clamp(8px, 1.2vw, 12px);
}

.card.size-medium {
  width: clamp(45px, 7vw, 60px);
  height: clamp(63px, 9.8vw, 84px);
  font-size: clamp(10px, 1.4vw, 14px);
}

.card.size-large {
  width: clamp(55px, 9vw, 75px);
  height: clamp(77px, 12.6vw, 105px);
  font-size: clamp(12px, 1.6vw, 16px);
}

/* Card content layout */
.card-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
  padding: 2px;
  box-sizing: border-box;
}

.card-rank {
  font-size: inherit;
  line-height: 1;
  margin-bottom: 2px;
}

.card-suit {
  font-size: calc(1em * 1.2);
  line-height: 1;
}

/* Suit colors */
.card.red .card-rank,
.card.red .card-suit {
  color: #dc3545;
}

.card.black .card-rank,
.card.black .card-suit {
  color: #212529;
}

/* Interactive states */
.card.playable {
  cursor: pointer;
  border-color: #28a745;
  box-shadow: 
    0 2px 8px rgba(40, 167, 69, 0.2),
    0 1px 3px rgba(0, 0, 0, 0.1);
}

.card.playable:hover {
  transform: translateY(-2px) scale(1.02);
  box-shadow: 
    0 4px 12px rgba(40, 167, 69, 0.3),
    0 2px 6px rgba(0, 0, 0, 0.15);
  border-color: #20c997;
  background: linear-gradient(145deg, #f8fff9 0%, #e8f5e8 100%);
}

.card.selected {
  transform: translateY(-4px) scale(1.05);
  box-shadow: 
    0 6px 16px rgba(255, 193, 7, 0.4),
    0 3px 8px rgba(0, 0, 0, 0.2);
  border-color: #ffc107;
  background: linear-gradient(145deg, #fffbf0 0%, #fff3cd 100%);
  z-index: 100;
}

.card.disabled {
  opacity: 0.6;
  cursor: not-allowed;
  filter: grayscale(30%);
}

.card.disabled:hover {
  transform: none;
  box-shadow: 
    0 2px 4px rgba(0, 0, 0, 0.1),
    0 1px 2px rgba(0, 0, 0, 0.06);
}

/* Card back styling */
.card-back {
  position: relative;
  background: linear-gradient(145deg, #1a5c4a 0%, #0f4c3a 100%);
  border: 1px solid #0a3d2a;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 
    0 2px 4px rgba(0, 0, 0, 0.2),
    0 1px 2px rgba(0, 0, 0, 0.1);
  
  /* Same dynamic sizing as front */
  width: clamp(40px, 8vw, 70px);
  height: clamp(56px, 11.2vw, 98px);
  aspect-ratio: 5/7;
}

.card-back.size-small {
  width: clamp(35px, 6vw, 50px);
  height: clamp(49px, 8.4vw, 70px);
}

.card-back.size-medium {
  width: clamp(45px, 7vw, 60px);
  height: clamp(63px, 9.8vw, 84px);
}

.card-back.size-large {
  width: clamp(55px, 9vw, 75px);
  height: clamp(77px, 12.6vw, 105px);
}

.card-back-design {
  color: #ffd700;
  font-size: clamp(16px, 3vw, 24px);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* Position-specific optimizations */
.player-position.north .card,
.player-position.south .card {
  /* Horizontal positions get slightly wider cards */
  width: clamp(45px, 8.5vw, 75px);
}

.player-position.east .card,
.player-position.west .card {
  /* Vertical positions get slightly narrower cards */
  width: clamp(35px, 7vw, 60px);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .card {
    width: clamp(30px, 10vw, 50px);
    height: clamp(42px, 14vw, 70px);
    font-size: clamp(8px, 2vw, 12px);
  }
  
  .card-back {
    width: clamp(30px, 10vw, 50px);
    height: clamp(42px, 14vw, 70px);
  }
  
  .card-back-design {
    font-size: clamp(12px, 4vw, 18px);
  }
}

@media (max-width: 480px) {
  .card {
    width: clamp(25px, 12vw, 40px);
    height: clamp(35px, 16.8vw, 56px);
    font-size: clamp(6px, 2.5vw, 10px);
    border-radius: 4px;
  }
  
  .card-back {
    width: clamp(25px, 12vw, 40px);
    height: clamp(35px, 16.8vw, 56px);
    border-radius: 4px;
  }
  
  .card-back-design {
    font-size: clamp(10px, 5vw, 14px);
  }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .card {
    border-width: 0.5px;
  }
  
  .card-back {
    border-width: 0.5px;
  }
}

/* Animation for card dealing */
@keyframes dealCard {
  from {
    opacity: 0;
    transform: translateY(-20px) rotate(-10deg);
  }
  to {
    opacity: 1;
    transform: translateY(0) rotate(0deg);
  }
}

.card.dealing {
  animation: dealCard 0.3s ease-out;
}

/* Focus states for accessibility */
.card:focus {
  outline: 2px solid #007bff;
  outline-offset: 2px;
}

.card.playable:focus {
  outline-color: #28a745;
}

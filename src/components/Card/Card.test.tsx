/**
 * Card component tests
 */

import React from 'react';
import { render, screen } from '@testing-library/react';
import Card from './Card';
import { Suit, Rank } from '../../types/bridge';

describe('Card', () => {
  it('renders Hearts with red color', () => {
    const heartCard = { suit: Suit.HEARTS, rank: Rank.ACE };
    render(<Card card={heartCard} />);

    const cardElement = screen.getByTestId('card-A-H');
    expect(cardElement).toHaveClass('red');
    expect(cardElement).not.toHaveClass('black');
  });

  it('renders Diamonds with red color', () => {
    const diamondCard = { suit: Suit.DIAMONDS, rank: Rank.KING };
    render(<Card card={diamondCard} />);

    const cardElement = screen.getByTestId('card-K-D');
    expect(cardElement).toHaveClass('red');
    expect(cardElement).not.toHaveClass('black');
  });

  it('renders Spades with black color', () => {
    const spadeCard = { suit: Suit.SPADES, rank: Rank.QUEEN };
    render(<Card card={spadeCard} />);

    const cardElement = screen.getByTestId('card-Q-S');
    expect(cardElement).toHaveClass('black');
    expect(cardElement).not.toHaveClass('red');
  });

  it('renders Clubs with black color', () => {
    const clubCard = { suit: Suit.CLUBS, rank: Rank.JACK };
    render(<Card card={clubCard} />);

    const cardElement = screen.getByTestId('card-J-C');
    expect(cardElement).toHaveClass('black');
    expect(cardElement).not.toHaveClass('red');
  });

  it('displays correct suit symbols', () => {
    const cards = [
      { suit: Suit.HEARTS, rank: Rank.ACE, symbol: '♥' },
      { suit: Suit.DIAMONDS, rank: Rank.TWO, symbol: '♦' },
      { suit: Suit.SPADES, rank: Rank.THREE, symbol: '♠' },
      { suit: Suit.CLUBS, rank: Rank.FOUR, symbol: '♣' }
    ];

    cards.forEach(({ suit, rank, symbol }) => {
      const { unmount } = render(<Card card={{ suit, rank }} />);
      expect(screen.getAllByText(symbol).length).toBeGreaterThan(0);
      unmount();
    });
  });
});

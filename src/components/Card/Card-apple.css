/**
 * Apple-Inspired Card Design
 * Clean, elegant playing cards with subtle depth and smooth interactions
 */

@import '../../styles/apple-design-system.css';

/* Apple-style Playing Card */
.playing-card {
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: linear-gradient(145deg, #ffffff 0%, #fafafa 100%);
  border: 1px solid rgba(0, 0, 0, 0.06);
  border-radius: var(--radius-lg);
  box-shadow: 
    0 2px 8px rgba(0, 0, 0, 0.04),
    0 1px 3px rgba(0, 0, 0, 0.06),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  transition: all var(--transition-normal);
  cursor: default;
  user-select: none;
  font-family: var(--font-family-system);
  font-weight: var(--font-weight-semibold);
  box-sizing: border-box;
  overflow: hidden;
  
  /* Dynamic sizing with Apple-style proportions */
  width: clamp(40px, 8vw, 70px);
  height: clamp(56px, 11.2vw, 98px);
  aspect-ratio: 5/7;
}

/* Apple-style card surface effect */
.playing-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, transparent 50%, rgba(0, 0, 0, 0.02) 100%);
  border-radius: inherit;
  pointer-events: none;
  z-index: 1;
}

/* Size variants with Apple proportions */
.playing-card.small {
  width: clamp(35px, 6vw, 50px);
  height: clamp(49px, 8.4vw, 70px);
  font-size: clamp(8px, 1.2vw, 12px);
  border-radius: var(--radius-md);
}

.playing-card.medium {
  width: clamp(45px, 7vw, 60px);
  height: clamp(63px, 9.8vw, 84px);
  font-size: clamp(10px, 1.4vw, 14px);
  border-radius: var(--radius-lg);
}

.playing-card.large {
  width: clamp(55px, 9vw, 75px);
  height: clamp(77px, 12.6vw, 105px);
  font-size: clamp(12px, 1.6vw, 16px);
  border-radius: var(--radius-xl);
}

/* Card content layout */
.playing-card .card-content {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xs);
  box-sizing: border-box;
  z-index: 2;
}

/* Apple-style card corners */
.playing-card .card-corner {
  position: absolute;
  display: flex;
  flex-direction: column;
  align-items: center;
  font-size: inherit;
  line-height: 1;
  font-weight: var(--font-weight-bold);
  text-shadow: 0 0.5px 1px rgba(255, 255, 255, 0.8);
}

.playing-card .card-corner.top-left {
  top: var(--spacing-xs);
  left: var(--spacing-xs);
}

.playing-card .card-corner.bottom-right {
  bottom: var(--spacing-xs);
  right: var(--spacing-xs);
  transform: rotate(180deg);
}

/* Center symbol with Apple styling */
.playing-card .card-center {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: calc(1em * 1.8);
  font-weight: var(--font-weight-bold);
  text-shadow: 0 0.5px 1px rgba(255, 255, 255, 0.8);
}

.playing-card .center-suit {
  font-size: inherit;
  filter: drop-shadow(0 0.5px 1px rgba(0, 0, 0, 0.1));
}

/* Rank and suit styling */
.playing-card .rank {
  font-size: inherit;
  line-height: 1;
  margin-bottom: 1px;
  font-weight: var(--font-weight-bold);
  text-shadow: 0 0.5px 1px rgba(255, 255, 255, 0.8);
}

.playing-card .suit {
  font-size: calc(1em * 0.85);
  line-height: 1;
  font-weight: var(--font-weight-bold);
  filter: drop-shadow(0 0.5px 1px rgba(0, 0, 0, 0.1));
}

/* Apple-style suit colors */
.playing-card.red .rank,
.playing-card.red .suit,
.playing-card.red .center-suit {
  color: #ff3b30; /* Apple Red */
}

.playing-card.black .rank,
.playing-card.black .suit,
.playing-card.black .center-suit {
  color: #1d1d1f; /* Apple Gray 800 */
}

/* Apple-style interactive states */
.playing-card.playable {
  cursor: pointer;
  border-color: rgba(0, 122, 255, 0.3);
  box-shadow: 
    0 4px 12px rgba(0, 122, 255, 0.15),
    0 2px 6px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
}

.playing-card.playable:hover {
  transform: translateY(-3px) scale(1.03);
  box-shadow: 
    0 8px 20px rgba(0, 122, 255, 0.2),
    0 4px 12px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
  border-color: rgba(0, 122, 255, 0.4);
  background: linear-gradient(145deg, #ffffff 0%, #f8f9ff 100%);
}

.playing-card.selected {
  transform: translateY(-6px) scale(1.05);
  box-shadow: 
    0 12px 24px rgba(255, 204, 0, 0.3),
    0 6px 16px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
  border-color: rgba(255, 204, 0, 0.6);
  background: linear-gradient(145deg, #fffef7 0%, #fff9e6 100%);
  z-index: var(--z-raised);
}

.playing-card.clickable {
  cursor: pointer;
}

.playing-card.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  filter: grayscale(0.3);
}

.playing-card.disabled:hover {
  transform: none;
  box-shadow: 
    0 2px 8px rgba(0, 0, 0, 0.04),
    0 1px 3px rgba(0, 0, 0, 0.06);
}

/* Apple-style card back */
.card-back {
  position: relative;
  background: linear-gradient(145deg, #007aff 0%, #0051d5 100%);
  border: 1px solid rgba(0, 81, 213, 0.3);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 
    0 2px 8px rgba(0, 122, 255, 0.2),
    0 1px 3px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  overflow: hidden;
  
  /* Same dynamic sizing as front */
  width: clamp(40px, 8vw, 70px);
  height: clamp(56px, 11.2vw, 98px);
  aspect-ratio: 5/7;
}

.card-back::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.3) 0%, transparent 50%, rgba(0, 0, 0, 0.1) 100%);
  border-radius: inherit;
  pointer-events: none;
}

.card-back.small {
  width: clamp(35px, 6vw, 50px);
  height: clamp(49px, 8.4vw, 70px);
  border-radius: var(--radius-md);
}

.card-back.medium {
  width: clamp(45px, 7vw, 60px);
  height: clamp(63px, 9.8vw, 84px);
  border-radius: var(--radius-lg);
}

.card-back.large {
  width: clamp(55px, 9vw, 75px);
  height: clamp(77px, 12.6vw, 105px);
  border-radius: var(--radius-xl);
}

.card-back-design {
  color: rgba(255, 255, 255, 0.9);
  font-size: clamp(16px, 3vw, 24px);
  font-weight: var(--font-weight-bold);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  z-index: 1;
  position: relative;
}

/* Position-specific optimizations */
.player-position.north .playing-card,
.player-position.south .playing-card {
  width: clamp(45px, 8.5vw, 75px);
}

.player-position.east .playing-card,
.player-position.west .playing-card {
  width: clamp(35px, 7vw, 60px);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .playing-card {
    width: clamp(30px, 10vw, 50px);
    height: clamp(42px, 14vw, 70px);
    font-size: clamp(8px, 2vw, 12px);
    border-radius: var(--radius-md);
  }
  
  .card-back {
    width: clamp(30px, 10vw, 50px);
    height: clamp(42px, 14vw, 70px);
    border-radius: var(--radius-md);
  }
  
  .card-back-design {
    font-size: clamp(12px, 4vw, 18px);
  }
}

@media (max-width: 480px) {
  .playing-card {
    width: clamp(25px, 12vw, 40px);
    height: clamp(35px, 16.8vw, 56px);
    font-size: clamp(6px, 2.5vw, 10px);
    border-radius: var(--radius-sm);
  }
  
  .card-back {
    width: clamp(25px, 12vw, 40px);
    height: clamp(35px, 16.8vw, 56px);
    border-radius: var(--radius-sm);
  }
  
  .card-back-design {
    font-size: clamp(10px, 5vw, 14px);
  }
}

/* Apple-style animations */
@keyframes apple-card-deal {
  from {
    opacity: 0;
    transform: translateY(-20px) rotate(-10deg) scale(0.8);
  }
  to {
    opacity: 1;
    transform: translateY(0) rotate(0deg) scale(1);
  }
}

.playing-card.dealing {
  animation: apple-card-deal 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

/* Apple focus states for accessibility */
.playing-card:focus-visible {
  outline: 2px solid var(--apple-blue);
  outline-offset: 2px;
  border-radius: var(--radius-sm);
}

.playing-card.playable:focus-visible {
  outline-color: var(--apple-green);
}

/* Dark mode adaptations */
@media (prefers-color-scheme: dark) {
  .playing-card {
    background: linear-gradient(145deg, #2c2c2e 0%, #1c1c1e 100%);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 
      0 2px 8px rgba(0, 0, 0, 0.3),
      0 1px 3px rgba(0, 0, 0, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
  }
  
  .playing-card.black .rank,
  .playing-card.black .suit,
  .playing-card.black .center-suit {
    color: #f2f2f7;
  }
  
  .playing-card .rank,
  .playing-card .suit,
  .playing-card .center-suit {
    text-shadow: 0 0.5px 1px rgba(0, 0, 0, 0.5);
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .playing-card {
    background: white;
    border: 2px solid black;
    box-shadow: none;
  }
  
  .playing-card.red .rank,
  .playing-card.red .suit,
  .playing-card.red .center-suit {
    color: #d70015;
  }
  
  .playing-card.black .rank,
  .playing-card.black .suit,
  .playing-card.black .center-suit {
    color: black;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .playing-card {
    transition: none;
  }
  
  .playing-card.playable:hover,
  .playing-card.selected {
    transform: none;
  }
  
  .playing-card.dealing {
    animation: none;
  }
}

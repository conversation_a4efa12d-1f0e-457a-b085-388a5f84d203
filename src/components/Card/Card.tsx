/**
 * Card component for displaying individual playing cards
 * Handles card rendering, styling, and interaction states
 */

import React from 'react';
import { Card as CardType, Suit, Rank } from '../../types/bridge';
import './Card.css';

interface CardProps {
  card: CardType;
  isPlayable?: boolean;
  isSelected?: boolean;
  size?: 'small' | 'medium' | 'large';
  onClick?: () => void;
  onDoubleClick?: () => void;
  onDragStart?: (e: React.DragEvent) => void;
  onDragEnd?: (e: React.DragEvent) => void;
  draggable?: boolean;
}

const Card: React.FC<CardProps> = ({
  card,
  isPlayable = false,
  isSelected = false,
  size = 'medium',
  onClick,
  onDoubleClick,
  onDragStart,
  onDragEnd,
  draggable = false
}) => {
  console.log('Card rendering:', {
    card: `${card.rank}${card.suit}`,
    isPlayable,
    hasOnDoubleClick: !!onDoubleClick,
    hasOnDragStart: !!onDragStart,
    draggable
  });
  const getSuitSymbol = (suit: Suit): string => {
    switch (suit) {
      case Suit.SPADES:
        return '♠';
      case Suit.HEARTS:
        return '♥';
      case Suit.DIAMONDS:
        return '♦';
      case Suit.CLUBS:
        return '♣';
      default:
        return '?';
    }
  };

  const getSuitColor = (suit: Suit): 'red' | 'black' => {
    return suit === Suit.HEARTS || suit === Suit.DIAMONDS ? 'red' : 'black';
  };

  const getRankDisplay = (rank: Rank): string => {
    switch (rank) {
      case Rank.ACE:
        return 'A';
      case Rank.KING:
        return 'K';
      case Rank.QUEEN:
        return 'Q';
      case Rank.JACK:
        return 'J';
      case Rank.TEN:
        return '10';
      case Rank.NINE:
        return '9';
      case Rank.EIGHT:
        return '8';
      case Rank.SEVEN:
        return '7';
      case Rank.SIX:
        return '6';
      case Rank.FIVE:
        return '5';
      case Rank.FOUR:
        return '4';
      case Rank.THREE:
        return '3';
      case Rank.TWO:
        return '2';
      default:
        return '?';
    }
  };

  const suitSymbol = getSuitSymbol(card.suit);
  const suitColor = getSuitColor(card.suit);
  const rankDisplay = getRankDisplay(card.rank);

  const cardClasses = [
    'playing-card',
    size,
    suitColor,
    isPlayable ? 'playable' : '',
    isSelected ? 'selected' : '',
    onClick || onDoubleClick ? 'clickable' : '',
    draggable ? 'draggable' : ''
  ].filter(Boolean).join(' ');

  return (
    <div
      className={cardClasses}
      onClick={onClick}
      onDoubleClick={(e) => {
        e.preventDefault();
        e.stopPropagation();
        console.log(`Double-click detected on ${card.rank}${card.suit} - attempting to play card`);
        if (onDoubleClick) onDoubleClick();
      }}
      onDragStart={onDragStart}
      onDragEnd={onDragEnd}
      draggable={draggable && isPlayable}
      data-testid={`card-${card.rank}-${card.suit}`}
      data-card-rank={card.rank}
      data-card-suit={card.suit}
      title={isPlayable ? `${rankDisplay} of ${suitSymbol} - Click, double-click, or drag to play` : `${rankDisplay} of ${suitSymbol}`}
      role={(onClick || onDoubleClick) ? 'button' : undefined}
      tabIndex={(onClick || onDoubleClick) ? 0 : undefined}
      onKeyDown={(onClick || onDoubleClick) ? (e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          if (onDoubleClick) {
            onDoubleClick();
          } else if (onClick) {
            onClick();
          }
        }
      } : undefined}
    >
      <div className="card-content">
        {/* Top-left corner */}
        <div className="card-corner top-left">
          <div className="rank-suit">{rankDisplay}{suitSymbol}</div>
        </div>

        {/* Center symbol */}
        <div className="card-center">
          <div className="center-suit">{suitSymbol}</div>
        </div>

        {/* Bottom-right corner (rotated) */}
        <div className="card-corner bottom-right">
          <div className="rank-suit">{rankDisplay}{suitSymbol}</div>
        </div>
      </div>

      {/* Playable indicator */}
      {isPlayable && (
        <div className="playable-indicator">
          <div className="indicator-glow"></div>
        </div>
      )}

      {/* Selected indicator */}
      {isSelected && (
        <div className="selected-indicator">
          <div className="selection-border"></div>
        </div>
      )}
    </div>
  );
};

export default Card;

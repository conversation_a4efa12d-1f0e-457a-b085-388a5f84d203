/**
 * Playing card component styles
 */

.playing-card {
  position: relative;
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 
    0 2px 8px rgba(0, 0, 0, 0.2),
    0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e0e0e0;
  transition: all 0.2s ease;
  user-select: none;
  overflow: hidden;
}

/* Card sizes */
.playing-card.small {
  width: 40px;
  height: 56px;
}

.playing-card.medium {
  width: 50px;
  height: 70px;
}

.playing-card.large {
  width: 60px;
  height: 84px;
}

/* Card content */
.card-content {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Card corners */
.card-corner {
  position: absolute;
  display: flex;
  flex-direction: column;
  align-items: center;
  font-weight: bold;
  line-height: 1;
}

.card-corner.top-left {
  top: 4px;
  left: 4px;
}

.card-corner.bottom-right {
  bottom: 4px;
  right: 4px;
  transform: rotate(180deg);
}

/* Rank and suit in corners */
.card-corner .rank {
  font-size: 10px;
  margin-bottom: 1px;
}

.card-corner .suit {
  font-size: 8px;
}

/* Center symbol */
.card-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.center-suit {
  font-size: 24px;
  font-weight: bold;
}

/* Size-specific adjustments */
.playing-card.small .card-corner .rank {
  font-size: 8px;
}

.playing-card.small .card-corner .suit {
  font-size: 6px;
}

.playing-card.small .center-suit {
  font-size: 16px;
}

.playing-card.large .card-corner .rank {
  font-size: 12px;
}

.playing-card.large .card-corner .suit {
  font-size: 10px;
}

.playing-card.large .center-suit {
  font-size: 32px;
}

/* Suit colors */
.playing-card.red .card-corner,
.playing-card.red .center-suit {
  color: #dc3545;
}

.playing-card.black .card-corner,
.playing-card.black .center-suit {
  color: #212529;
}

/* Interactive states */
.playing-card.clickable {
  cursor: pointer;
}

.playing-card.clickable:hover {
  transform: translateY(-2px);
  box-shadow: 
    0 4px 12px rgba(0, 0, 0, 0.3),
    0 2px 6px rgba(0, 0, 0, 0.2);
}

.playing-card.clickable:active {
  transform: translateY(-1px);
}

/* Playable state */
.playing-card.playable {
  cursor: pointer;
  animation: playable-glow 2s ease-in-out infinite alternate;
}

@keyframes playable-glow {
  from {
    box-shadow: 
      0 2px 8px rgba(0, 0, 0, 0.2),
      0 0 0 2px rgba(74, 158, 255, 0.3);
  }
  to {
    box-shadow: 
      0 2px 8px rgba(0, 0, 0, 0.2),
      0 0 0 2px rgba(74, 158, 255, 0.6);
  }
}

.playable-indicator {
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border-radius: 10px;
  pointer-events: none;
}

.indicator-glow {
  width: 100%;
  height: 100%;
  border: 2px solid #4a9eff;
  border-radius: 10px;
  animation: glow-pulse 1.5s ease-in-out infinite;
}

@keyframes glow-pulse {
  0%, 100% {
    opacity: 0.5;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.02);
  }
}

/* Selected state */
.playing-card.selected {
  transform: translateY(-4px);
  box-shadow: 
    0 6px 16px rgba(0, 0, 0, 0.3),
    0 0 0 3px rgba(255, 215, 0, 0.6);
}

.selected-indicator {
  position: absolute;
  top: -3px;
  left: -3px;
  right: -3px;
  bottom: -3px;
  border-radius: 11px;
  pointer-events: none;
}

.selection-border {
  width: 100%;
  height: 100%;
  border: 3px solid #ffd700;
  border-radius: 11px;
  animation: selection-pulse 1s ease-in-out infinite;
}

@keyframes selection-pulse {
  0%, 100% {
    opacity: 0.8;
  }
  50% {
    opacity: 1;
  }
}

/* Card back (for hidden cards) */
.card-back {
  width: 50px;
  height: 70px;
  background: linear-gradient(135deg, #1a365d 0%, #2d5a87 100%);
  border-radius: 8px;
  border: 1px solid #4a90b8;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 
    0 2px 8px rgba(0, 0, 0, 0.2),
    0 1px 3px rgba(0, 0, 0, 0.1);
}

.card-back-design {
  font-size: 24px;
  color: #ffffff;
  opacity: 0.8;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .playing-card.medium {
    width: 45px;
    height: 63px;
  }
  
  .playing-card.large {
    width: 55px;
    height: 77px;
  }
  
  .card-back {
    width: 45px;
    height: 63px;
  }
}

@media (max-width: 480px) {
  .playing-card.small {
    width: 35px;
    height: 49px;
  }
  
  .playing-card.medium {
    width: 40px;
    height: 56px;
  }
  
  .playing-card.large {
    width: 50px;
    height: 70px;
  }
  
  .card-back {
    width: 40px;
    height: 56px;
  }
  
  .card-back-design {
    font-size: 20px;
  }
}

/* Accessibility */
.playing-card:focus {
  outline: 2px solid #4a9eff;
  outline-offset: 2px;
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .playing-card,
  .playing-card.clickable:hover,
  .playing-card.selected {
    transition: none;
    transform: none;
    animation: none;
  }
  
  .playable-indicator,
  .selected-indicator {
    animation: none;
  }
  
  .indicator-glow,
  .selection-border {
    animation: none;
    opacity: 1;
  }
}


/* Responsive Design Enhancements */
.card.small {
  width: var(--card-width-xs, 45px);
  height: var(--card-height-xs, 63px);
  font-size: clamp(8px, 1vw, 12px);
  padding: clamp(1px, 0.2vw, 4px);
}

.card.medium {
  width: var(--card-width-sm, 60px);
  height: var(--card-height-sm, 84px);
  font-size: clamp(10px, 1.2vw, 14px);
  padding: clamp(2px, 0.3vw, 6px);
}

.card.large {
  width: var(--card-width-md, 75px);
  height: var(--card-height-md, 105px);
  font-size: clamp(12px, 1.4vw, 16px);
  padding: clamp(3px, 0.4vw, 8px);
}

/* Mobile responsive adjustments */
@media (max-width: 768px) {
  .card {
    width: clamp(40px, 8vw, 55px);
    height: clamp(56px, 11.2vw, 77px);
    font-size: clamp(8px, 1.5vw, 12px);
    padding: clamp(1px, 0.2vw, 3px);
  }
  
  .card.small {
    width: clamp(35px, 7vw, 45px);
    height: clamp(49px, 9.8vw, 63px);
  }
  
  .card.large {
    width: clamp(50px, 10vw, 65px);
    height: clamp(70px, 14vw, 91px);
  }
}

@media (max-width: 480px) {
  .card {
    width: clamp(32px, 9vw, 45px);
    height: clamp(45px, 12.6vw, 63px);
    font-size: clamp(7px, 1.8vw, 10px);
    padding: 1px;
    border-radius: 3px;
  }
}

/* High DPI display adjustments */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .card {
    border-width: 0.5px;
  }
}

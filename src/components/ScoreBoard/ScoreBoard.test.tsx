/**
 * Unit tests for ScoreBoard component
 */

import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import ScoreBoard from './ScoreBoard';
import { 
  Position, 
  PlayerType, 
  GamePhase, 
  GameState,
  BidSuit
} from '../../types/bridge';

// Mock the scoring utils
jest.mock('../../lib/bridge/scoringUtils', () => ({
  calculateContractScore: jest.fn(() => 120),
  isVulnerable: jest.fn((status) => status),
}));

describe('ScoreBoard', () => {
  const mockGameState: GameState = {
    id: 'test-game',
    phase: GamePhase.PLAYING,
    dealer: Position.NORTH,
    currentPlayer: Position.SOUTH,
    players: {
      [Position.NORTH]: {
        id: 'north',
        name: 'North Player',
        position: Position.NORTH,
        type: PlayerType.AI,
        hand: []
      },
      [Position.SOUTH]: {
        id: 'south',
        name: 'You',
        position: Position.SOUTH,
        type: PlayerType.HUMAN,
        hand: []
      },
      [Position.EAST]: {
        id: 'east',
        name: 'East Player',
        position: Position.EAST,
        type: PlayerType.AI,
        hand: []
      },
      [Position.WEST]: {
        id: 'west',
        name: 'West Player',
        position: Position.WEST,
        type: PlayerType.AI,
        hand: []
      }
    },
    auction: [],
    contract: {
      level: 3,
      suit: BidSuit.NO_TRUMP,
      declarer: Position.SOUTH,
      doubled: 'none'
    },
    tricks: [
      {
        id: 1,
        cards: [],
        winner: Position.SOUTH,
        leadPlayer: Position.NORTH
      },
      {
        id: 2,
        cards: [],
        winner: Position.NORTH,
        leadPlayer: Position.SOUTH
      }
    ],
    currentTrick: [],
    dummy: null,
    vulnerabilities: { northSouth: false, eastWest: true },
    score: { northSouth: 120, eastWest: 80 },
    gameNumber: 1,
    rubberScore: { northSouth: 1, eastWest: 0 }
  };

  it('renders without crashing', () => {
    render(
      <ScoreBoard
        gameState={mockGameState}
        humanPosition={Position.SOUTH}
      />
    );
    
    expect(screen.getByTestId('score-board')).toBeInTheDocument();
  });

  it('displays score header with game number', () => {
    render(
      <ScoreBoard
        gameState={mockGameState}
        humanPosition={Position.SOUTH}
      />
    );
    
    expect(screen.getByText('Score')).toBeInTheDocument();
    expect(screen.getByText('Game 1')).toBeInTheDocument();
  });

  it('shows We vs They format', () => {
    render(
      <ScoreBoard
        gameState={mockGameState}
        humanPosition={Position.SOUTH}
      />
    );
    
    expect(screen.getByText('We')).toBeInTheDocument();
    expect(screen.getByText('They')).toBeInTheDocument();
    expect(screen.getByText('VS')).toBeInTheDocument();
  });

  it('displays partnership names correctly for North-South human', () => {
    render(
      <ScoreBoard
        gameState={mockGameState}
        humanPosition={Position.SOUTH}
      />
    );
    
    expect(screen.getByText('You & North Player')).toBeInTheDocument();
    expect(screen.getByText('East Player & West Player')).toBeInTheDocument();
  });

  it('displays partnership names correctly for East-West human', () => {
    render(
      <ScoreBoard
        gameState={mockGameState}
        humanPosition={Position.EAST}
      />
    );
    
    expect(screen.getByText('East Player & West Player')).toBeInTheDocument();
    expect(screen.getByText('North Player & You')).toBeInTheDocument();
  });

  it('shows vulnerability indicators', () => {
    render(
      <ScoreBoard
        gameState={mockGameState}
        humanPosition={Position.SOUTH}
      />
    );
    
    expect(screen.getByText('NOT VUL')).toBeInTheDocument();
    expect(screen.getByText('VUL')).toBeInTheDocument();
  });

  it('displays current scores', () => {
    render(
      <ScoreBoard
        gameState={mockGameState}
        humanPosition={Position.SOUTH}
      />
    );
    
    expect(screen.getByText('120')).toBeInTheDocument();
    expect(screen.getByText('80')).toBeInTheDocument();
  });

  it('shows contract information', () => {
    render(
      <ScoreBoard
        gameState={mockGameState}
        humanPosition={Position.SOUTH}
      />
    );
    
    expect(screen.getByText('Current Contract')).toBeInTheDocument();
    expect(screen.getByText('3NO_TRUMP')).toBeInTheDocument();
    expect(screen.getByText('by You')).toBeInTheDocument();
  });

  it('displays doubled indicator when contract is doubled', () => {
    const doubledGameState = {
      ...mockGameState,
      contract: {
        ...mockGameState.contract!,
        doubled: 'doubled' as const
      }
    };

    render(
      <ScoreBoard
        gameState={doubledGameState}
        humanPosition={Position.SOUTH}
      />
    );
    
    expect(screen.getByText('X')).toBeInTheDocument();
  });

  it('shows tricks won this hand', () => {
    render(
      <ScoreBoard
        gameState={mockGameState}
        humanPosition={Position.SOUTH}
      />
    );
    
    expect(screen.getByText('Tricks This Hand')).toBeInTheDocument();
    expect(screen.getByText('2')).toBeInTheDocument(); // We won 2 tricks
    expect(screen.getByText('0')).toBeInTheDocument(); // They won 0 tricks
  });

  it('displays contract target', () => {
    render(
      <ScoreBoard
        gameState={mockGameState}
        humanPosition={Position.SOUTH}
      />
    );
    
    expect(screen.getByText('Need 9 tricks to make contract')).toBeInTheDocument();
  });

  it('shows potential score when contract exists', () => {
    render(
      <ScoreBoard
        gameState={mockGameState}
        humanPosition={Position.SOUTH}
      />
    );
    
    expect(screen.getByText('Potential Score:')).toBeInTheDocument();
    expect(screen.getByText('+120')).toBeInTheDocument();
  });

  it('handles game state without contract', () => {
    const noContractState = {
      ...mockGameState,
      contract: null
    };

    render(
      <ScoreBoard
        gameState={noContractState}
        humanPosition={Position.SOUTH}
      />
    );
    
    expect(screen.queryByText('Current Contract')).not.toBeInTheDocument();
    expect(screen.queryByText('Potential Score:')).not.toBeInTheDocument();
  });

  it('shows rubber winner when rubber is complete', () => {
    const rubberWonState = {
      ...mockGameState,
      rubberScore: { northSouth: 2, eastWest: 0 }
    };

    render(
      <ScoreBoard
        gameState={rubberWonState}
        humanPosition={Position.SOUTH}
      />
    );
    
    expect(screen.getByText(/We Won the Rubber!/)).toBeInTheDocument();
    expect(screen.getByText(/Margin: 2 points/)).toBeInTheDocument();
  });

  it('displays previous games indicator for game 2+', () => {
    const game2State = {
      ...mockGameState,
      gameNumber: 2
    };

    render(
      <ScoreBoard
        gameState={game2State}
        humanPosition={Position.SOUTH}
      />
    );
    
    expect(screen.getByText('Previous Games')).toBeInTheDocument();
    expect(screen.getByText('Game 1 completed')).toBeInTheDocument();
  });

  it('calls onScoreUpdate when provided', () => {
    const mockOnScoreUpdate = jest.fn();
    
    render(
      <ScoreBoard
        gameState={mockGameState}
        humanPosition={Position.SOUTH}
        onScoreUpdate={mockOnScoreUpdate}
      />
    );
    
    // Component renders without calling the callback initially
    expect(mockOnScoreUpdate).not.toHaveBeenCalled();
  });

  it('handles East-West human position correctly', () => {
    render(
      <ScoreBoard
        gameState={mockGameState}
        humanPosition={Position.EAST}
      />
    );
    
    // Scores should be flipped for East-West perspective
    expect(screen.getByText('80')).toBeInTheDocument(); // We score (East-West)
    expect(screen.getByText('120')).toBeInTheDocument(); // They score (North-South)
  });
});

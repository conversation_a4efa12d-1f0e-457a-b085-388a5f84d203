/**
 * ScoreBoard component styles - Matching AuctionHistory design
 */

.score-board {
  width: 100%;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  overflow: hidden;
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', sans-serif;
}

/* Header */
.score-header {
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  padding: 12px 16px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.score-header h3 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
  color: #ecf0f1;
  text-align: center;
}

.game-number {
  font-size: 11px;
  color: #bdc3c7;
  font-weight: 500;
  text-align: center;
}

/* Main Score Display */
.score-display {
  display: flex;
  align-items: stretch;
  background: rgba(255, 255, 255, 0.02);
  border-radius: 6px;
  margin: 8px;
  overflow: hidden;
}

.score-column {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 12px;
  background: rgba(255, 255, 255, 0.03);
}

.score-column:first-child {
  border-right: 1px solid rgba(255, 255, 255, 0.1);
}

.score-divider {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 8px;
  color: #bdc3c7;
  font-size: 12px;
  font-weight: 600;
}

/* Partnership Headers */
.partnership-header {
  text-align: center;
  margin-bottom: 8px;
}

.partnership-name {
  font-size: 13px;
  font-weight: 600;
  margin-bottom: 4px;
  color: #ecf0f1;
}

.score-column.we .partnership-name {
  color: #3498db;
}

.score-column.they .partnership-name {
  color: #e74c3c;
}

.player-names {
  font-size: 10px;
  color: #bdc3c7;
  margin-bottom: 6px;
  line-height: 1.2;
}

.vulnerability-indicator {
  font-size: 9px;
  font-weight: 600;
  padding: 2px 4px;
  border-radius: 3px;
  background: rgba(255, 255, 255, 0.1);
  color: #bdc3c7;
}

.vulnerability-indicator.vulnerable {
  background: #e74c3c;
  color: white;
}

/* Scores */
.scores {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.game-score,
.rubber-score {
  text-align: center;
  padding: 6px;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.score-label {
  font-size: 10px;
  color: #bdc3c7;
  margin-bottom: 2px;
  font-weight: 500;
}

.score-value {
  font-size: 16px;
  font-weight: 600;
  color: #ecf0f1;
}

.score-value.winner {
  color: #f39c12;
  font-weight: 700;
}

@keyframes winner-glow {
  0%, 100% { 
    text-shadow: 0 0 8px #ffd700, 0 2px 4px rgba(0, 0, 0, 0.5);
  }
  50% { 
    text-shadow: 0 0 16px #ffd700, 0 2px 4px rgba(0, 0, 0, 0.5);
  }
}

/* Contract Section */
.contract-section {
  background: rgba(255, 255, 255, 0.02);
  border-radius: 6px;
  padding: 8px;
  margin: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.contract-header {
  font-size: 12px;
  font-weight: 600;
  color: #f39c12;
  margin-bottom: 6px;
  text-align: center;
}

.contract-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
}

.contract-bid {
  font-size: 14px;
  font-weight: 600;
  color: #ecf0f1;
}

.doubled-indicator {
  color: #e74c3c;
  font-weight: 600;
}

.contract-declarer {
  font-size: 10px;
  color: #bdc3c7;
}

.potential-score {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 6px;
  background: rgba(52, 152, 219, 0.1);
  border-radius: 4px;
  border: 1px solid rgba(52, 152, 219, 0.2);
}

.potential-label {
  font-size: 10px;
  color: #3498db;
  font-weight: 500;
}

.potential-value {
  font-size: 12px;
  font-weight: 600;
  color: #3498db;
}

/* Rubber Status */
.rubber-status {
  background: linear-gradient(135deg, #ffd700, #ffed4e);
  color: #333;
  padding: 16px;
  border-radius: 8px;
  text-align: center;
  margin-bottom: 16px;
  box-shadow: 0 4px 12px rgba(255, 215, 0, 0.4);
  animation: celebration 1s ease-out;
}

@keyframes celebration {
  0% {
    transform: scale(0.9);
    opacity: 0;
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.rubber-winner {
  font-size: 18px;
  font-weight: 700;
  margin-bottom: 8px;
}

.final-margin {
  font-size: 14px;
  font-weight: 500;
}

/* Tricks Section */
.tricks-section {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.tricks-header {
  font-size: 16px;
  font-weight: 600;
  color: #ffd700;
  margin-bottom: 12px;
  text-align: center;
}

.tricks-display {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.tricks-we,
.tricks-they {
  text-align: center;
  flex: 1;
}

.tricks-label {
  font-size: 12px;
  color: #cccccc;
  margin-bottom: 4px;
}

.tricks-count {
  font-size: 28px;
  font-weight: 700;
  color: white;
}

.tricks-divider {
  font-size: 20px;
  color: #666;
  margin: 0 16px;
}

.tricks-needed {
  text-align: center;
  padding: 8px;
  background: rgba(74, 158, 255, 0.1);
  border-radius: 6px;
  border: 1px solid rgba(74, 158, 255, 0.3);
}

.contract-target {
  font-size: 14px;
  color: #4a9eff;
  font-weight: 500;
}

/* Score History */
.score-history {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  padding: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.history-header {
  font-size: 14px;
  font-weight: 600;
  color: #ffd700;
  margin-bottom: 8px;
  text-align: center;
}

.history-note {
  font-size: 12px;
  color: #cccccc;
  text-align: center;
  font-style: italic;
}

/* Responsive Design */
@media (max-width: 768px) {
  .score-board {
    min-width: 280px;
    max-width: 320px;
    padding: 16px;
  }
  
  .score-display {
    flex-direction: column;
    gap: 16px;
  }
  
  .score-divider {
    margin: 0;
    transform: rotate(90deg);
  }
  
  .contract-details {
    flex-direction: column;
    gap: 8px;
    text-align: center;
  }
  
  .potential-score {
    flex-direction: column;
    gap: 4px;
    text-align: center;
  }
}

/* Accessibility */
.score-board button:focus,
.score-board [tabindex]:focus {
  outline: 2px solid #4a9eff;
  outline-offset: 2px;
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .vulnerability-indicator.vulnerable,
  .score-value.winner,
  .rubber-status {
    animation: none;
  }
}


/* Enhanced Responsive Design */
@media (max-width: 1200px) {
  .score-board {
    min-width: 260px;
    max-width: 320px;
    padding: 14px;
  }
}

@media (max-width: 1024px) {
  .score-board {
    min-width: 240px;
    max-width: 280px;
    padding: 12px;
    font-size: 14px;
  }
  
  .score-header h3 {
    font-size: 20px;
  }
  
  .score-value {
    font-size: 20px;
  }
}

@media (max-width: 768px) {
  .score-board {
    min-width: 200px;
    max-width: 240px;
    padding: 10px;
    font-size: 12px;
  }
  
  .score-header h3 {
    font-size: 18px;
  }
  
  .score-display {
    flex-direction: column;
    gap: 12px;
  }
  
  .score-divider {
    margin: 0;
    transform: rotate(90deg);
  }
  
  .vs-indicator {
    padding: 6px 10px;
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .score-board {
    min-width: 180px;
    max-width: 200px;
    padding: 8px;
    font-size: 11px;
  }
  
  .score-header h3 {
    font-size: 16px;
  }
  
  .partnership-name {
    font-size: 14px;
  }
  
  .player-names {
    font-size: 10px;
  }
  
  .score-value {
    font-size: 18px;
  }
  
  .contract-bid {
    font-size: 16px;
  }
}

/* Landscape mobile adjustments */
@media (max-width: 768px) and (orientation: landscape) and (max-height: 500px) {
  .score-board {
    max-height: 300px;
    overflow-y: auto;
  }
  
  .score-display {
    flex-direction: row;
    gap: 8px;
  }
  
  .score-divider {
    transform: none;
  }
}

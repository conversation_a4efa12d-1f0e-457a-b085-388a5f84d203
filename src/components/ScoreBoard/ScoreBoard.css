/**
 * ScoreBoard component styles
 */

.score-board {
  background: rgba(0, 0, 0, 0.9);
  border-radius: 12px;
  padding: 20px;
  color: white;
  min-width: 320px;
  max-width: 400px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Header */
.score-header {
  text-align: center;
  margin-bottom: 20px;
  border-bottom: 2px solid rgba(255, 215, 0, 0.3);
  padding-bottom: 12px;
}

.score-header h3 {
  margin: 0 0 8px 0;
  font-size: 22px;
  font-weight: 700;
  color: #ffd700;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.game-number {
  font-size: 14px;
  color: #cccccc;
  font-weight: 500;
}

/* Main Score Display */
.score-display {
  display: flex;
  align-items: stretch;
  margin-bottom: 20px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  padding: 16px;
}

.score-column {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.score-divider {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 16px;
}

.vs-indicator {
  background: linear-gradient(135deg, #4a9eff, #0066cc);
  color: white;
  padding: 8px 12px;
  border-radius: 50%;
  font-weight: 700;
  font-size: 14px;
  box-shadow: 0 4px 8px rgba(74, 158, 255, 0.3);
}

/* Partnership Headers */
.partnership-header {
  text-align: center;
  margin-bottom: 8px;
}

.partnership-name {
  font-size: 18px;
  font-weight: 700;
  margin-bottom: 4px;
}

.score-column.we .partnership-name {
  color: #4a9eff;
}

.score-column.they .partnership-name {
  color: #ff6b6b;
}

.player-names {
  font-size: 12px;
  color: #cccccc;
  margin-bottom: 6px;
  line-height: 1.3;
}

.vulnerability-indicator {
  font-size: 10px;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.1);
  color: #cccccc;
}

.vulnerability-indicator.vulnerable {
  background: #dc3545;
  color: white;
  animation: pulse-vulnerable 2s infinite;
}

@keyframes pulse-vulnerable {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

/* Scores */
.scores {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.game-score,
.rubber-score {
  text-align: center;
  padding: 8px;
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.score-label {
  font-size: 12px;
  color: #cccccc;
  margin-bottom: 4px;
  font-weight: 500;
}

.score-value {
  font-size: 24px;
  font-weight: 700;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.score-value.winner {
  color: #ffd700;
  animation: winner-glow 2s infinite;
}

@keyframes winner-glow {
  0%, 100% { 
    text-shadow: 0 0 8px #ffd700, 0 2px 4px rgba(0, 0, 0, 0.5);
  }
  50% { 
    text-shadow: 0 0 16px #ffd700, 0 2px 4px rgba(0, 0, 0, 0.5);
  }
}

/* Contract Section */
.contract-section {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.contract-header {
  font-size: 16px;
  font-weight: 600;
  color: #ffd700;
  margin-bottom: 12px;
  text-align: center;
}

.contract-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.contract-bid {
  font-size: 20px;
  font-weight: 700;
  color: white;
}

.doubled-indicator {
  color: #ff6b6b;
  font-weight: 700;
}

.contract-declarer {
  font-size: 14px;
  color: #cccccc;
}

.potential-score {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: rgba(74, 158, 255, 0.1);
  border-radius: 6px;
  border: 1px solid rgba(74, 158, 255, 0.3);
}

.potential-label {
  font-size: 14px;
  color: #4a9eff;
  font-weight: 500;
}

.potential-value {
  font-size: 16px;
  font-weight: 700;
  color: #4a9eff;
}

/* Rubber Status */
.rubber-status {
  background: linear-gradient(135deg, #ffd700, #ffed4e);
  color: #333;
  padding: 16px;
  border-radius: 8px;
  text-align: center;
  margin-bottom: 16px;
  box-shadow: 0 4px 12px rgba(255, 215, 0, 0.4);
  animation: celebration 1s ease-out;
}

@keyframes celebration {
  0% {
    transform: scale(0.9);
    opacity: 0;
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.rubber-winner {
  font-size: 18px;
  font-weight: 700;
  margin-bottom: 8px;
}

.final-margin {
  font-size: 14px;
  font-weight: 500;
}

/* Tricks Section */
.tricks-section {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.tricks-header {
  font-size: 16px;
  font-weight: 600;
  color: #ffd700;
  margin-bottom: 12px;
  text-align: center;
}

.tricks-display {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.tricks-we,
.tricks-they {
  text-align: center;
  flex: 1;
}

.tricks-label {
  font-size: 12px;
  color: #cccccc;
  margin-bottom: 4px;
}

.tricks-count {
  font-size: 28px;
  font-weight: 700;
  color: white;
}

.tricks-divider {
  font-size: 20px;
  color: #666;
  margin: 0 16px;
}

.tricks-needed {
  text-align: center;
  padding: 8px;
  background: rgba(74, 158, 255, 0.1);
  border-radius: 6px;
  border: 1px solid rgba(74, 158, 255, 0.3);
}

.contract-target {
  font-size: 14px;
  color: #4a9eff;
  font-weight: 500;
}

/* Score History */
.score-history {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  padding: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.history-header {
  font-size: 14px;
  font-weight: 600;
  color: #ffd700;
  margin-bottom: 8px;
  text-align: center;
}

.history-note {
  font-size: 12px;
  color: #cccccc;
  text-align: center;
  font-style: italic;
}

/* Responsive Design */
@media (max-width: 768px) {
  .score-board {
    min-width: 280px;
    max-width: 320px;
    padding: 16px;
  }
  
  .score-display {
    flex-direction: column;
    gap: 16px;
  }
  
  .score-divider {
    margin: 0;
    transform: rotate(90deg);
  }
  
  .contract-details {
    flex-direction: column;
    gap: 8px;
    text-align: center;
  }
  
  .potential-score {
    flex-direction: column;
    gap: 4px;
    text-align: center;
  }
}

/* Accessibility */
.score-board button:focus,
.score-board [tabindex]:focus {
  outline: 2px solid #4a9eff;
  outline-offset: 2px;
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .vulnerability-indicator.vulnerable,
  .score-value.winner,
  .rubber-status {
    animation: none;
  }
}


/* Enhanced Responsive Design */
@media (max-width: 1200px) {
  .score-board {
    min-width: 260px;
    max-width: 320px;
    padding: 14px;
  }
}

@media (max-width: 1024px) {
  .score-board {
    min-width: 240px;
    max-width: 280px;
    padding: 12px;
    font-size: 14px;
  }
  
  .score-header h3 {
    font-size: 20px;
  }
  
  .score-value {
    font-size: 20px;
  }
}

@media (max-width: 768px) {
  .score-board {
    min-width: 200px;
    max-width: 240px;
    padding: 10px;
    font-size: 12px;
  }
  
  .score-header h3 {
    font-size: 18px;
  }
  
  .score-display {
    flex-direction: column;
    gap: 12px;
  }
  
  .score-divider {
    margin: 0;
    transform: rotate(90deg);
  }
  
  .vs-indicator {
    padding: 6px 10px;
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .score-board {
    min-width: 180px;
    max-width: 200px;
    padding: 8px;
    font-size: 11px;
  }
  
  .score-header h3 {
    font-size: 16px;
  }
  
  .partnership-name {
    font-size: 14px;
  }
  
  .player-names {
    font-size: 10px;
  }
  
  .score-value {
    font-size: 18px;
  }
  
  .contract-bid {
    font-size: 16px;
  }
}

/* Landscape mobile adjustments */
@media (max-width: 768px) and (orientation: landscape) and (max-height: 500px) {
  .score-board {
    max-height: 300px;
    overflow-y: auto;
  }
  
  .score-display {
    flex-direction: row;
    gap: 8px;
  }
  
  .score-divider {
    transform: none;
  }
}

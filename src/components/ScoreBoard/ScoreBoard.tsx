/**
 * ScoreBoard component for displaying bridge scores in "We vs They" format
 * Shows current game scores, rubber progress, vulnerability, and contract details
 */

import React from 'react';
import { GameState, Position, PlayerType } from '../../types/bridge';
import { calculateContractScore, isVulnerable } from '../../lib/bridge/scoringUtils';
import './ScoreBoard.css';

interface ScoreBoardProps {
  gameState: GameState;
  humanPosition: Position;
  onScoreUpdate?: (newScore: any) => void;
}

const ScoreBoard: React.FC<ScoreBoardProps> = ({
  gameState,
  humanPosition,
  onScoreUpdate
}) => {
  // Determine which partnership the human is in
  const isHumanNorthSouth = humanPosition === Position.NORTH || humanPosition === Position.SOUTH;
  const weScore = isHumanNorthSouth ? gameState.score.northSouth : gameState.score.eastWest;
  const theyScore = isHumanNorthSouth ? gameState.score.eastWest : gameState.score.northSouth;
  
  const weRubberScore = isHumanNorthSouth ? gameState.rubberScore.northSouth : gameState.rubberScore.eastWest;
  const theyRubberScore = isHumanNorthSouth ? gameState.rubberScore.eastWest : gameState.rubberScore.northSouth;

  // Vulnerability status
  const weVulnerable = isHumanNorthSouth ? 
    isVulnerable(gameState.vulnerabilities.northSouth) : 
    isVulnerable(gameState.vulnerabilities.eastWest);
  const theyVulnerable = isHumanNorthSouth ? 
    isVulnerable(gameState.vulnerabilities.eastWest) : 
    isVulnerable(gameState.vulnerabilities.northSouth);

  // Get partnership names
  const getPartnershipName = (isWe: boolean): string => {
    if (isWe) {
      if (isHumanNorthSouth) {
        return `${gameState.players[humanPosition].name} & ${gameState.players[humanPosition === Position.NORTH ? Position.SOUTH : Position.NORTH].name}`;
      } else {
        return `${gameState.players[humanPosition].name} & ${gameState.players[humanPosition === Position.EAST ? Position.WEST : Position.EAST].name}`;
      }
    } else {
      if (isHumanNorthSouth) {
        return `${gameState.players[Position.EAST].name} & ${gameState.players[Position.WEST].name}`;
      } else {
        return `${gameState.players[Position.NORTH].name} & ${gameState.players[Position.SOUTH].name}`;
      }
    }
  };

  // Calculate potential score for current contract
  const getPotentialScore = (): number | null => {
    if (!gameState.contract) return null;
    
    try {
      return calculateContractScore(
        gameState.contract,
        gameState.tricks.length,
        weVulnerable
      );
    } catch (error) {
      return null;
    }
  };

  const potentialScore = getPotentialScore();

  // Determine if a partnership has won the rubber
  const hasWonRubber = (score: number): boolean => {
    return score >= 100; // Standard rubber bridge scoring
  };

  const weWonRubber = hasWonRubber(weRubberScore);
  const theyWonRubber = hasWonRubber(theyRubberScore);
  const rubberComplete = weWonRubber || theyWonRubber;

  return (
    <div className="score-board" data-testid="score-board">
      <div className="score-header">
        <h3>Score</h3>
        <div className="game-number">Game {gameState.gameNumber}</div>
      </div>

      {/* Main Score Display */}
      <div className="score-display">
        <div className="score-column we">
          <div className="partnership-header">
            <div className="partnership-name">We</div>
            <div className="player-names">{getPartnershipName(true)}</div>
            {weVulnerable && <div className="vulnerability-indicator vulnerable">VUL</div>}
            {!weVulnerable && <div className="vulnerability-indicator">NOT VUL</div>}
          </div>
          
          <div className="scores">
            <div className="game-score">
              <div className="score-label">Game</div>
              <div className={`score-value ${weWonRubber ? 'winner' : ''}`}>
                {weScore}
              </div>
            </div>
            
            <div className="rubber-score">
              <div className="score-label">Rubber</div>
              <div className={`score-value ${weWonRubber ? 'winner' : ''}`}>
                {weRubberScore}
              </div>
            </div>
          </div>
        </div>

        <div className="score-divider">
          <div className="vs-indicator">VS</div>
        </div>

        <div className="score-column they">
          <div className="partnership-header">
            <div className="partnership-name">They</div>
            <div className="player-names">{getPartnershipName(false)}</div>
            {theyVulnerable && <div className="vulnerability-indicator vulnerable">VUL</div>}
            {!theyVulnerable && <div className="vulnerability-indicator">NOT VUL</div>}
          </div>
          
          <div className="scores">
            <div className="game-score">
              <div className="score-label">Game</div>
              <div className={`score-value ${theyWonRubber ? 'winner' : ''}`}>
                {theyScore}
              </div>
            </div>
            
            <div className="rubber-score">
              <div className="score-label">Rubber</div>
              <div className={`score-value ${theyWonRubber ? 'winner' : ''}`}>
                {theyRubberScore}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Contract Information */}
      {gameState.contract && (
        <div className="contract-section">
          <div className="contract-header">Current Contract</div>
          <div className="contract-details">
            <div className="contract-bid">
              {gameState.contract.level}{gameState.contract.suit}
              {gameState.contract.doubled !== 'none' && (
                <span className="doubled-indicator">
                  {gameState.contract.doubled === 'doubled' ? ' X' : ' XX'}
                </span>
              )}
            </div>
            <div className="contract-declarer">
              by {gameState.players[gameState.contract.declarer].name}
            </div>
          </div>
          
          {potentialScore !== null && (
            <div className="potential-score">
              <div className="potential-label">Potential Score:</div>
              <div className="potential-value">
                {potentialScore > 0 ? '+' : ''}{potentialScore}
              </div>
            </div>
          )}
        </div>
      )}

      {/* Rubber Status */}
      {rubberComplete && (
        <div className="rubber-status">
          <div className="rubber-winner">
            🏆 {weWonRubber ? 'We' : 'They'} Won the Rubber!
          </div>
          <div className="final-margin">
            Margin: {Math.abs(weRubberScore - theyRubberScore)} points
          </div>
        </div>
      )}

      {/* Tricks Won This Hand */}
      <div className="tricks-section">
        <div className="tricks-header">Tricks This Hand</div>
        <div className="tricks-display">
          <div className="tricks-we">
            <div className="tricks-label">We</div>
            <div className="tricks-count">
              {gameState.tricks.filter(trick => {
                const winnerIsWe = isHumanNorthSouth ? 
                  (trick.winner === Position.NORTH || trick.winner === Position.SOUTH) :
                  (trick.winner === Position.EAST || trick.winner === Position.WEST);
                return winnerIsWe;
              }).length}
            </div>
          </div>
          
          <div className="tricks-divider">-</div>
          
          <div className="tricks-they">
            <div className="tricks-label">They</div>
            <div className="tricks-count">
              {gameState.tricks.filter(trick => {
                const winnerIsThey = isHumanNorthSouth ? 
                  (trick.winner === Position.EAST || trick.winner === Position.WEST) :
                  (trick.winner === Position.NORTH || trick.winner === Position.SOUTH);
                return winnerIsThey;
              }).length}
            </div>
          </div>
        </div>
        
        <div className="tricks-needed">
          {gameState.contract && (
            <div className="contract-target">
              Need {6 + gameState.contract.level} tricks to make contract
            </div>
          )}
        </div>
      </div>

      {/* Score History (if available) */}
      {gameState.gameNumber > 1 && (
        <div className="score-history">
          <div className="history-header">Previous Games</div>
          <div className="history-note">
            Game {gameState.gameNumber - 1} completed
          </div>
        </div>
      )}
    </div>
  );
};

export default ScoreBoard;

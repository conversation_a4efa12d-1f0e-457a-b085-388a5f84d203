/**
 * Main App component for the Bridge Game
 */

import React, { useState } from 'react';
import GameTable from './components/GameTable/GameTable';
import MultiplayerGameManager from './components/MultiplayerGameManager/MultiplayerGameManager';
import { Position } from './types/bridge';
import './App.css';
import './styles/responsive.css';

const App: React.FC = () => {
  const [gameMode, setGameMode] = useState<'single' | 'multiplayer'>('single');
  const [playerName, setPlayerName] = useState('Player');
  const [gameId, setGameId] = useState('');

  const handleStartMultiplayer = () => {
    const newGameId = `game-${Date.now()}`;
    setGameId(newGameId);
    setGameMode('multiplayer');
  };

  const handleBackToSingle = () => {
    setGameMode('single');
    setGameId('');
  };

  return (
    <div className="app">
      <header className="app-header">
        <h1>Bridge Game</h1>
        {gameMode === 'single' && (
          <div className="game-mode-controls">
            <button onClick={handleStartMultiplayer} className="multiplayer-btn">
              🌐 Start Multiplayer Game
            </button>
          </div>
        )}
        {gameMode === 'multiplayer' && (
          <div className="game-mode-controls">
            <button onClick={handleBackToSingle} className="single-btn">
              🤖 Back to Single Player
            </button>
            <span className="game-id">Game ID: {gameId}</span>
          </div>
        )}
      </header>
      <main className="app-main">
        {gameMode === 'single' ? (
          <GameTable humanPosition={Position.SOUTH} />
        ) : (
          <MultiplayerGameManager
            gameId={gameId}
            playerId={`player-${Date.now()}`}
            playerName={playerName}
            playerPosition={Position.SOUTH}
          />
        )}
      </main>
    </div>
  );
};

export default App;

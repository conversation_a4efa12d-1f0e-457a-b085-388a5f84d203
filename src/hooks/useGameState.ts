/**
 * React hook for managing Bridge game state
 * Provides game state management and validation logic
 */

import { useState, useCallback } from 'react';
import { GameState, Position, Card, Bid, GamePhase, Player, PlayerType } from '../types/bridge';

export interface UseGameStateReturn {
  gameState: GameState;
  updateGameState: (newState: GameState) => void;
  makeBid: (bid: Bid, position?: Position) => void;
  playCard: (card: Card, position?: Position) => void;
  canMakeBid: (bid: Bid) => boolean;
  canPlayCard: (card: Card) => boolean;
  getValidCards: () => Card[];
}

export const useGameState = (gameId: string, playerId: string): UseGameStateReturn => {
  // Initialize default game state
  const [gameState, setGameState] = useState<GameState>({
    id: gameId,
    phase: GamePhase.WAITING_FOR_PLAYERS,
    dealer: Position.NORTH,
    currentPlayer: Position.NORTH,
    players: {
      [Position.NORTH]: {
        id: '',
        name: '',
        position: Position.NORTH,
        type: PlayerType.AI,
        hand: [],
        isConnected: false
      },
      [Position.SOUTH]: {
        id: '',
        name: '',
        position: Position.SOUTH,
        type: PlayerType.AI,
        hand: [],
        isConnected: false
      },
      [Position.EAST]: {
        id: '',
        name: '',
        position: Position.EAST,
        type: PlayerType.AI,
        hand: [],
        isConnected: false
      },
      [Position.WEST]: {
        id: '',
        name: '',
        position: Position.WEST,
        type: PlayerType.AI,
        hand: [],
        isConnected: false
      }
    },
    vulnerabilities: { northSouth: false, eastWest: false },
    auction: [],
    contract: null,
    tricks: [],
    currentTrick: [],
    dummy: null,
    score: { northSouth: 0, eastWest: 0 },
    rubberScore: { northSouth: 0, eastWest: 0 },
    gameNumber: 1
  });

  // Update game state
  const updateGameState = useCallback((newState: GameState) => {
    setGameState(newState);
  }, []);

  // Make a bid
  const makeBid = useCallback((bid: Bid, position?: Position) => {
    // This is a simplified implementation
    // In a real game, this would validate the bid and update the auction
    console.log('Bid made:', bid, 'by', position);
  }, []);

  // Play a card
  const playCard = useCallback((card: Card, position?: Position) => {
    // This is a simplified implementation
    // In a real game, this would validate the card play and update the trick
    console.log('Card played:', card, 'by', position);
  }, []);

  // Check if a bid is valid
  const canMakeBid = useCallback((bid: Bid): boolean => {
    // Simplified validation - always allow for now
    return gameState.phase === GamePhase.BIDDING;
  }, [gameState.phase]);

  // Check if a card can be played
  const canPlayCard = useCallback((card: Card): boolean => {
    // Simplified validation - always allow for now
    return gameState.phase === GamePhase.PLAYING;
  }, [gameState.phase]);

  // Get valid cards that can be played
  const getValidCards = useCallback((): Card[] => {
    // Simplified implementation - return all cards in hand
    const playerPosition = Object.values(gameState.players)
      .find(player => player.id === playerId)?.position;

    if (playerPosition && gameState.players[playerPosition]) {
      return gameState.players[playerPosition].hand;
    }

    return [];
  }, [gameState.players, playerId]);

  return {
    gameState,
    updateGameState,
    makeBid,
    playCard,
    canMakeBid,
    canPlayCard,
    getValidCards
  };
};

export default useGameState;

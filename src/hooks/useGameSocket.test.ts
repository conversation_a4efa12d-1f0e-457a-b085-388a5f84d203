/**
 * Unit tests for useGameSocket hook
 */

import { renderHook, act } from '@testing-library/react';
import { useGameSocket, UseGameSocketOptions } from './useGameSocket';
import { Suit, Rank, Position } from '../types/bridge';

// Mock GameSocket
const mockGameSocket = {
  connect: jest.fn(),
  disconnect: jest.fn(),
  isConnected: jest.fn(),
  sendBid: jest.fn(),
  sendCardPlay: jest.fn(),
  requestGameState: jest.fn()
};

jest.mock('../lib/websocket/gameSocket', () => {
  return {
    __esModule: true,
    default: jest.fn().mockImplementation(() => mockGameSocket)
  };
});

describe('useGameSocket', () => {
  let defaultOptions: UseGameSocketOptions;

  beforeEach(() => {
    defaultOptions = {
      gameId: 'test-game-123',
      playerId: 'player-456',
      playerName: 'Test Player',
      autoConnect: false
    };

    jest.clearAllMocks();
    mockGameSocket.isConnected.mockReturnValue(false);
  });

  it('should initialize with correct default state', () => {
    const { result } = renderHook(() => useGameSocket(defaultOptions));

    expect(result.current.state).toEqual({
      isConnected: false,
      isConnecting: false,
      error: null,
      lastMessage: null,
      connectionAttempts: 0
    });
  });

  it('should create GameSocket instance with correct config', () => {
    const GameSocketMock = require('../lib/websocket/gameSocket').default;
    
    renderHook(() => useGameSocket(defaultOptions));

    expect(GameSocketMock).toHaveBeenCalledWith(
      expect.objectContaining({
        url: expect.any(String),
        gameId: 'test-game-123',
        playerId: 'player-456',
        playerName: 'Test Player'
      }),
      expect.any(Object)
    );
  });

  it('should connect when connect method is called', async () => {
    mockGameSocket.connect.mockResolvedValue(undefined);
    
    const { result } = renderHook(() => useGameSocket(defaultOptions));

    await act(async () => {
      await result.current.connect();
    });

    expect(mockGameSocket.connect).toHaveBeenCalled();
  });

  it('should update state when connecting', async () => {
    mockGameSocket.connect.mockImplementation(() => {
      return new Promise(resolve => {
        setTimeout(resolve, 100);
      });
    });

    const { result } = renderHook(() => useGameSocket(defaultOptions));

    act(() => {
      result.current.connect();
    });

    expect(result.current.state.isConnecting).toBe(true);
    expect(result.current.state.connectionAttempts).toBe(1);
  });

  it('should handle connection errors', async () => {
    const error = new Error('Connection failed');
    mockGameSocket.connect.mockRejectedValue(error);

    const { result } = renderHook(() => useGameSocket(defaultOptions));

    await act(async () => {
      try {
        await result.current.connect();
      } catch (e) {
        // Expected to throw
      }
    });

    expect(result.current.state.error).toEqual(error);
    expect(result.current.state.isConnecting).toBe(false);
  });

  it('should disconnect when disconnect method is called', () => {
    const { result } = renderHook(() => useGameSocket(defaultOptions));

    act(() => {
      result.current.disconnect();
    });

    expect(mockGameSocket.disconnect).toHaveBeenCalled();
  });

  it('should send bid when connected', () => {
    mockGameSocket.isConnected.mockReturnValue(true);
    
    const { result } = renderHook(() => useGameSocket(defaultOptions));

    const mockBid = { level: 1, suit: 'HEARTS', isPass: false, isDouble: false, isRedouble: false };

    act(() => {
      result.current.sendBid(mockBid);
    });

    expect(mockGameSocket.sendBid).toHaveBeenCalledWith(mockBid);
  });

  it('should not send bid when disconnected', () => {
    mockGameSocket.isConnected.mockReturnValue(false);
    
    const { result } = renderHook(() => useGameSocket(defaultOptions));

    const mockBid = { level: 1, suit: 'HEARTS', isPass: false, isDouble: false, isRedouble: false };

    act(() => {
      result.current.sendBid(mockBid);
    });

    expect(mockGameSocket.sendBid).not.toHaveBeenCalled();
  });

  it('should send card play when connected', () => {
    mockGameSocket.isConnected.mockReturnValue(true);
    
    const { result } = renderHook(() => useGameSocket(defaultOptions));

    const mockCard = { suit: Suit.SPADES, rank: Rank.ACE };

    act(() => {
      result.current.sendCardPlay(mockCard);
    });

    expect(mockGameSocket.sendCardPlay).toHaveBeenCalledWith(mockCard);
  });

  it('should request game state when connected', () => {
    mockGameSocket.isConnected.mockReturnValue(true);
    
    const { result } = renderHook(() => useGameSocket(defaultOptions));

    act(() => {
      result.current.requestGameState();
    });

    expect(mockGameSocket.requestGameState).toHaveBeenCalled();
  });

  it('should call onGameStateUpdate callback', () => {
    const onGameStateUpdate = jest.fn();
    const options = { ...defaultOptions, onGameStateUpdate };

    const { result } = renderHook(() => useGameSocket(options));

    // Simulate callback being called by GameSocket
    const GameSocketMock = require('../lib/websocket/gameSocket').default;
    const lastCall = GameSocketMock.mock.calls[GameSocketMock.mock.calls.length - 1];
    const callbacks = lastCall[1];

    const mockGameState = {
      gameId: 'test-game-123',
      phase: 'bidding',
      currentPlayer: Position.NORTH
    };

    act(() => {
      callbacks.onGameStateUpdate(mockGameState);
    });

    expect(onGameStateUpdate).toHaveBeenCalledWith(mockGameState);
    expect(result.current.state.lastMessage).toEqual({
      type: 'gameStateUpdate',
      data: mockGameState
    });
  });

  it('should call onPlayerJoined callback', () => {
    const onPlayerJoined = jest.fn();
    const options = { ...defaultOptions, onPlayerJoined };

    renderHook(() => useGameSocket(options));

    const GameSocketMock = require('../lib/websocket/gameSocket').default;
    const lastCall = GameSocketMock.mock.calls[GameSocketMock.mock.calls.length - 1];
    const callbacks = lastCall[1];

    act(() => {
      callbacks.onPlayerJoined('player-789', 'New Player', Position.SOUTH);
    });

    expect(onPlayerJoined).toHaveBeenCalledWith('player-789', 'New Player', Position.SOUTH);
  });

  it('should call onBidMade callback', () => {
    const onBidMade = jest.fn();
    const options = { ...defaultOptions, onBidMade };

    renderHook(() => useGameSocket(options));

    const GameSocketMock = require('../lib/websocket/gameSocket').default;
    const lastCall = GameSocketMock.mock.calls[GameSocketMock.mock.calls.length - 1];
    const callbacks = lastCall[1];

    const mockBid = { level: 2, suit: 'CLUBS', isPass: false, isDouble: false, isRedouble: false };

    act(() => {
      callbacks.onBidMade(Position.EAST, mockBid);
    });

    expect(onBidMade).toHaveBeenCalledWith(Position.EAST, mockBid);
  });

  it('should call onCardPlayed callback', () => {
    const onCardPlayed = jest.fn();
    const options = { ...defaultOptions, onCardPlayed };

    renderHook(() => useGameSocket(options));

    const GameSocketMock = require('../lib/websocket/gameSocket').default;
    const lastCall = GameSocketMock.mock.calls[GameSocketMock.mock.calls.length - 1];
    const callbacks = lastCall[1];

    const mockCard = { suit: Suit.DIAMONDS, rank: Rank.QUEEN };

    act(() => {
      callbacks.onCardPlayed(Position.WEST, mockCard);
    });

    expect(onCardPlayed).toHaveBeenCalledWith(Position.WEST, mockCard);
  });

  it('should handle error callbacks', () => {
    const onError = jest.fn();
    const options = { ...defaultOptions, onError };

    renderHook(() => useGameSocket(options));

    const GameSocketMock = require('../lib/websocket/gameSocket').default;
    const lastCall = GameSocketMock.mock.calls[GameSocketMock.mock.calls.length - 1];
    const callbacks = lastCall[1];

    const error = new Error('Test error');

    act(() => {
      callbacks.onError(error);
    });

    expect(onError).toHaveBeenCalledWith(error);
  });

  it('should auto-connect when autoConnect is true', () => {
    const options = { ...defaultOptions, autoConnect: true };
    mockGameSocket.connect.mockResolvedValue(undefined);

    renderHook(() => useGameSocket(options));

    expect(mockGameSocket.connect).toHaveBeenCalled();
  });

  it('should not auto-connect when autoConnect is false', () => {
    const options = { ...defaultOptions, autoConnect: false };

    renderHook(() => useGameSocket(options));

    expect(mockGameSocket.connect).not.toHaveBeenCalled();
  });

  it('should cleanup on unmount', () => {
    const { unmount } = renderHook(() => useGameSocket(defaultOptions));

    unmount();

    expect(mockGameSocket.disconnect).toHaveBeenCalled();
  });

  it('should provide socket instance', () => {
    const { result } = renderHook(() => useGameSocket(defaultOptions));

    expect(result.current.socket).toBe(mockGameSocket);
  });
});

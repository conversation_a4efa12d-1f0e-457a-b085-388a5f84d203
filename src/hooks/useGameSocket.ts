/**
 * React hook for managing WebSocket connection and game communication
 * Provides a clean interface for components to interact with the game socket
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import GameSocket, { GameSocketConfig, GameSocketCallbacks, GameMessageType } from '../lib/websocket/gameSocket';
import { GameState, Position, Card, Bid } from '../types/bridge';

export interface UseGameSocketOptions {
  gameId: string;
  playerId: string;
  playerName: string;
  autoConnect?: boolean;
  onGameStateUpdate?: (gameState: GameState) => void;
  onPlayerJoined?: (playerId: string, playerName: string, position: Position) => void;
  onPlayerLeft?: (playerId: string, position: Position) => void;
  onBidMade?: (position: Position, bid: Bid) => void;
  onCardPlayed?: (position: Position, card: Card) => void;
  onTrickCompleted?: (winner: Position, trick: Card[]) => void;
  onGameStarted?: () => void;
  onGameEnded?: (result: any) => void;
  onError?: (error: Error) => void;
}

export interface GameSocketState {
  isConnected: boolean;
  isConnecting: boolean;
  error: Error | null;
  lastMessage: any;
  connectionAttempts: number;
}

export interface UseGameSocketReturn {
  // Connection state
  state: GameSocketState;
  
  // Connection methods
  connect: () => Promise<void>;
  disconnect: () => void;
  
  // Game actions
  sendBid: (bid: Bid) => void;
  sendCardPlay: (card: Card) => void;
  requestGameState: () => void;
  
  // Raw socket access (for advanced usage)
  socket: GameSocket | null;
}

export const useGameSocket = (options: UseGameSocketOptions): UseGameSocketReturn => {
  const {
    gameId,
    playerId,
    playerName,
    autoConnect = true,
    onGameStateUpdate,
    onPlayerJoined,
    onPlayerLeft,
    onBidMade,
    onCardPlayed,
    onTrickCompleted,
    onGameStarted,
    onGameEnded,
    onError
  } = options;

  const [state, setState] = useState<GameSocketState>({
    isConnected: false,
    isConnecting: false,
    error: null,
    lastMessage: null,
    connectionAttempts: 0
  });

  const socketRef = useRef<GameSocket | null>(null);
  const callbacksRef = useRef<GameSocketCallbacks>({});

  // Update callbacks ref when props change
  useEffect(() => {
    callbacksRef.current = {
      onConnect: () => {
        setState(prev => ({
          ...prev,
          isConnected: true,
          isConnecting: false,
          error: null
        }));
      },

      onDisconnect: (reason: string) => {
        setState(prev => ({
          ...prev,
          isConnected: false,
          isConnecting: false,
          error: new Error(`Disconnected: ${reason}`)
        }));
      },

      onError: (error: Error) => {
        setState(prev => ({
          ...prev,
          error,
          isConnecting: false
        }));
        onError?.(error);
      },

      onGameStateUpdate: (gameState: GameState) => {
        setState(prev => ({
          ...prev,
          lastMessage: { type: 'gameStateUpdate', data: gameState }
        }));
        onGameStateUpdate?.(gameState);
      },

      onPlayerJoined: (playerId: string, playerName: string, position: Position) => {
        setState(prev => ({
          ...prev,
          lastMessage: { type: 'playerJoined', data: { playerId, playerName, position } }
        }));
        onPlayerJoined?.(playerId, playerName, position);
      },

      onPlayerLeft: (playerId: string, position: Position) => {
        setState(prev => ({
          ...prev,
          lastMessage: { type: 'playerLeft', data: { playerId, position } }
        }));
        onPlayerLeft?.(playerId, position);
      },

      onBidMade: (position: Position, bid: Bid) => {
        setState(prev => ({
          ...prev,
          lastMessage: { type: 'bidMade', data: { position, bid } }
        }));
        onBidMade?.(position, bid);
      },

      onCardPlayed: (position: Position, card: Card) => {
        setState(prev => ({
          ...prev,
          lastMessage: { type: 'cardPlayed', data: { position, card } }
        }));
        onCardPlayed?.(position, card);
      },

      onTrickCompleted: (winner: Position, trick: Card[]) => {
        setState(prev => ({
          ...prev,
          lastMessage: { type: 'trickCompleted', data: { winner, trick } }
        }));
        onTrickCompleted?.(winner, trick);
      },

      onGameStarted: () => {
        setState(prev => ({
          ...prev,
          lastMessage: { type: 'gameStarted', data: {} }
        }));
        onGameStarted?.();
      },

      onGameEnded: (result: any) => {
        setState(prev => ({
          ...prev,
          lastMessage: { type: 'gameEnded', data: result }
        }));
        onGameEnded?.(result);
      }
    };
  }, [
    onGameStateUpdate,
    onPlayerJoined,
    onPlayerLeft,
    onBidMade,
    onCardPlayed,
    onTrickCompleted,
    onGameStarted,
    onGameEnded,
    onError
  ]);

  // Initialize socket
  useEffect(() => {
    const config: GameSocketConfig = {
      url: process.env.REACT_APP_WEBSOCKET_URL || 'ws://localhost:8080/ws',
      gameId,
      playerId,
      playerName
    };

    socketRef.current = new GameSocket(config, callbacksRef.current);

    return () => {
      if (socketRef.current) {
        socketRef.current.disconnect();
        socketRef.current = null;
      }
    };
  }, [gameId, playerId, playerName]);

  // Auto-connect if enabled
  useEffect(() => {
    if (autoConnect && socketRef.current && !state.isConnected && !state.isConnecting) {
      connect();
    }
  }, [autoConnect, gameId, playerId, playerName]);

  // Connection methods
  const connect = useCallback(async (): Promise<void> => {
    if (!socketRef.current || state.isConnecting || state.isConnected) {
      return;
    }

    setState(prev => ({
      ...prev,
      isConnecting: true,
      error: null,
      connectionAttempts: prev.connectionAttempts + 1
    }));

    try {
      await socketRef.current.connect();
    } catch (error) {
      setState(prev => ({
        ...prev,
        isConnecting: false,
        error: error as Error
      }));
      throw error;
    }
  }, [state.isConnecting, state.isConnected]);

  const disconnect = useCallback((): void => {
    if (socketRef.current) {
      socketRef.current.disconnect();
    }
  }, []);

  // Game action methods
  const sendBid = useCallback((bid: Bid): void => {
    if (socketRef.current && state.isConnected) {
      socketRef.current.sendBid(bid);
    } else {
      console.warn('Cannot send bid: socket not connected');
    }
  }, [state.isConnected]);

  const sendCardPlay = useCallback((card: Card): void => {
    if (socketRef.current && state.isConnected) {
      socketRef.current.sendCardPlay(card);
    } else {
      console.warn('Cannot send card play: socket not connected');
    }
  }, [state.isConnected]);

  const requestGameState = useCallback((): void => {
    if (socketRef.current && state.isConnected) {
      socketRef.current.requestGameState();
    } else {
      console.warn('Cannot request game state: socket not connected');
    }
  }, [state.isConnected]);

  return {
    state,
    connect,
    disconnect,
    sendBid,
    sendCardPlay,
    requestGameState,
    socket: socketRef.current
  };
};

export default useGameSocket;

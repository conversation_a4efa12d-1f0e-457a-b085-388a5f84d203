<?xml version="1.0" encoding="UTF-8"?>
<svg width="500" height="120" viewBox="0 0 500 120" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Gradient for elegant text -->
    <linearGradient id="textGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#1a1a1a;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2c3e50;stop-opacity:1" />
    </linearGradient>

    <!-- Gold gradient for suits -->
    <linearGradient id="goldGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#d4af37;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#b8860b;stop-opacity:1" />
    </linearGradient>
  </defs>

  <!-- Main text: Bridge -->
  <g transform="translate(20, 75)">
    <!-- B with spade -->
    <text x="0" y="0" font-family="Georgia, serif" font-size="64" font-weight="600" fill="url(#textGradient)">B</text>
    <path d="M8 -45 C8 -50 12 -54 16 -50 C20 -46 20 -40 16 -36 C12 -32 8 -36 8 -45 Z M16 -36 L16 -20"
          fill="#2c3e50" stroke="#2c3e50" stroke-width="1"/>

    <!-- r -->
    <text x="65" y="0" font-family="Georgia, serif" font-size="64" font-weight="600" fill="url(#textGradient)">r</text>

    <!-- i with heart -->
    <text x="95" y="0" font-family="Georgia, serif" font-size="64" font-weight="600" fill="url(#textGradient)">i</text>
    <path d="M105 -50 C105 -55 110 -55 115 -50 C120 -55 125 -55 125 -50 C125 -45 115 -35 115 -35 C115 -35 105 -45 105 -50 Z"
          fill="#e74c3c"/>

    <!-- d with diamond -->
    <text x="135" y="0" font-family="Georgia, serif" font-size="64" font-weight="600" fill="url(#textGradient)">d</text>
    <path d="M155 -50 L165 -40 L155 -30 L145 -40 Z" fill="#d4af37"/>

    <!-- g -->
    <text x="195" y="0" font-family="Georgia, serif" font-size="64" font-weight="600" fill="url(#textGradient)">g</text>

    <!-- e with club -->
    <text x="255" y="0" font-family="Georgia, serif" font-size="64" font-weight="600" fill="url(#textGradient)">e</text>
    <path d="M275 -45 C275 -50 280 -50 285 -45 C290 -50 295 -50 295 -45 C295 -40 290 -40 285 -40 C290 -35 295 -35 295 -30 C295 -25 290 -25 285 -30 C280 -25 275 -25 275 -30 C275 -35 280 -35 285 -40 C280 -40 275 -40 275 -45 Z M285 -30 L285 -20"
          fill="#2c3e50" stroke="#2c3e50" stroke-width="1"/>
  </g>

  <!-- Decorative bridge arch -->
  <g transform="translate(350, 60)">
    <path d="M0 0 Q50 -30 100 0" stroke="url(#goldGradient)" stroke-width="3" fill="none"/>
    <path d="M0 5 Q50 -25 100 5" stroke="url(#goldGradient)" stroke-width="2" fill="none" opacity="0.7"/>
    <path d="M0 10 Q50 -20 100 10" stroke="url(#goldGradient)" stroke-width="1" fill="none" opacity="0.5"/>
  </g>
</svg>

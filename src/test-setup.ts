// Global test setup for Jest
// This file is run before each test file

// Extend Jest matchers if needed
// React Testing Library setup
import '@testing-library/jest-dom';
// import 'jest-extended';

// Set up global test utilities
global.console = {
  ...console,
  // Uncomment to suppress console.log during tests
  // log: jest.fn(),
  // warn: jest.fn(),
  // error: jest.fn(),
};

// Set test timeout
jest.setTimeout(10000);

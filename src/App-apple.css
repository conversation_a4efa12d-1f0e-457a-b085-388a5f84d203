/**
 * Apple-Inspired App Layout
 * Clean, minimal design with elegant depth and smooth animations
 */

@import './styles/apple-design-system.css';

/* Main App Container */
.app {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  overflow: hidden;
  font-family: var(--font-family-system);
  color: var(--text-primary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Apple-style Header */
.app-header {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: var(--blur-md);
  -webkit-backdrop-filter: var(--blur-md);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  padding: var(--spacing-lg) var(--spacing-3xl);
  z-index: var(--z-floating);
  position: relative;
}

.app-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.1) 0%, transparent 100%);
  pointer-events: none;
}

.app-header h1 {
  font-size: var(--font-size-title1);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  text-align: center;
  letter-spacing: -0.025em;
  margin: 0;
  position: relative;
  z-index: 1;
}

/* Main Content Area */
.app-main {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-3xl);
  min-height: 0;
  position: relative;
}

/* Apple-style Game Container */
.game-container {
  width: 100%;
  max-width: 1200px;
  height: 100%;
  max-height: 800px;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: var(--blur-lg);
  -webkit-backdrop-filter: var(--blur-lg);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-3xl);
  box-shadow: 
    0 20px 40px rgba(0, 0, 0, 0.1),
    0 8px 16px rgba(0, 0, 0, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  overflow: hidden;
  position: relative;
  animation: apple-scale-in 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.game-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.5), transparent);
  z-index: 1;
}

/* Apple-style Game Mode Controls */
.game-mode-controls {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-lg);
  margin-top: var(--spacing-2xl);
  padding: var(--spacing-lg);
}

.multiplayer-btn, 
.single-btn {
  font-family: var(--font-family-system);
  font-size: var(--font-size-body);
  font-weight: var(--font-weight-semibold);
  background: var(--apple-blue);
  color: var(--apple-white);
  border: none;
  padding: var(--spacing-md) var(--spacing-3xl);
  border-radius: var(--radius-full);
  cursor: pointer;
  transition: all var(--transition-fast);
  box-shadow: 
    0 4px 12px rgba(0, 122, 255, 0.3),
    0 2px 4px rgba(0, 0, 0, 0.1);
  min-height: 44px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  user-select: none;
  position: relative;
  overflow: hidden;
}

.multiplayer-btn::before,
.single-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.2) 0%, transparent 50%);
  pointer-events: none;
}

.multiplayer-btn:hover, 
.single-btn:hover {
  background: var(--apple-blue-dark);
  transform: translateY(-2px) scale(1.02);
  box-shadow: 
    0 8px 20px rgba(0, 122, 255, 0.4),
    0 4px 8px rgba(0, 0, 0, 0.15);
}

.multiplayer-btn:active,
.single-btn:active {
  transform: translateY(-1px) scale(1.01);
  transition: all 0.1s ease;
}

/* Apple-style Status and Info Panels */
.status-panel {
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: var(--blur-sm);
  -webkit-backdrop-filter: var(--blur-sm);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: var(--radius-xl);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-md);
  position: relative;
}

.status-panel::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.6), transparent);
}

/* Apple-style Loading States */
.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  gap: var(--spacing-2xl);
  color: var(--text-secondary);
}

.loading-spinner .spinner {
  width: 32px;
  height: 32px;
  border: 2px solid var(--border-primary);
  border-top: 2px solid var(--apple-blue);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: var(--font-size-body);
  font-weight: var(--font-weight-medium);
  color: var(--text-secondary);
  animation: apple-fade-in 0.5s ease-out;
}

/* Responsive Design */
@media (max-width: 768px) {
  .app-header {
    padding: var(--spacing-md) var(--spacing-xl);
  }
  
  .app-header h1 {
    font-size: var(--font-size-title2);
  }
  
  .app-main {
    padding: var(--spacing-xl);
  }
  
  .game-container {
    border-radius: var(--radius-2xl);
  }
  
  .game-mode-controls {
    flex-direction: column;
    gap: var(--spacing-md);
  }
  
  .multiplayer-btn, 
  .single-btn {
    width: 100%;
    max-width: 280px;
  }
}

@media (max-width: 480px) {
  .app-header {
    padding: var(--spacing-sm) var(--spacing-lg);
  }
  
  .app-header h1 {
    font-size: var(--font-size-title3);
  }
  
  .app-main {
    padding: var(--spacing-lg);
  }
  
  .game-container {
    border-radius: var(--radius-xl);
    box-shadow: 
      0 10px 20px rgba(0, 0, 0, 0.1),
      0 4px 8px rgba(0, 0, 0, 0.05);
  }
}

/* Dark Mode Adaptations */
@media (prefers-color-scheme: dark) {
  .app {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d30 100%);
  }
  
  .app-header {
    background: rgba(28, 28, 30, 0.8);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  .app-header h1 {
    color: var(--apple-gray-100);
  }
  
  .game-container {
    background: rgba(28, 28, 30, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 
      0 20px 40px rgba(0, 0, 0, 0.3),
      0 8px 16px rgba(0, 0, 0, 0.2);
  }
  
  .status-panel {
    background: rgba(44, 44, 46, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .app-header {
    background: var(--apple-white);
    border-bottom: 2px solid var(--apple-gray-800);
    backdrop-filter: none;
  }
  
  .game-container {
    background: var(--apple-white);
    border: 2px solid var(--apple-gray-800);
    backdrop-filter: none;
  }
  
  .multiplayer-btn, 
  .single-btn {
    background: var(--apple-gray-800);
    color: var(--apple-white);
    border: 2px solid var(--apple-gray-800);
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .game-container {
    animation: none;
  }
  
  .multiplayer-btn:hover, 
  .single-btn:hover {
    transform: none;
  }
  
  .loading-spinner .spinner {
    animation: none;
  }
}

/* Print Styles */
@media print {
  .app {
    background: white;
  }
  
  .app-header {
    background: white;
    border-bottom: 1px solid black;
    backdrop-filter: none;
  }
  
  .game-container {
    background: white;
    border: 1px solid black;
    backdrop-filter: none;
    box-shadow: none;
  }
}

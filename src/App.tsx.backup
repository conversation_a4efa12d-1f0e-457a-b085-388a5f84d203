/**
 * Main App component for the Bridge Game - REACT WORKING
 */

import React, { useState } from 'react';
import GameTable from './components/GameTable/GameTable';
import { Position } from './types/bridge';
import './App.css';
import './styles/responsive.css';

const App: React.FC = () => {
  const handleStartMultiplayer = () => {
    alert('🚧 Multiplayer mode is being updated! Please use single player mode for now. 🎮');
  };

  return (
    <div className="app">
      <header className="app-header">
        <h1>Bridge Game - REACT WORKING</h1>
        <div className="game-mode-controls">
          <button onClick={handleStartMultiplayer} className="multiplayer-btn">
            🌐 Multiplayer (Coming Soon!)
          </button>
        </div>
      </header>
      <main className="app-main">
        <GameTable />
      </main>
    </div>
  );
};

export default App;

/**
 * Main App component styles with responsive design
 */

.app {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  overflow: hidden;
  font-family: -apple-system, BlinkMacSystemFont, "SF Pro Display", "SF Pro Text", "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-size: var(--font-size-md, 16px);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  line-height: 1.5;
}

/* Responsive app adjustments */
@media (max-width: 768px) {
  .app {
    font-size: var(--font-size-sm, 14px);
  }
}

@media (max-width: 480px) {
  .app {
    font-size: var(--font-size-xs, 12px);
  }
}

.app-header {
  background-color: rgba(0, 0, 0, 0.2);
  padding: 16px 24px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  z-index: 100;
}

.app-logo {
  display: flex;
  justify-content: center;
  align-items: center;
}

.logo-image {
  height: 40px;
  width: auto;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
  transition: transform 0.2s ease;
}

.logo-image:hover {
  transform: scale(1.05);
}

.app-main {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  min-height: 0; /* Important for flex child to shrink */
}

/* Responsive design */
@media (max-width: 768px) {
  .app-header {
    padding: 12px 16px;
  }
  
  .logo-image {
    height: 32px;
  }
  
  .app-main {
    padding: 10px;
  }
}

@media (max-width: 480px) {
  .logo-image {
    height: 28px;
  }
  
  .app-main {
    padding: 5px;
  }
}

/* Dark mode support (if needed in future) */
@media (prefers-color-scheme: dark) {
  .app {
    /* Already dark by default */
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .app-header {
    border-bottom: 2px solid #ffffff;
    background-color: rgba(0, 0, 0, 0.8);
  }
  
  .app-header h1 {
    text-shadow: none;
  }
}

/* Game mode controls */
.game-mode-controls {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  margin-top: 1rem;
}

.multiplayer-btn, .single-btn {
  background: linear-gradient(135deg, #4a9eff 0%, #357abd 100%);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(74, 158, 255, 0.3);
}

.multiplayer-btn:hover, .single-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(74, 158, 255, 0.4);
}

.game-id {
  color: #4a9eff;
  font-size: 0.9rem;
  background: rgba(74, 158, 255, 0.1);
  padding: 0.5rem 1rem;
  border-radius: 6px;
  border: 1px solid rgba(74, 158, 255, 0.3);
}

.app-header h1 {
  margin-bottom: 0.5rem;
}


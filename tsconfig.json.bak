{"compilerOptions": {"target": "ES2020", "lib": ["ES2020", "DOM"], "module": "commonjs", "moduleResolution": "node", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "declaration": true, "outDir": "./dist", "rootDir": "./src", "resolveJsonModule": true, "types": ["jest", "node"]}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.test.tsx"]}

> bridge-game@0.0.1 build
> webpack --config webpack.config.prod.js

assets by status 329 KiB [cached] 41 assets
runtime modules 972 bytes 5 modules
modules by path ./src/ 161 KiB
  modules by path ./src/components/ 103 KiB 22 modules
  modules by path ./src/*.css 8.29 KiB 4 modules
  modules by path ./src/lib/bridge/*.ts 33.6 KiB 4 modules
  modules by path ./src/*.tsx 1.92 KiB 2 modules
  modules by path ./src/styles/*.css 8.44 KiB 2 modules
  + 1 module
modules by path ./node_modules/ 554 KiB
  modules by path ./node_modules/style-loader/dist/runtime/*.js 5.84 KiB 6 modules
  modules by path ./node_modules/react/ 18 KiB 4 modules
  modules by path ./node_modules/react-dom/ 512 KiB 4 modules
  modules by path ./node_modules/scheduler/ 10.1 KiB 2 modules
  modules by path ./node_modules/css-loader/dist/runtime/*.js 2.31 KiB 2 modules
  + 1 module

ERROR in /Users/<USER>/Desktop/MyProjects/Bridge/src/components/GameTable/PlayerPosition.tsx
./src/components/GameTable/PlayerPosition.tsx 48:31-36
[tsl] ERROR in /Users/<USER>/Desktop/MyProjects/Bridge/src/components/GameTable/PlayerPosition.tsx(48,32)
      TS2339: Property 'cards' does not exist on type 'PlayedCard[]'.
 @ ./src/components/GameTable/GameTable.tsx 47:41-68
 @ ./src/App.tsx 7:36-79
 @ ./src/index.tsx 12:30-46

ERROR in /Users/<USER>/Desktop/MyProjects/Bridge/src/components/GameTable/PlayerPosition.tsx
./src/components/GameTable/PlayerPosition.tsx 53:31-36
[tsl] ERROR in /Users/<USER>/Desktop/MyProjects/Bridge/src/components/GameTable/PlayerPosition.tsx(53,32)
      TS2339: Property 'cards' does not exist on type 'PlayedCard[]'.
 @ ./src/components/GameTable/GameTable.tsx 47:41-68
 @ ./src/App.tsx 7:36-79
 @ ./src/index.tsx 12:30-46

ERROR in /Users/<USER>/Desktop/MyProjects/Bridge/src/components/GameTable/PlayerPosition.tsx
./src/components/GameTable/PlayerPosition.tsx 53:42-43
[tsl] ERROR in /Users/<USER>/Desktop/MyProjects/Bridge/src/components/GameTable/PlayerPosition.tsx(53,43)
      TS7006: Parameter 'c' implicitly has an 'any' type.
 @ ./src/components/GameTable/GameTable.tsx 47:41-68
 @ ./src/App.tsx 7:36-79
 @ ./src/index.tsx 12:30-46

3 errors have detailed information that is not shown.
Use 'stats.errorDetails: true' resp. '--stats-error-details' to show it.

webpack 5.99.9 compiled with 3 errors in 3188 ms

#!/bin/bash

echo "🔍 VERIFICATION SCRIPT: East Player Fix"
echo "======================================"

# Function to check compilation status
check_compilation() {
    echo "📋 Checking TypeScript compilation..."
    npm run build > build_output.log 2>&1
    if [ $? -eq 0 ]; then
        echo "✅ Build: SUCCESS"
        return 0
    else
        echo "❌ Build: FAILED"
        echo "Errors:"
        grep -E "ERROR|error" build_output.log | head -5
        return 1
    fi
}

# Function to check for debug code
check_debug_code() {
    echo "🔍 Checking for debug code..."
    if grep -q "EAST.*GUARANTEED\|guaranteed-east-fallback\|red.*background" src/components/GameTable/PlayerPosition.tsx; then
        echo "❌ Debug code still present"
        return 1
    else
        echo "✅ No debug code found"
        return 0
    fi
}

# Function to check TypeScript errors
check_typescript_errors() {
    echo "🔍 Checking for TypeScript errors..."
    if grep -q "TS2367.*overlap" build_output.log; then
        echo "❌ TypeScript overlap error still present"
        return 1
    else
        echo "✅ No TypeScript overlap errors"
        return 0
    fi
}

# Function to check component structure
check_component_structure() {
    echo "🔍 Checking component structure..."
    # Check for malformed code like "}  if (position === Position.EAST) {"
    if grep -q "}.*if.*position.*Position\.EAST" src/components/GameTable/PlayerPosition.tsx; then
        echo "❌ Malformed debug code found"
        return 1
    else
        echo "✅ Component structure looks good"
        return 0
    fi
}

echo "🚀 Starting verification..."
echo ""

echo "BEFORE FIX:"
echo "-----------"
check_compilation
check_debug_code
check_component_structure
echo ""

# Store results
BEFORE_BUILD=$?

echo "📝 Fix will be applied now..."
echo ""

# The fix will be applied here by the main script

echo "AFTER FIX:"
echo "----------"
check_compilation
BUILD_RESULT=$?
check_debug_code
DEBUG_RESULT=$?
check_typescript_errors
TS_RESULT=$?
check_component_structure
STRUCTURE_RESULT=$?

echo ""
echo "📊 SUMMARY:"
echo "==========="
if [ $BUILD_RESULT -eq 0 ] && [ $DEBUG_RESULT -eq 0 ] && [ $TS_RESULT -eq 0 ] && [ $STRUCTURE_RESULT -eq 0 ]; then
    echo "🎉 ALL CHECKS PASSED! Fix deployed successfully."
    exit 0
else
    echo "❌ Some checks failed. Fix needs more work."
    exit 1
fi

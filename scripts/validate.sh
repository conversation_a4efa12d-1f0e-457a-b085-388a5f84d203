#!/bin/bash

# Bridge Game Validation Script
# This script runs all validation checks before committing

set -e  # Exit on any error

echo "🔍 Starting validation checks..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    print_error "package.json not found. Please run this script from the project root."
    exit 1
fi

print_status "Checking project structure..."

# 1. TypeScript Compilation Check
print_status "Running TypeScript compilation check..."
if npm run build > /dev/null 2>&1; then
    print_success "TypeScript compilation passed"
else
    print_error "TypeScript compilation failed"
    echo "Running build to show errors:"
    npm run build
    exit 1
fi

# 2. Linting Check (if available)
print_status "Checking for linting..."
if npm run lint > /dev/null 2>&1; then
    print_success "Linting passed"
elif command -v eslint > /dev/null 2>&1; then
    print_warning "ESLint available but no npm lint script found"
else
    print_warning "No linting configured"
fi

# 3. Test Suite Check
print_status "Running test suite..."
TEST_OUTPUT=$(npm test -- --passWithNoTests --watchAll=false 2>&1)
TEST_EXIT_CODE=$?

if [ $TEST_EXIT_CODE -eq 0 ]; then
    print_success "All tests passed"
else
    # Count failed tests
    FAILED_TESTS=$(echo "$TEST_OUTPUT" | grep -o '[0-9]\+ failed' | head -1 | grep -o '[0-9]\+' || echo "0")
    PASSED_TESTS=$(echo "$TEST_OUTPUT" | grep -o '[0-9]\+ passed' | head -1 | grep -o '[0-9]\+' || echo "0")
    
    if [ "$FAILED_TESTS" -gt 0 ]; then
        print_warning "Tests failed: $FAILED_TESTS failed, $PASSED_TESTS passed"
        echo "Consider fixing failing tests before committing."
        echo "Use 'npm test' to see detailed test results."
    else
        print_success "Tests completed (no failures detected)"
    fi
fi

# 4. Check for common issues
print_status "Checking for common issues..."

# Check for console.log statements (excluding test files)
CONSOLE_LOGS=$(find src -name "*.ts" -o -name "*.tsx" | grep -v test | xargs grep -l "console\." || true)
if [ -n "$CONSOLE_LOGS" ]; then
    print_warning "Found console statements in:"
    echo "$CONSOLE_LOGS"
fi

# Check for TODO/FIXME comments
TODOS=$(find src -name "*.ts" -o -name "*.tsx" | xargs grep -n "TODO\|FIXME" || true)
if [ -n "$TODOS" ]; then
    print_warning "Found TODO/FIXME comments:"
    echo "$TODOS" | head -5
    if [ $(echo "$TODOS" | wc -l) -gt 5 ]; then
        echo "... and $(( $(echo "$TODOS" | wc -l) - 5 )) more"
    fi
fi

# 5. Bundle size check
print_status "Checking bundle size..."
if [ -d "dist" ]; then
    BUNDLE_SIZE=$(du -sh dist 2>/dev/null | cut -f1 || echo "unknown")
    print_success "Bundle size: $BUNDLE_SIZE"
else
    print_warning "No dist directory found"
fi

print_success "Validation completed!"
echo ""
echo "📋 Summary:"
echo "  ✅ TypeScript compilation: PASSED"
echo "  📝 Tests: $PASSED_TESTS passed, $FAILED_TESTS failed"
echo "  📦 Bundle size: ${BUNDLE_SIZE:-unknown}"
echo ""

if [ "$FAILED_TESTS" -gt 0 ]; then
    echo "⚠️  There are failing tests. Consider fixing them before committing."
    echo "   Use 'npm test' to see detailed results."
    exit 1
else
    echo "🎉 All critical checks passed! Ready to commit."
    exit 0
fi

#!/bin/bash

# Bridge Game Git Commit Helper
# This script validates the code and creates a proper git commit

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if commit message is provided
if [ $# -eq 0 ]; then
    print_error "Usage: $0 \"commit message\""
    echo "Example: $0 \"feat: implement card play validation\""
    echo ""
    echo "Commit message format suggestions:"
    echo "  feat: new feature"
    echo "  fix: bug fix"
    echo "  test: add or fix tests"
    echo "  refactor: code refactoring"
    echo "  docs: documentation changes"
    echo "  style: formatting changes"
    exit 1
fi

COMMIT_MESSAGE="$1"

print_status "Preparing to commit: \"$COMMIT_MESSAGE\""

# Run validation
print_status "Running pre-commit validation..."
if ./scripts/validate.sh; then
    print_success "Validation passed!"
else
    print_error "Validation failed. Please fix issues before committing."
    exit 1
fi

# Check git status
print_status "Checking git status..."
if [ -z "$(git status --porcelain)" ]; then
    print_error "No changes to commit"
    exit 1
fi

# Show what will be committed
print_status "Changes to be committed:"
git status --short

echo ""
read -p "Do you want to proceed with the commit? (y/N): " -n 1 -r
echo ""

if [[ $REPLY =~ ^[Yy]$ ]]; then
    # Add all changes
    print_status "Adding changes..."
    git add .
    
    # Create commit
    print_status "Creating commit..."
    git commit -m "$COMMIT_MESSAGE"
    
    print_success "Commit created successfully!"
    
    # Show the commit
    echo ""
    print_status "Commit details:"
    git log --oneline -1
    
    echo ""
    echo "🎉 Ready to push! Use 'git push' to push to remote repository."
else
    print_status "Commit cancelled."
    exit 1
fi
